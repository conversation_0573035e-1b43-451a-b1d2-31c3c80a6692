<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class NewsSeeder extends Seeder
{
    public function run()
    {
      $rows = [];

        for ($i = 0; $i < 50; $i++) {
            $rows[] = [
                'title'      => '标题 ' . Str::random(5),
                'msg'        => '消息 ' . Str::random(10),
                'author'     => '作者 ' . Str::random(6),
                'content'    => '内容 ' . Str::random(50),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        DB::table('news')->insert($rows);
    }
}
