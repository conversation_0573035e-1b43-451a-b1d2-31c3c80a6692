<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('users')) {
            return;
        }
        Schema::create('users', function (Blueprint $table) {
            $table->comment('用户');
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('city');
            $table->string('country');
            $table->string('is_message', 11)->comment('是否接受消息 1:接受 0:不接受');
            $table->integer('is_auth')->comment('是否授权 1:授权 0:未授权');
            $table->integer('status')->default(1)->comment(' 1:启用 0:禁用');
            $table->rememberToken();
            $table->softDeletes();
            $table->timestamps();
            $table->integer('profile_id')->nullable()->comment('平台用户id');
            $table->string('membership_card_no')->nullable()->comment('会员id');
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->id();
            $table->string('email');
            $table->string('token');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
