<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('hotel_brand')) {
            return;
        }
        Schema::create('hotel_image', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable()->comment('图片名称');
            $table->string('hotel_code')->nullable()->comment("酒店code");
            $table->integer('type')->nullable()->comment("1酒店类型图片 2房型类型图片 3品牌图片");
            $table->string('room_code')->nullable()->comment('房型code');
            $table->string('brand_code')->nullable()->comment('品牌code');
            $table->integer('is_default')->nullable()->comment('是否默认 0否 1是');
            $table->string('url')->nullable()->comment('图片地址');
            $table->string('room_name_en')->nullable()->comment('房型名称(英文)');
            $table->text('remark_cn')->nullable()->comment('描述(中文)');
            $table->text('remark_en')->nullable()->comment('描述(英文)');
            $table->integer('order_num')->nullable()->comment('排序');
            $table->string('source_url')->nullable()->comment('图片来源地址');
            $table->string('name_en')->nullable()->comment('图片英文名称');
            $table->integer('is_hidden')->nullable()->default(0)->comment('图片是否隐藏 0否 1是');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_image');
    }
};
