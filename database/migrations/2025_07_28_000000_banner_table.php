<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('banner')) {
            return;
        }
        Schema::create('banner', function (Blueprint $table) {
            $table->id();
            $table->integer('type')->nullable()->comment('banner类型');
            $table->string('title')->nullable()->comment('标题');
            $table->string('url')->nullable()->comment('跳转地址');
            $table->string('button_text')->nullable()->comment('按钮文字');
            $table->integer('status')->nullable()->comment('状态1:上线，0:下线');
            $table->string('img_url')->nullable()->comment('图片地址');
            $table->text('order_num')->nullable()->comment('排序');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banner');
    }
};
