<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateNewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('news')) {
            return;
        }
        Schema::create('news', function (Blueprint $table) {
            $table->comment('新闻动态');
            $table->increments('id')->comment('id');
            $table->string('title')->nullable()->comment('新闻标题');
            $table->string('msg')->nullable()->comment('新闻描述');
            $table->text('url')->nullable()->comment('新闻外部链接');
            $table->string('image')->nullable()->comment('新闻照片');
            $table->string('author')->nullable()->comment('作者');
            $table->text('content')->nullable()->comment('内容');
            $table->integer('status')->nullable()->default(1)->comment('状态');
            $table->integer('sort')->nullable()->default(0)->comment('排序');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('news');
    }
}
