<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('hotel_dining')) {
            return;
        }
        Schema::create('hotel_dining', function (Blueprint $table) {
            $table->comment('餐厅');
            $table->bigIncrements('id');
            $table->string('brand_code')->nullable()->index('brand_code')->comment('品牌代码');
            $table->string('hotel_code')->nullable()->index('hotel_code')->comment('酒店代码');
            $table->string('name')->nullable()->comment('餐厅名称');
            $table->string('name_en')->nullable()->comment('餐厅名称en');
            $table->string('sub_title')->nullable()->comment('餐厅子标题');
            $table->string('sub_title_en')->nullable()->comment('餐厅子标题en');
            $table->json('images')->nullable()->comment('餐厅照片');
            $table->string('cuisine')->nullable()->comment('餐厅特色');
            $table->string('website')->nullable()->comment('餐厅官网');
            $table->string('phone')->nullable()->comment('餐厅联系电话');
            $table->string('reservation_url', 500)->nullable()->comment('餐厅预约链接');
            $table->string('menu_1_url', 1000)->nullable()->comment('菜单图片1');
            $table->string('menu_2_url', 1000)->nullable()->comment('菜单图片2');
            $table->string('start_time')->nullable()->comment('营业开始时间');
            $table->string('end_time')->nullable()->comment('营业开始时间');
            $table->integer('dining_id')->nullable()->comment('餐厅id');
            $table->string('version_no')->nullable()->comment('同步/跟踪更新记录的版本号');
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_dining');

    }
};
