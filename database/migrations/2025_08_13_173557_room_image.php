<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('room', 'images')) {
            Schema::table('room', function (Blueprint $table) {
                $table->json('images')->nullable()->comment('图片');
            });
        }
        if (!Schema::hasColumn('hotel_brand', 'images')) {
            Schema::table('hotel_brand', function (Blueprint $table) {
                $table->json('images')->nullable()->comment('图片');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('room', 'images')) {
            Schema::table('room', function (Blueprint $table) {
                $table->dropColumn('images');
            });
        }
        if (Schema::hasColumn('hotel_brand', 'images')) {
            Schema::table('hotel_brand', function (Blueprint $table) {
                $table->dropColumn('images');
            });
        }
    }
};
