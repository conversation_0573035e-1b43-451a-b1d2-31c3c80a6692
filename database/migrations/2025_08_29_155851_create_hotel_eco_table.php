<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('hotel_eco')) {
            return;
        }
        Schema::create('hotel_eco', function (Blueprint $table) {
            $table->comment('生态证书');
            $table->increments('id')->comment('id');
            $table->string('name')->nullable()->comment('证书名称');
            $table->integer('original_id')->nullable()->comment('原id');
            $table->string('name_en')->nullable()->comment('证书名称英文');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_eco');
    }
};
