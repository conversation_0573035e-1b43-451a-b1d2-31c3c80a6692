<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        if (Schema::hasTable('hotel_offers')) {
            return;
        }
        Schema::create('hotel_offers', function (Blueprint $table) {
            $table->comment('酒店活动');
            $table->bigIncrements('id');
            $table->integer('external_id')->nullable()->comment('原始id');
            $table->string('title')->comment('优惠标题');
            $table->string('title_cn')->nullable()->comment('优惠标题en');
            $table->json('hotels')->nullable()->comment('酒店');
            $table->string('url')->unique()->comment('跳转链接');
            $table->json('related_rate_codes')->nullable()->comment('相关费率');
            $table->unsignedBigInteger('start_date')->comment('优惠开始时间');
            $table->unsignedBigInteger('end_date')->comment('优惠结束时间');
            $table->unsignedBigInteger('stay_start_date')->comment('入住开始日期');
            $table->unsignedBigInteger('stay_end_date')->comment('入住结束日期');
            $table->string('hotel_name')->comment('酒店名称');
            $table->string('hotel_name_en')->nullable()->comment('酒店名称en');
            $table->string('rate_code1')->nullable()->comment('房价代码1');
            $table->string('rate_code2')->nullable()->comment('房价代码2');
            $table->string('rate_code3')->nullable()->comment('房价代码3');
            $table->string('rate_code4')->nullable()->comment('房价代码4');
            $table->string('rate_checked')->nullable()->comment('房价核对状态');
            $table->boolean('featured')->default(false)->comment('是否推荐');
            $table->string('offer_types')->nullable()->comment('优惠类型');
            $table->string('offer_types_en')->nullable()->comment('优惠类型 引文');
            $table->string('promo_type')->nullable()->comment('优惠类型');
            $table->string('promo_code')->nullable()->comment('优惠码');
            $table->text('description')->nullable()->comment('优惠描述');
            $table->text('description_en')->nullable()->comment('优惠描述en');
            $table->text('distribution')->nullable()->comment('酒店分布');
            $table->text('highlights')->nullable()->comment('特色亮点');
            $table->text('offerIncludes')->nullable()->comment('优惠包含');
            $table->text('offerIncludes_en')->nullable()->comment('优惠包含en');
            $table->text('terms_conditions')->nullable()->comment('条款与条件');
            $table->text('terms_conditions_en')->nullable()->comment('条款与条件en');
            $table->text('taxes')->nullable()->comment('税费说明');
            $table->text('taxes_en')->nullable()->comment('税费说明en');
            $table->json('images')->nullable()->comment('图片集');
            $table->string('image')->nullable()->comment('图片');
            $table->string('members_only')->nullable()->comment('是否仅限会员');
            $table->string('percentage_off')->nullable()->comment('折扣百分比');
            $table->timestamps();
        });

    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_offers');
    }
};
