<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('hotel_brand')) {
            return;
        }
        Schema::create('hotel_brand', function (Blueprint $table) {
            $table->comment('酒店品牌');
            $table->bigIncrements('id');
            $table->string('brand_code')->nullable()->unique('brand_code')->comment('酒店品牌id');
            $table->string('code')->nullable()->comment('酒店品牌code');
            $table->string('version_no')->nullable()->comment('版本号');
            $table->string('name')->nullable()->comment('品牌短名称');
            $table->string('name_en')->nullable()->comment('原始短名称');
            $table->string('title')->nullable()->comment('酒店品牌的全称');
            $table->string('title_en')->nullable()->comment('原始酒店品牌的全称');
            $table->text('description')->nullable()->comment('品牌描述');
            $table->text('distribution')->nullable()->comment('酒店分布');
            $table->text('highlights')->nullable()->comment('特色亮点');
            $table->string('country_code_en')->nullable()->comment('从trust下载的原始数据');
            $table->string('country_code')->nullable();
            $table->string('country_name')->nullable()->comment('国家');
            $table->string('city_code_en')->nullable()->comment('从trust下载的原始数据');
            $table->string('city_code')->nullable();
            $table->string('city_name')->nullable()->comment('从trust下载的原始数据');
            $table->string('logo')->nullable()->comment('原始酒店品牌的全称');
            $table->string('logo_svg')->nullable()->comment('logoSvg');
            $table->string('card_logo')->nullable()->comment('用于卡片或缩略图中的标志，通常尺寸较小且简单');
            $table->integer('status')->nullable()->default(1)->comment('是否上线 0:下线 1:上线');
            $table->json('extend')->comment('扩展字段');
            $table->softDeletes();
            $table->timestamps();
            $table->integer('location_id')->nullable();
            $table->json('images')->nullable()->comment('图片');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_brand');
    }
};
