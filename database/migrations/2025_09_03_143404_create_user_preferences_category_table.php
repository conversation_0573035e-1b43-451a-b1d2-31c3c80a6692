<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserPreferencesCategoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('user_preferences_category')) {
            return;
        }
        Schema::create('user_preferences_category', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable()->comment('分类名称');
            $table->string('name_en')->nullable()->comment('分类名称英文');
            $table->string('status')->default('1')->nullable()->comment('1:正常，2:禁用');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_preferences_category');
    }
}
