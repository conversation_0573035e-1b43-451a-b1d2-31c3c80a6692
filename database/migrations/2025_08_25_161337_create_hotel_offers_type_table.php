<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateHotelOffersTypeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('hotel_offers_type')) {
            return;
        }
        Schema::create('hotel_offers_type', function (Blueprint $table) {
            $table->comment('优惠类型');
            $table->increments('id')->comment('id');
            $table->string('name')->nullable()->comment('优惠名称');
            $table->string('description')->nullable()->comment('优惠描述');
            $table->string('name_en')->nullable()->comment('优惠名称英文');
            $table->string('image')->nullable()->comment('图片');
            $table->integer('status')->nullable()->default(1)->comment('状态');
            $table->integer('sort')->nullable()->default(0)->comment('排序');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hotel_offers_type');
    }
}
