<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('continent', 'image')) {
            Schema::table('continent', function (Blueprint $table) {
                $table->string('image', 255)->comment('图片');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('continent', 'image')) {
            Schema::table('continent', function (Blueprint $table) {
                $table->dropColumn('image');
            });
        }
    }
};
