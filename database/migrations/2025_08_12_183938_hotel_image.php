<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('hotel', 'images')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->json('images')->nullable()->comment('图片');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('hotel', 'images')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->dropColumn('images');
            });
        }

    }
};
