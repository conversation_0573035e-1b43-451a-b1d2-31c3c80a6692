<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateBannerTypeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('banner_type')) {
            return;
        }
        Schema::create('banner_type', function (Blueprint $table) {
            $table->comment('轮播类型');
            $table->increments('id')->comment('id');
            $table->string('title')->nullable()->comment('轮播图类型');
            $table->string('code')->nullable()->unique('code')->comment('轮播标识');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banner_type');
    }
}
