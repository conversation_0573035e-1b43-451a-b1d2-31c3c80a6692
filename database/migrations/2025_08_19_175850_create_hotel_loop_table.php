<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateHotelLoopTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('hotel_loop')) {
            return;
        }
        Schema::create('hotel_loop', function (Blueprint $table) {
            $table->comment('酒店系列');
            $table->increments('id')->comment('id');
            $table->string('name')->nullable()->comment('系列名称');
            $table->integer('original_id')->nullable()->comment('原id');
            $table->string('name_en')->nullable()->comment('系列名称英文');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hotel_loop');
    }
}
