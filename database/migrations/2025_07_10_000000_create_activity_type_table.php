<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('activity_type')) {
            return;
        }
        Schema::create('activity_type', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable()->comment('中文名称');
            $table->string('name_en')->nullable()->comment('英文名称');
            $table->string('status')->nullable()->comment('1:正常，0:禁用');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_type');
    }
};
