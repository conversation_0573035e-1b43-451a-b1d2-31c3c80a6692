<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserCollectTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('user_collect')) {
            return;
        }
        Schema::create('user_collect', function (Blueprint $table) {
            $table->comment('用户收藏');
            $table->increments('id')->comment('id');
            $table->string('type')->nullable()->index('type')->comment('收藏类型  hotel 酒店 activity活动');
            $table->string('collect')->nullable()->comment('收藏id');
            $table->integer('user_id')->nullable()->index('user_id')->comment('用户');
            $table->string('status')->nullable()->comment('1:正常，2:禁用');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_collect');
    }
}
