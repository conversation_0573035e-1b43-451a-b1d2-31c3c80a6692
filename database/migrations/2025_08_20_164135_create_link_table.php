<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLinkTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('link')) {
            return;
        }
        Schema::create('link', function (Blueprint $table) {
            $table->comment('快捷方式');
            $table->increments('id')->comment('id');
            $table->string('type')->nullable()->comment('所属类目');
            $table->string('name')->nullable()->comment('名称');
            $table->string('url')->nullable()->comment('链接');
            $table->integer('status')->nullable()->comment('状态1:上线，0:下线');
            $table->integer('order_num')->nullable()->comment('排序');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('link');
    }
}
