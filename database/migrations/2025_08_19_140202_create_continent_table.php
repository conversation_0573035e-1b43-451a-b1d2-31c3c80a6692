<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateContinentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('continent')) {
            return;
        }
        Schema::create('continent', function (Blueprint $table) {
            $table->comment('大陆');
            $table->bigIncrements('id');
            $table->string('name')->nullable()->comment('名称');
            $table->string('name_en')->nullable()->comment('名称英文');
            $table->string('description')->nullable()->comment('描述');
            $table->softDeletes();
            $table->timestamps();
            $table->string('image')->comment('图片');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('continent');
    }
}
