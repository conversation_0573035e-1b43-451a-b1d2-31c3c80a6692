<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('country')) {
            return;
        }
        Schema::create('country', function (Blueprint $table) {
            $table->comment('国家');
            $table->string('country_code', 100)->comment('国家');
            $table->string('country_name', 100)->nullable();
            $table->string('country_name_en', 100)->nullable();
            $table->string('country_name_pinyin', 100)->nullable();
            $table->string('continent_name_en', 100)->nullable();
            $table->string('brief_en', 1000)->nullable();
            $table->string('brief', 1000)->nullable();
            $table->string('detail_en', 2000)->nullable();
            $table->string('detail', 2000)->nullable();
            $table->string('introducing', 1000)->nullable();
            $table->string('introducing_en', 1000)->nullable();
            $table->string('image_url', 500)->nullable();
            $table->string('status', 20)->nullable();
            $table->string('country_url', 200)->nullable();
            $table->string('country_link', 100)->nullable();
            $table->softDeletes();
            $table->timestamps();
            $table->integer('id', true);
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('country');
    }
};
