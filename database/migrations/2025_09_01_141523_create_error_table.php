<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateErrorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('error')) {
            return;
        }
        Schema::create('error', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('title')->nullable()->comment('错误返回');
            $table->string('title_en')->nullable()->comment('错误返回(英文)');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('error');
    }
}
