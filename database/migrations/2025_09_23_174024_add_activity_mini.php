<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('activity', function (Blueprint $table) {
            $table->string('mini_type', 255)->nullable()->default('')->comment('小程序类型');
            $table->string('mini_id', 255)->nullable()->default('')->comment('跳转内容');
            $table->integer('mini_switch')->nullable()->default(0)->comment('小程开关');
        });
        Schema::table('banner', function (Blueprint $table) {
            $table->string('mini_type', 255)->nullable()->default('')->comment('小程序类型');
            $table->string('mini_id', 255)->nullable()->default('')->comment('跳转内容');
            $table->integer('mini_switch')->nullable()->default(0)->comment('小程开关');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('activity', function (Blueprint $table) {
            $table->dropColumn('mini_type');
            $table->dropColumn('mini_id');
            $table->dropColumn('mini_switch');
        });
        Schema::table('banner', function (Blueprint $table) {
            $table->dropColumn('mini_type');
            $table->dropColumn('mini_id');
            $table->dropColumn('mini_switch');
        });
    }
};
