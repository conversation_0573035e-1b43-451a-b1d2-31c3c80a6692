<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLinkTypeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('link_type')) {
            return;
        }
        Schema::create('link_type', function (Blueprint $table) {
            $table->comment('快捷类目');
            $table->increments('id')->comment('id');
            $table->string('name')->nullable()->comment('分类名称');
            $table->integer('status')->nullable()->default(1)->comment('状态1:上线，0:下线');
            $table->text('order_num')->nullable()->comment('排序');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('link_type');
    }
}
