<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('resv_base')) {
            return;
        }
        Schema::create('resv_base', function (Blueprint $table) {
            $table->id();
            // 会员信息
            $table->string('membership_card_no', 50)->nullable()->comment('会员卡号');
            $table->string('membership_level', 50)->nullable()->comment('会员等级');
            // 订单信息
            $table->string('reservation_id', 50)->nullable()->comment('预定ID');
            $table->string('confirmation_number', 50)->nullable()->comment('确认号');
            $table->string('itinerary_number', 50)->nullable()->comment('行程号');
            $table->string('status', 50)->nullable()->comment('状态');

            // 基本预订信息
            $table->unsignedBigInteger('hotel_id')->nullable()->comment('酒店synxis_hotel_id');
            $table->unsignedBigInteger('chain_id')->nullable()->comment('酒店synxis_chain_id');
            $table->date('check_in_date')->nullable()->comment('开始时间');
            $table->date('check_out_date')->nullable()->comment('结束时间');
            $table->unsignedTinyInteger('num_rooms')->nullable()->comment('房间数');
            $table->unsignedTinyInteger('adults')->nullable()->comment('成人数');
            $table->unsignedTinyInteger('children')->nullable()->default(0)->comment('儿童数');
            $table->json('children_ages')->nullable()->comment('儿童年龄');
            // 房型和价格信息
            $table->string('room_code', 50)->nullable()->comment('房型代码');
            $table->string('rate_code', 50)->nullable()->comment('房价代码');
            // 主要客人信息
            $table->string('first_name', 50)->nullable()->comment('名');
            $table->string('last_name', 50)->nullable()->comment('姓');
            $table->string('email', 100)->nullable()->comment('邮件');
            $table->string('phone', 20)->nullable()->comment('手机号');
            $table->string('address_line1')->nullable()->comment('详细地址');
            $table->string('address_city', 100)->nullable()->comment('城市');
            $table->string('address_state', 100)->nullable()->comment('区域');
            $table->string('address_country', 10)->nullable()->comment('国家');
            $table->string('address_postal_code', 20)->nullable()->comment('邮编');
            $table->string('card_type')->nullable()->comment('信用卡类别');
            $table->string('card_number', 20)->nullable()->comment('卡号');
            $table->string('card_holder', 100)->nullable()->comment('姓名');
            $table->string('expiry_month', 2)->nullable()->comment('月份');
            $table->string('expiry_year', 4)->nullable()->comment('年份');

            $table->timestamps();
        });

        Schema::table('hotel', function (Blueprint $table) {
            // 添加会员等级
            $table->text('hotel_desc_en')->nullable()->comment('酒店描述英文');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resv_base');
    }
};
