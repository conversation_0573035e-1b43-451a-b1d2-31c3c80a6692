<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('hotel', 'destinations')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->json('destinations')->nullable()->comment('目的地');
            });
        }
        if (!Schema::hasColumn('hotel', 'interests')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->json('interests')->nullable()->comment('兴趣');
            });
        }
        if (!Schema::hasColumn('hotel', 'eco_certificates')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->json('eco_certificates')->nullable()->comment('生态证书');
            });
        }
        if (!Schema::hasColumn('hotel', 'new_hotel')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->tinyInteger('new_hotel')->default(0)->comment('是否新酒店');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('hotel', 'destinations')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->dropColumn('destinations');
            });
        }
        if (Schema::hasColumn('hotel', 'interests')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->dropColumn('interests');
            });
        }
        if (Schema::hasColumn('hotel', 'eco_certificates')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->dropColumn('eco_certificates');
            });
        }
        if (Schema::hasColumn('hotel', 'new_hotel')) {
            Schema::table('hotel', function (Blueprint $table) {
                $table->dropColumn('new_hotel');
            });
        }
    }
};
