<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('hot')) {
            return;
        }
        Schema::create('hot', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('keyword')->nullable()->comment('热门关键词');
            $table->string('hotel')->nullable()->comment('热门酒店');
            $table->string('city')->nullable()->comment('热门城市');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hot');
    }
}
