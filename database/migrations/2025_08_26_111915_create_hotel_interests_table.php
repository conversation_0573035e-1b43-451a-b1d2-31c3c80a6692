<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateHotelInterestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('hotel_interests')) {
            return;
        }
        Schema::create('hotel_interests', function (Blueprint $table) {
            $table->comment('酒店亮点 兴趣');
            $table->increments('id')->comment('id');
            $table->string('name')->nullable()->comment('兴趣名称');
            $table->integer('original_id')->nullable()->comment('原id');
            $table->string('name_en')->nullable()->comment('兴趣名称英文');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hotel_interests');
    }
}
