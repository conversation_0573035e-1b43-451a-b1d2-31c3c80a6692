<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateWechatHighlightsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('wechat_highlights')) {
            return;
        }
        Schema::create('wechat_highlights', function (Blueprint $table) {
            $table->comment('微信精选');
            $table->increments('id')->comment('id');
            $table->string('title')->nullable()->comment('新闻标题');
            $table->string('msg')->nullable()->comment('新闻描述');
            $table->string('author')->nullable()->comment('作者');
            $table->string('image')->nullable()->comment('文章缩略图');
            $table->text('content')->nullable()->comment('内容');
            $table->text('url')->nullable()->comment('新闻外部链接');
            $table->integer('status')->nullable()->default(1)->comment('状态');
            $table->integer('sort')->nullable()->default(0)->comment('排序');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wechat_highlights');
    }
}
