<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateHotelRomsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('hotel_roms')) {
            return;
        }
        Schema::create('hotel_roms', function (Blueprint $table) {
            $table->comment('酒店设施');
            $table->increments('id')->comment('id');
            $table->string('name')->nullable()->comment('系列名称');
            $table->integer('original_id')->nullable()->comment('原id');
            $table->string('name_en')->nullable()->comment('系列名称英文');
            $table->string('image')->nullable()->comment('图标');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hotel_roms');
    }
}
