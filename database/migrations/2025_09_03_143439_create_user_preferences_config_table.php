<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserPreferencesConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('user_preferences_config')) {
            return;
        }
        Schema::create('user_preferences_config', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('category_id')->nullable()->comment('分类id');
            $table->string('name')->nullable()->comment('分类名称');
            $table->integer('original_id')->nullable()->comment('原id');
            $table->string('name_en')->nullable()->comment('分类名称英文');
            $table->string('group')->nullable()->comment('分类');
            $table->string('ows_group')->nullable()->comment('ows分类');
            $table->string('yes_code')->nullable()->comment('yes_code');
            $table->string('status')->nullable()->comment('1:正常，2:禁用');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_preferences_config');
    }
}
