<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('hotel')) {
            return;
        }
        Schema::create('hotel', function (Blueprint $table) {
            $table->comment('酒店');
            $table->bigIncrements('id');
            $table->string('version_no')->nullable()->comment('同步/跟踪更新记录的版本号');
            $table->string('hotelcode')->index('hotelcode')->comment('酒店原始id');
            $table->json('hotel_type')->nullable()->comment('酒店类型');
            $table->string('code')->comment('唯一的酒店代码（短标识符）');
            $table->json('region')->nullable()->comment('区域大陆');
            $table->string('country_code')->nullable()->comment('位置参考ID');
            $table->json('city_code')->nullable()->comment('酒店所在城市');
            $table->string('hotel_name')->nullable()->comment('酒店名称');
            $table->string('hotel_name_en')->nullable()->comment('酒店原始名称');
            $table->string('hotel_name_pinyin')->nullable()->comment('酒店名称拼音');
            $table->string('gradient_color')->nullable()->comment('酒店显示组件的UI特定渐变颜色。');
            $table->string('primary_currency')->nullable()->comment('酒店货币代码');
            $table->integer('new_hotel_start_date')->nullable()->comment('酒店可见性“开始”日期（用于显示新酒店）。');
            $table->integer('new_hotel_end_date')->nullable()->comment('酒店能见度“结束”日期。');
            $table->string('synxis_hotel_id', 11)->nullable()->comment('用于Synxis PMS集成的酒店ID');
            $table->string('synxis_chain_id', 11)->nullable()->comment('Synxis集成的链ID。');
            $table->string('sabre_tax_setup')->nullable()->comment('Sabre/Synxis上的税务结构标签（例如，包含税的金额）');
            $table->string('online_status')->nullable()->comment('在线状态');
            $table->string('address')->nullable()->comment('地址');
            $table->string('address_en')->nullable()->comment('地址（英文)');
            $table->string('postal_code')->nullable()->comment('邮编');
            $table->string('postal_code_en')->nullable()->comment('邮编（英文)');
            $table->string('phone')->nullable()->comment('电话');
            $table->string('fax')->nullable()->comment('传真');
            $table->string('email')->nullable()->comment('邮箱');
            $table->string('zero_rate_code')->nullable()->comment('保留用于内部计费/费率逻辑');
            $table->string('phone_country_code')->nullable()->comment('国家/地区拨号代码（例如，+254、968）。');
            $table->string('phone_area_code')->nullable()->comment('本地拨号的区号/STD代码。');
            $table->integer('is_up_pay')->default(0)->comment('是否支持银联付款，1：支持，0：不支持');
            $table->text('offered_service')->nullable()->comment('酒店特征代码');
            $table->string('longitude')->nullable()->comment('经度');
            $table->string('latitude')->nullable()->comment('纬度');
            $table->text('hotel_desc')->nullable()->comment('酒店描述');
            $table->integer('condition_code')->nullable()->comment('酒店情况：0营业中、1装修中、2尚未开业');
            $table->text('remark')->nullable()->comment('酒店情况描述');
            $table->decimal('floor_rate')->nullable()->comment('默认最低价格或价格阈值（供内部使用）。');
            $table->text('highlight')->nullable()->comment('酒店高亮信息（酒家简介)');
            $table->string('award_winning_dinning')->nullable()->comment('获奖餐厅 Y:是获奖餐厅');
            $table->string('website_url')->nullable()->comment('酒店官网地址');
            $table->text('unique_quelities_en')->nullable();
            $table->text('unique_quelities')->nullable();
            $table->text('detail_en')->nullable();
            $table->text('detail')->nullable();
            $table->text('brief_en')->nullable();
            $table->text('brief')->nullable();
            $table->integer('rooms_total_number')->nullable();
            $table->integer('floors_number')->nullable();
            $table->integer('restaurants_number')->nullable();
            $table->integer('bars_number')->nullable();
            $table->integer('hasPools')->nullable();
            $table->integer('michelinStarredRestaurants')->nullable();
            $table->dateTime('edit_time')->nullable();
            $table->integer('trust_online')->nullable();
            $table->string('hotel_link')->nullable();
            $table->string('hotel_alias_en')->nullable();
            $table->string('crs_api')->nullable();
            $table->string('check_in_after')->nullable();
            $table->string('check_out_before')->nullable();
            $table->string('brand_code')->nullable()->comment('品牌代码');
            $table->string('brand_nameEn')->nullable()->comment('品牌名称(英文)');
            $table->string('brand_name')->nullable()->comment('品牌名称');
            $table->string('max_childrenage')->nullable()->comment('最大儿童年龄');
            $table->string('open_date')->nullable();
            $table->json('extend')->comment('扩展字段');
            $table->softDeletes();
            $table->timestamps();
            $table->json('images')->nullable()->comment('图片数组');
            $table->json('destinations')->nullable()->comment('目的地');
            $table->json('interests')->nullable()->comment('酒店亮点');
            $table->json('eco_certificates')->nullable()->comment('生态证书');
            $table->json('feature')->nullable()->comment('酒店设施');
            $table->tinyInteger('new_hotel')->default(0)->comment('是否新酒店');
            $table->json('categories')->nullable()->comment('酒店系列');
            $table->json('neighborhood_tag')->nullable()->comment('社区标签');
            $table->string('url')->nullable()->comment('跳转链接');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel');
    }
};
