<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateBannerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('banner')) {
            return;
        }
        Schema::create('banner', function (Blueprint $table) {
            $table->comment('轮播图');
            $table->bigIncrements('id');
            $table->integer('type')->nullable()->comment('banner类型');
            $table->string('title')->nullable()->comment('标题');
            $table->string('img_url')->nullable()->comment('图片地址');
            $table->string('url')->nullable()->comment('跳转地址');
            $table->string('button_text')->nullable()->comment('按钮文字');
            $table->integer('hotel_id')->nullable()->comment('酒店id');
            $table->integer('status')->nullable()->comment('状态1:上线，0:下线');
            $table->text('order_num')->nullable()->comment('排序');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banner');
    }
}
