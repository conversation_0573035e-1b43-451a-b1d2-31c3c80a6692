# 批量预订处理接口使用示例

## 概述

系统提供了两个批量预订处理接口：

1. **SabreController**: `/api/v1/sabre/processBatchReservations` - 基础版本
2. **RoomController**: `/api/v1/room/processBatchReservations` - 增强版本（推荐前端使用）

## 修正后的请求示例

基于你提供的原始请求，这里是修正后的正确格式：

```json
[
    {
        "CRS_confirmationNumber": "100820CK000421",
        "itineraryNumber": "32446B0003254",
        "status": "modify",
        "hotelId": 100820,
        "chainId": "32446",
        "checkInDate": "2025-10-01",
        "checkOutDate": "2025-10-02",
        "numRooms": 1,
        "adults": 2,
        "children": 1,
        "childrenAges": [8],
        "roomCode": "1STE",
        "rateCode": "BAR",
        "primaryGuest": {
            "firstName": "张",
            "lastName": "三",
            "email": "<EMAIL>",
            "phone": "13800138000"
        },
        "payment": {
            "cardNumber": "****************",
            "cardHolder": "ZHANG SAN",
            "expiryMonth": 12,
            "expiryYear": 2025
        },
        "loyaltyNumber": "111",
        "sendConfirmationEmail": true
    },
    {
        "itineraryNumber": "32446B0003254",
        "status": "create",
        "hotelId": 100820,
        "chainId": "32446",
        "checkInDate": "2025-10-01",
        "checkOutDate": "2025-10-02",
        "numRooms": 1,
        "adults": 2,
        "children": 1,
        "childrenAges": [8],
        "roomCode": "1STE",
        "rateCode": "BAR",
        "primaryGuest": {
            "firstName": "张",
            "lastName": "三",
            "email": "<EMAIL>",
            "phone": "13800138000"
        },
        "payment": {
            "cardNumber": "****************",
            "cardHolder": "ZHANG SAN",
            "expiryMonth": 12,
            "expiryYear": 2025
        },
        "loyaltyNumber": "111",
        "sendConfirmationEmail": true
    }
]
```

## 主要修正点

1. **日期格式**: `"2025-10-"` → `"2025-10-02"`
2. **数据类型**: `"2"` → `2` (数字类型)
3. **移除null值**: 删除 `"promoCode": null`, `"specialRequests": null`

## JavaScript调用示例

```javascript
// 使用fetch调用RoomController接口
async function processBatchReservations(reservations) {
    try {
        const response = await fetch('/api/v1/room/processBatchReservations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + getAuthToken()
            },
            body: JSON.stringify(reservations)
        });

        const result = await response.json();
        
        if (result.success) {
            console.log('批量处理成功:', result.data);
            console.log(`成功: ${result.data.success_count}, 失败: ${result.data.failed_count}`);
            
            // 处理每个操作的结果
            result.data.operations.forEach((operation, index) => {
                if (operation.success) {
                    console.log(`操作 ${index + 1} (${operation.operation}) 成功:`, operation.confirmation_number);
                } else {
                    console.error(`操作 ${index + 1} (${operation.operation}) 失败:`, operation.error);
                }
            });
        } else {
            console.error('批量处理失败:', result.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 使用示例
const reservations = [
    {
        "status": "modify",
        "CRS_confirmationNumber": "100820CK000421",
        "hotelId": 100820,
        "checkInDate": "2025-10-01",
        "checkOutDate": "2025-10-02",
        "adults": 2,
        "children": 1,
        "childrenAges": [8],
        "roomCode": "1STE",
        "rateCode": "BAR",
        "primaryGuest": {
            "firstName": "张",
            "lastName": "三",
            "email": "<EMAIL>",
            "phone": "13800138000"
        },
        "payment": {
            "cardNumber": "****************",
            "cardHolder": "ZHANG SAN",
            "expiryMonth": 12,
            "expiryYear": 2025
        }
    }
];

processBatchReservations(reservations);
```

## PHP调用示例

```php
use Illuminate\Support\Facades\Http;

class ReservationService 
{
    public function processBatchReservations(array $reservations)
    {
        $response = Http::post('/api/v1/room/processBatchReservations', $reservations);
        
        if ($response->successful()) {
            $result = $response->json();
            
            if ($result['success']) {
                Log::info('批量处理成功', [
                    'itinerary_number' => $result['data']['itinerary_number'],
                    'success_count' => $result['data']['success_count'],
                    'failed_count' => $result['data']['failed_count']
                ]);
                
                return $result['data'];
            }
        }
        
        throw new Exception('批量处理失败: ' . $response->body());
    }
}
```

## 错误处理示例

```javascript
function handleBatchResult(result) {
    const { success_count, failed_count, operations } = result.data;
    
    if (failed_count > 0) {
        // 处理失败的操作
        const failedOperations = operations.filter(op => !op.success);
        
        failedOperations.forEach(operation => {
            switch (operation.status) {
                case 'create':
                    console.error(`创建订单失败: ${operation.error}`);
                    // 可以重试或提示用户
                    break;
                case 'modify':
                    console.error(`修改订单 ${operation.confirmation_number} 失败: ${operation.error}`);
                    break;
                case 'cancel':
                    console.error(`取消订单 ${operation.confirmation_number} 失败: ${operation.error}`);
                    break;
            }
        });
    }
    
    if (success_count > 0) {
        console.log(`成功处理 ${success_count} 个操作`);
        // 更新UI或跳转到成功页面
    }
}
```

## 常见错误及解决方案

1. **参数验证失败**: 检查必需字段和数据类型
2. **儿童年龄不匹配**: 确保childrenAges数组长度等于children数量
3. **日期格式错误**: 使用YYYY-MM-DD格式
4. **确认号缺失**: modify和cancel操作必须提供CRS_confirmationNumber
5. **全部操作失败**: 检查网络连接和Sabre服务状态
