# 统一预订API实现文档

## 概述

根据前端需求，现已实现统一的预订处理接口，通过添加 `status` 字段来区分操作类型（create、modify、cancel），使数据格式在创建、修改、取消操作之间保持一致。

## 主要改进

### 1. 统一数据格式
- **创建预订**：使用统一的房间数组格式
- **修改预订**：使用相同的数据结构，额外添加确认号
- **取消预订**：使用相同的基础数据格式

### 2. 新增统一接口
- **端点**: `POST /api/v1/sabre/reservation`
- **操作类型**: 通过 `status` 字段区分：`create`, `modify`, `cancel`
- **向后兼容**: 保留所有原有的独立接口

## API 使用方式

### 单房间创建
```json
{
  "status": "create",
  "hotelId": 12345,
  "checkInDate": "2024-12-15",
  "checkOutDate": "2024-12-17",
  "adults": 2,
  "children": 1,
  "childrenAges": [10],
  "roomCode": "STD",
  "rateCode": "MEMBER_RATE",
  "primaryGuest": { ... },
  "payment": { ... },
  "loyaltyNumber": "GHA123456789"
}
```

### 多房间创建
```json
{
  "status": "create",
  "hotelId": 12345,
  "checkInDate": "2024-12-15",
  "checkOutDate": "2024-12-18",
  "rooms": [
    {
      "roomCode": "STD",
      "rateCode": "MEMBER_RATE",
      "adults": 2,
      "children": 2,
      "childrenAges": [8, 12]
    },
    {
      "roomCode": "DLX",
      "rateCode": "PROMO_RATE",
      "adults": 2,
      "children": 0,
      "childrenAges": []
    }
  ],
  "primaryGuest": { ... },
  "payment": { ... },
  "loyaltyNumber": "GHA987654321"
}
```

### 单房间修改
```json
{
  "status": "modify",
  "confirmationNumber": "CONF123456",
  "hotelId": 12345,
  "checkInDate": "2024-12-15",
  "checkOutDate": "2024-12-17",
  "adults": 2,
  "children": 0,
  "roomCode": "DLX",
  "rateCode": "MEMBER_RATE",
  "primaryGuest": { ... },
  "payment": { ... }
}
```

### 多房间修改
```json
{
  "status": "modify",
  "itineraryNumber": "ITN987654321",
  "hotelId": 12345,
  "checkInDate": "2024-12-15",
  "checkOutDate": "2024-12-18",
  "rooms": [
    {
      "roomCode": "STD",
      "rateCode": "MEMBER_RATE",
      "adults": 2,
      "children": 1,
      "childrenAges": [8],
      "confirmationNumber": "ROOM001"
    },
    {
      "roomCode": "DLX",
      "rateCode": "PROMO_RATE",
      "adults": 2,
      "children": 0,
      "childrenAges": [],
      "confirmationNumber": "ROOM002"
    }
  ],
  "primaryGuest": { ... },
  "payment": { ... }
}
```

### 多房间修改（支持房间级别操作）
```json
{
  "status": "modify",
  "itineraryNumber": "ITN987654321",
  "hotelId": 12345,
  "checkInDate": "2024-12-15",
  "checkOutDate": "2024-12-18",
  "rooms": [
    {
      "status": "modify",
      "roomCode": "DLX",
      "rateCode": "MEMBER_RATE",
      "adults": 2,
      "children": 1,
      "childrenAges": [10],
      "confirmationNumber": "ROOM001"
    },
    {
      "status": "cancel",
      "roomCode": "DLX",
      "rateCode": "PROMO_RATE",
      "adults": 2,
      "children": 0,
      "childrenAges": [],
      "confirmationNumber": "ROOM002"
    },
    {
      "status": "create",
      "roomCode": "FAM",
      "rateCode": "FAMILY_RATE",
      "adults": 2,
      "children": 2,
      "childrenAges": [6, 8]
    }
  ],
  "primaryGuest": { ... },
  "payment": { ... }
}
```

### 取消预订
```json
{
  "status": "cancel",
  "itineraryNumber": "ITN987654321",
  "hotelId": 12345,
  "primaryGuest": { ... },
  "payment": { ... }
}
```

## 技术实现

### 1. 新增文件
- `app/DTOs/UnifiedReservationRequestDTO.php` - 统一预订请求DTO
- `app/DTOs/SimplifiedRoomRequestDTO.php` - 房间请求DTO
- `tests/Feature/UnifiedReservationProcessTest.php` - 统一接口测试
- `examples/multi_room_modification_example.php` - 使用示例（已更新）

### 2. 修改文件
- `app/Http/Controllers/V1/SabreController.php` - 添加统一处理接口
- `routes/v1.php` - 添加新路由
- `app/DTOs/SimplifiedReservationRequestDTO.php` - 扩展支持统一格式

### 3. 核心逻辑
- **自动检测**: 根据数据结构自动识别单房间/多房间操作
- **房间级别操作**: 多房间修改支持每个房间独立的操作类型（modify, create, cancel）
- **统一验证**: 一致的参数验证规则，包括房间级别状态验证
- **向后兼容**: 保持所有原有接口不变
- **错误处理**: 统一的错误响应格式

## 响应格式

### 统一响应结构
所有操作都返回统一的响应格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "success": true,
    "itinerary_number": "ITN987654321",
    "total_rooms": 2,
    "success_count": 2,
    "failed_count": 0,
    "rooms": [
      {
        "success": true,
        "room_index": 0,
        "confirmation_number": "ROOM001",
        "room_code": "STD",
        "rate_code": "MEMBER_RATE"
      },
      {
        "success": true,
        "room_index": 1,
        "confirmation_number": "ROOM002",
        "room_code": "DLX",
        "rate_code": "PROMO_RATE"
      }
    ]
  }
}
```

## 兼容性

### 完全向后兼容
- 所有原有API接口保持不变
- 现有客户端代码无需修改
- 响应格式保持一致

### 新功能优势
- **统一数据格式**: 前端只需维护一套数据结构
- **操作类型明确**: 通过 `status` 字段清楚区分操作
- **灵活扩展**: 易于添加新的操作类型
- **一致体验**: 创建、修改、取消使用相同的数据格式

## 验证规则

- **status**: 必须为 `create`, `modify`, `cancel` 之一
- **单房间修改**: 需要 `confirmationNumber`
- **多房间修改**: 需要 `itineraryNumber` 和每个房间的 `confirmationNumber`
- **房间级别状态**: 房间的 `status` 字段可以为 `modify`, `create`, `cancel`
  - `modify`: 修改现有房间，需要 `confirmationNumber`
  - `create`: 新增房间到现有订单，不需要 `confirmationNumber`
  - `cancel`: 取消现有房间，需要 `confirmationNumber`
  - 如果不指定房间状态，默认为 `modify`
- **取消操作**: 需要 `itineraryNumber` 或 `confirmationNumbers`
- **儿童年龄**: 数量必须与儿童人数匹配，年龄范围0-17岁

## 使用建议

### 前端实现
1. 使用统一接口 `/api/v1/sabre/reservation`
2. 根据操作设置对应的 `status` 值
3. 保持数据结构一致性
4. 处理统一的响应格式

### 错误处理
- 监听HTTP状态码
- 检查响应中的 `success` 字段
- 显示详细的错误信息
- 处理部分成功场景

这个实现完全满足了前端的需求，提供了统一的数据格式，同时保持了系统的向后兼容性。