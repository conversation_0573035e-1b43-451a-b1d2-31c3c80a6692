# 批量处理预订接口 (Process Batch Reservations)

## 接口信息

- **URL**: `/api/v1/sabre/processBatchReservations`
- **方法**: `POST`
- **描述**: 支持混合操作的批量预订处理接口，可以在一次请求中执行多个创建、修改、取消操作

## 请求格式

请求体为数组格式，每个数组元素代表一个操作：

```json
[
    {
        "CRS_confirmationNumber": "100820CK000421",
        "itineraryNumber": "32446B0003254",
        "status": "modify",
        "hotelId": 100820,
        "chainId": "32446",
        "checkInDate": "2025-10-01",
        "checkOutDate": "2025-10-02",
        "numRooms": 1,
        "adults": 2,
        "children": 1,
        "childrenAges": [8],
        "roomCode": "1STE",
        "rateCode": "BAR",
        "primaryGuest": {
            "firstName": "张",
            "lastName": "三",
            "email": "<EMAIL>",
            "phone": "13800138000"
        },
        "payment": {
            "cardNumber": "****************",
            "cardHolder": "ZHANG SAN",
            "expiryMonth": 12,
            "expiryYear": 2025
        },
        "loyaltyNumber": "111",
        "sendConfirmationEmail": true
    },
    {
        "itineraryNumber": "32446B0003254",
        "status": "create",
        "hotelId": 100820,
        "chainId": "32446",
        "checkInDate": "2025-10-01",
        "checkOutDate": "2025-10-02",
        "numRooms": 1,
        "adults": 2,
        "children": 1,
        "childrenAges": [8],
        "roomCode": "1STE",
        "rateCode": "BAR",
        "primaryGuest": {
            "firstName": "张",
            "lastName": "三",
            "email": "<EMAIL>",
            "phone": "13800138000"
        },
        "payment": {
            "cardNumber": "****************",
            "cardHolder": "ZHANG SAN",
            "expiryMonth": 12,
            "expiryYear": 2025
        },
        "loyaltyNumber": "111",
        "sendConfirmationEmail": true
    }
]
```

## 参数说明

### 通用参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 是 | 操作类型：modify/create/cancel |
| hotelId | integer | 是 | 酒店ID |
| chainId | string | 否 | 连锁ID |

### 操作特定参数

#### modify 和 cancel 操作
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| CRS_confirmationNumber | string | 是 | 确认号 |

#### create 操作
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| itineraryNumber | string | 否 | 行程号（用于添加到现有行程） |

#### create 和 modify 操作
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| checkInDate | string | 是 | 入住日期 (YYYY-MM-DD) |
| checkOutDate | string | 是 | 退房日期 (YYYY-MM-DD) |
| roomCode | string | 是 | 房型代码 |
| rateCode | string | 是 | 价格代码 |
| adults | integer | 否 | 成人数量 (默认1) |
| children | integer | 否 | 儿童数量 (默认0) |
| childrenAges | array | 否 | 儿童年龄数组 |
| primaryGuest | object | 是 | 主要客人信息 |
| payment | object | 是 | 支付信息 |
| loyaltyNumber | string | 否 | 会员号 |
| promoCode | string | 否 | 促销代码 |
| specialRequests | string | 否 | 特殊要求 |
| sendConfirmationEmail | boolean | 否 | 是否发送确认邮件 |

## 响应格式

```json
{
    "success": true,
    "message": "部分操作成功：1个成功，1个失败",
    "data": {
        "itinerary_number": "32446B0003254",
        "total_operations": 2,
        "success_count": 1,
        "failed_count": 1,
        "operations": [
            {
                "index": 0,
                "status": "modify",
                "success": true,
                "confirmation_number": "100820CK000421",
                "itinerary_number": "32446B0003254",
                "room_code": "1STE",
                "rate_code": "BAR",
                "operation": "modify",
                "message": null,
                "error": null
            },
            {
                "index": 1,
                "status": "create",
                "success": false,
                "confirmation_number": null,
                "itinerary_number": "32446B0003254",
                "room_code": "1STE",
                "rate_code": "BAR",
                "operation": "create",
                "message": null,
                "error": "房间不可用"
            }
        ]
    }
}
```

## 错误响应

```json
{
    "success": false,
    "message": "参数验证失败",
    "errors": {
        "data.0.CRS_confirmationNumber": [
            "modify 操作时 CRS_confirmationNumber 字段是必需的。"
        ]
    }
}
```

## 注意事项

1. 最多支持10个操作
2. modify 和 cancel 操作必须提供 `CRS_confirmationNumber`
3. create 和 modify 操作必须提供完整的客人和支付信息
4. 儿童年龄数组的长度必须与儿童数量匹配
5. 系统会自动识别信用卡类型，无需手动提供 `cardType`
6. 如果提供了 `itineraryNumber`，新创建的订单会添加到该行程下
