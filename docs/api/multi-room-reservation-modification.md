# 多房间预订修改功能实现文档

## 概述

本文档详细介绍了GHA后端系统中多房间预订修改功能的实现。该功能扩展了原有的单房间修改能力，支持批量修改同一行程下的多个房间。

## 功能特性

### 1. 向后兼容性
- 现有单房间修改API保持不变
- 自动检测请求类型（单房间或多房间）
- 无缝切换处理逻辑

### 2. 多房间修改支持
- 支持同时修改多个房间
- 每个房间独立验证和处理
- 部分成功处理（某些房间修改成功，某些失败）

### 3. 数据验证
- 房间数量限制（1-10个房间）
- 儿童年龄验证（0-17岁）
- 儿童数量与年龄匹配验证
- 必填字段验证

## API接口

### 端点
```
POST /api/v1/sabre/modifyReservation
```

### 请求格式

#### 单房间修改（保持原有格式）
```json
{
  "CRS_confirmationNumber": "CONF123456",
  "hotelId": 12345,
  "checkInDate": "2024-12-15",
  "checkOutDate": "2024-12-17",
  "roomCode": "DLX",
  "rateCode": "MEMBER_RATE",
  "adults": 2,
  "children": 1,
  "childrenAges": [10],
  "primaryGuest": {
    "firstName": "张",
    "lastName": "三",
    "email": "<EMAIL>",
    "phone": "+86-13800138000",
    "address": {
      "line1": "北京市朝阳区建国路88号",
      "city": "北京",
      "state": "北京",
      "country": "CN",
      "postalCode": "100025"
    }
  },
  "payment": {
    "cardType": "VISA",
    "cardNumber": "****************",
    "cardHolder": "张三",
    "expiryMonth": 12,
    "expiryYear": 2026
  },
  "loyaltyNumber": "GHA123456789",
  "promoCode": "WINTER2024",
  "specialRequests": "希望安排靠近电梯的房间",
  "sendConfirmationEmail": true
}
```

#### 多房间修改（新格式）
```json
{
  "itineraryNumber": "ITN987654321",
  "hotelId": 12345,
  "checkInDate": "2024-12-15",
  "checkOutDate": "2024-12-18",
  "rooms": [
    {
      "roomCode": "STD",
      "rateCode": "MEMBER_RATE",
      "adults": 2,
      "children": 2,
      "childrenAges": [8, 12],
      "confirmationNumber": "ROOM001",
      "specialRequests": "需要加床"
    },
    {
      "roomCode": "DLX",
      "rateCode": "PROMO_RATE",
      "adults": 2,
      "children": 0,
      "childrenAges": [],
      "confirmationNumber": "ROOM002",
      "specialRequests": "高层房间，海景"
    }
  ],
  "primaryGuest": {
    "firstName": "李",
    "lastName": "四",
    "email": "<EMAIL>",
    "phone": "+86-13900139000",
    "address": {
      "line1": "上海市浦东新区世纪大道1000号",
      "city": "上海",
      "state": "上海",
      "country": "CN",
      "postalCode": "200120"
    }
  },
  "payment": {
    "cardType": "MASTERCARD",
    "cardNumber": "****************",
    "cardHolder": "李四",
    "expiryMonth": 6,
    "expiryYear": 2027
  },
  "loyaltyNumber": "GHA987654321",
  "loyaltyLevel": "GOLD",
  "promoCode": "FAMILY2024",
  "specialRequests": "家庭出行，需要相邻房间",
  "sendConfirmationEmail": true
}
```

### 响应格式

#### 单房间修改成功响应
```json
{
  "success": true,
  "message": "修改成功",
  "data": {
    "success": true,
    "confirmation_number": "CONF123456",
    "itinerary_number": "ITN987654321",
    "status": "Confirmed"
  }
}
```

#### 多房间修改成功响应
```json
{
  "success": true,
  "message": "修改成功",
  "data": {
    "success": true,
    "itinerary_number": "ITN987654321",
    "total_rooms": 2,
    "success_count": 2,
    "failed_count": 0,
    "rooms": [
      {
        "success": true,
        "room_index": 0,
        "confirmation_number": "ROOM001"
      },
      {
        "success": true,
        "room_index": 1,
        "confirmation_number": "ROOM002"
      }
    ]
  }
}
```

#### 部分成功响应
```json
{
  "success": true,
  "message": "修改成功",
  "data": {
    "success": true,
    "itinerary_number": "ITN987654321",
    "total_rooms": 2,
    "success_count": 1,
    "failed_count": 1,
    "rooms": [
      {
        "success": true,
        "room_index": 0,
        "confirmation_number": "ROOM001"
      },
      {
        "success": false,
        "room_index": 1,
        "error": "房间不可用或价格已变更"
      }
    ]
  }
}
```

## 技术实现

### 1. 数据传输对象 (DTOs)

#### MultiRoomReservationRequestDTO
- 处理多房间修改请求数据
- 包含多个房间信息数组
- 提供统一的数据访问接口

#### SimplifiedRoomRequestDTO
- 表示单个房间的修改信息
- 包含房间代码、价格代码、客人数量等
- 验证儿童年龄数据

### 2. 服务层扩展

#### SabreReservationService::modifyMultiRoomReservation()
- 处理多房间修改业务逻辑
- 循环处理每个房间的修改请求
- 统计成功和失败的房间数量
- 返回详细的处理结果

### 3. 控制器层改进

#### SabreController::modifyReservation()
- 自动检测请求类型（单房间vs多房间）
- 路由到相应的处理方法
- 统一的验证和错误处理

#### 验证规则
- 单房间：需要`CRS_confirmationNumber`
- 多房间：需要`itineraryNumber`和`rooms`数组
- 每个房间需要`confirmationNumber`
- 儿童年龄范围和数量验证

### 4. 错误处理

#### 验证错误
- 参数缺失或格式错误
- 儿童年龄不匹配
- 房间数量超限

#### 业务错误
- Sabre API调用失败
- 房间不可用
- 价格变更

## 文件结构

```
app/
├── DTOs/
│   ├── MultiRoomReservationRequestDTO.php     # 多房间请求DTO
│   ├── SimplifiedRoomRequestDTO.php           # 单房间请求DTO
│   └── SimplifiedReservationRequestDTO.php    # 原有单房间DTO（已扩展）
├── Services/
│   └── SabreReservationService.php           # 预订服务（已扩展）
└── Http/Controllers/V1/
    └── SabreController.php                    # Sabre控制器（已修改）

tests/Feature/
└── MultiRoomReservationModificationTest.php  # 功能测试

examples/
└── multi_room_modification_example.php       # 使用示例

docs/api/
└── multi-room-reservation-modification.md    # 本文档
```

## 使用示例

详细的使用示例请参考：
- `examples/multi_room_modification_example.php` - PHP调用示例
- `tests/Feature/MultiRoomReservationModificationTest.php` - 测试用例

## 兼容性说明

### 向后兼容
- 现有单房间修改API完全兼容
- 不影响现有客户端代码
- 响应格式保持一致

### 新功能
- 多房间修改需要使用新的数据格式
- 需要提供行程号而不是确认号
- 每个房间需要单独的确认号

## 性能考虑

### 处理策略
- 串行处理每个房间修改（保证数据一致性）
- 失败房间不影响其他房间处理
- 详细记录每个房间的处理结果

### 限制
- 最多支持10个房间同时修改
- 超时处理机制
- 错误重试逻辑

## 高级功能：房间数量变化处理

### 新增API端点
```
POST /api/v1/sabre/modifyReservationWithRoomChanges
```

### 功能特性
- **智能房间对比**：自动识别需要修改、取消和添加的房间
- **批量操作处理**：一次API调用完成所有房间变化
- **详细操作日志**：记录每个操作的具体结果
- **部分成功支持**：某些操作失败不影响其他操作

### 请求格式
```json
{
  "itineraryNumber": "ITN123456789",
  "hotelId": 12345,
  "hotelCode": "HOTEL001",
  "checkInDate": "2024-12-20",
  "checkOutDate": "2024-12-23",

  // 当前所有房间列表（用于对比确定需要取消哪些房间）
  "currentRooms": [
    {
      "confirmationNumber": "ROOM001",
      "roomCode": "STD",
      "rateCode": "MEMBER_RATE"
    },
    {
      "confirmationNumber": "ROOM002",
      "roomCode": "DLX",
      "rateCode": "DELUXE_RATE"
    }
  ],

  // 目标房间配置（最终期望的房间状态）
  "targetRooms": [
    // 有confirmationNumber：修改现有房间
    {
      "confirmationNumber": "ROOM001",
      "roomCode": "STE",
      "rateCode": "UPGRADE_RATE",
      "adults": 2,
      "children": 1,
      "childrenAges": [8],
      "specialRequests": "升级到套房"
    },
    // 无confirmationNumber：添加新房间
    {
      "roomCode": "DLX",
      "rateCode": "VIP_RATE",
      "adults": 2,
      "children": 0,
      "specialRequests": "新增VIP房间"
    }
    // ROOM002不在targetRooms中，将被自动取消
  ],

  "primaryGuest": { ... },
  "payment": { ... }
}
```

### 响应格式
```json
{
  "success": true,
  "message": "修改成功",
  "data": {
    "success": true,
    "itinerary_number": "ITN123456789",

    // 房间修改操作结果
    "modifications": [
      {
        "success": true,
        "room_index": 0,
        "confirmation_number": "ROOM001",
        "operation": "modify",
        "result": { ... }
      }
    ],

    // 房间取消操作结果
    "cancellations": [
      {
        "success": true,
        "confirmation_number": "ROOM002",
        "operation": "cancel",
        "result": { ... }
      }
    ],

    // 房间添加操作结果
    "additions": [
      {
        "success": true,
        "room_index": 1,
        "operation": "add",
        "confirmation_number": "ROOM301",
        "result": { ... }
      }
    ],

    // 操作汇总
    "summary": {
      "total_operations": 3,
      "successful_operations": 3,
      "failed_operations": 0
    }
  }
}
```

### 操作逻辑
1. **修改操作**：对于`targetRooms`中有`confirmationNumber`且存在于`currentRooms`的房间
2. **取消操作**：对于`currentRooms`中存在但不在`targetRooms`中的房间
3. **添加操作**：对于`targetRooms`中没有`confirmationNumber`的房间

### 使用场景
- **增加房间**：客人需要预订更多房间
- **减少房间**：客人取消部分房间预订
- **房型调整**：同时取消一些房间，添加另一些房间
- **复合操作**：修改现有房间参数的同时调整房间数量

## 文件结构更新

```
app/
├── DTOs/
│   ├── MultiRoomReservationRequestDTO.php     # 多房间请求DTO
│   ├── SimplifiedRoomRequestDTO.php           # 单房间请求DTO
│   └── SimplifiedReservationRequestDTO.php    # 原有单房间DTO（已扩展）
├── Services/
│   └── SabreReservationService.php           # 预订服务（已扩展）
└── Http/Controllers/V1/
    └── SabreController.php                    # Sabre控制器（已修改）

tests/Feature/
├── MultiRoomReservationModificationTest.php  # 多房间修改测试
└── RoomCountModificationTest.php             # 房间数量变化测试

examples/
├── multi_room_modification_example.php       # 多房间修改示例
└── room_count_modification_example.php       # 房间数量变化示例

docs/api/
└── multi-room-reservation-modification.md    # 本文档
```

## API端点总览

| 端点 | 功能 | 使用场景 |
|------|------|----------|
| `POST /sabre/modifyReservation` | 单房间/多房间修改 | 修改现有房间参数，不改变房间数量 |
| `POST /sabre/modifyReservationWithRoomChanges` | 综合房间变化处理 | 涉及房间数量增减的复杂修改场景 |

## 总结

多房间预订修改功能成功扩展了GHA系统的能力，在保持向后兼容的同时，提供了强大的批量修改功能。该实现具有以下优点：

1. **完全向后兼容** - 现有代码无需修改
2. **灵活的验证机制** - 支持复杂的业务规则
3. **详细的错误处理** - 提供准确的错误信息
4. **可扩展的架构** - 易于后续功能扩展
5. **完整的测试覆盖** - 确保功能稳定性
6. **智能房间管理** - 支持房间数量的灵活增减
7. **综合操作支持** - 一次调用完成修改、取消、添加的组合操作

该功能现已准备好投入生产使用，能够满足各种复杂的房间修改需求。