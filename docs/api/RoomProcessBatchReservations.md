# RoomController 批量处理预订接口 (Room Process Batch Reservations)

## 接口信息

- **URL**: `/api/v1/room/processBatchReservations`
- **方法**: `POST`
- **描述**: RoomController版本的批量预订处理接口，支持混合操作（创建、修改、取消），包含会员信息自动处理和本地数据存储

## 与SabreController的区别

RoomController版本的接口相比SabreController版本有以下特点：

1. **自动会员信息处理**：自动从登录用户获取会员卡号和等级
2. **本地数据存储**：成功创建的订单会自动写入本地数据库（ResvBase表）
3. **增强的房间信息**：包含房间名称、描述、图片等本地化信息
4. **用户上下文**：更适合前端用户直接调用

## 请求格式

请求体为数组格式，每个数组元素代表一个操作：

```json
[
    {
        "CRS_confirmationNumber": "100820CK000421",
        "itineraryNumber": "32446B0003254",
        "status": "modify",
        "hotelId": 100820,
        "chainId": "32446",
        "checkInDate": "2025-10-01",
        "checkOutDate": "2025-10-02",
        "numRooms": 1,
        "adults": 2,
        "children": 1,
        "childrenAges": [8],
        "roomCode": "1STE",
        "rateCode": "BAR",
        "primaryGuest": {
            "firstName": "张",
            "lastName": "三",
            "email": "<EMAIL>",
            "phone": "13800138000"
        },
        "payment": {
            "cardNumber": "****************",
            "cardHolder": "ZHANG SAN",
            "expiryMonth": 12,
            "expiryYear": 2025
        },
        "loyaltyNumber": "111",
        "sendConfirmationEmail": true
    },
    {
        "itineraryNumber": "32446B0003254",
        "status": "create",
        "hotelId": 100820,
        "chainId": "32446",
        "checkInDate": "2025-10-01",
        "checkOutDate": "2025-10-02",
        "numRooms": 1,
        "adults": 2,
        "children": 1,
        "childrenAges": [8],
        "roomCode": "1STE",
        "rateCode": "BAR",
        "primaryGuest": {
            "firstName": "张",
            "lastName": "三",
            "email": "<EMAIL>",
            "phone": "13800138000"
        },
        "payment": {
            "cardNumber": "****************",
            "cardHolder": "ZHANG SAN",
            "expiryMonth": 12,
            "expiryYear": 2025
        },
        "loyaltyNumber": "111",
        "sendConfirmationEmail": true
    }
]
```

## 参数说明

参数规则与SabreController版本相同，但有以下增强：

### 自动处理的字段

| 字段名 | 说明 |
|--------|------|
| loyaltyNumber | 如果未提供，自动从登录用户获取会员卡号 |
| loyaltyLevel | 自动从登录用户获取会员等级 |
| sendConfirmationEmail | 如果未提供，默认为false |
| payment.cardType | 自动从卡号识别卡片类型 |

### 操作类型

- **create**: 创建新订单，成功后会写入本地数据库
- **modify**: 修改现有订单
- **cancel**: 取消现有订单

## 响应格式

```json
{
    "success": true,
    "message": "部分操作成功：1个成功，1个失败",
    "data": {
        "itinerary_number": "32446B0003254",
        "total_operations": 2,
        "success_count": 1,
        "failed_count": 1,
        "operations": [
            {
                "index": 0,
                "status": "modify",
                "success": true,
                "confirmation_number": "100820CK000421",
                "itinerary_number": "32446B0003254",
                "room_code": "1STE",
                "rate_code": "BAR",
                "operation": "modify",
                "message": null,
                "error": null
            },
            {
                "index": 1,
                "status": "create",
                "success": false,
                "confirmation_number": null,
                "itinerary_number": "32446B0003254",
                "room_code": "1STE",
                "rate_code": "BAR",
                "operation": "create",
                "message": null,
                "error": "房间不可用"
            }
        ]
    }
}
```

## 特殊处理

### 会员信息自动填充

```php
// 如果用户已登录，自动获取会员信息
if (AuthCheck()) {
    $loyaltyNumber = AuthUser()->membership_card_no ?? '';
    $loyaltyLevel = AuthUser()->membership_level ?? '';
}
```

### 本地数据存储

成功创建的订单会自动调用：
```php
ResvBase::createResv($reservationData, $result);
```

### 全部失败处理

如果所有操作都失败，返回500状态码：
```json
{
    "success": false,
    "message": "全部操作失败"
}
```

## 使用场景

1. **用户前端**：用户在前端进行批量订单操作
2. **会员系统**：需要自动处理会员信息的场景
3. **本地化需求**：需要将订单数据存储到本地数据库
4. **移动应用**：需要完整用户上下文的移动端应用

## 注意事项

1. 需要用户登录才能获取会员信息
2. 创建操作成功后会自动写入本地数据库
3. 支持最多10个操作
4. 儿童年龄数组长度必须与儿童数量匹配
5. 系统会自动识别信用卡类型
6. 如果所有操作都失败，接口返回500状态码
