<?php

/**
 * 批量查询多个酒店最佳价格接口使用示例
 *
 * 此文件展示了如何使用新的批量查询接口来获取多个酒店的最佳价格
 */

// ============================================================================
// 基本查询示例
// ============================================================================

$basicRequest = [
    "hotelIds" => [100823, 100824, 100825, 100826],
    "startDate" => "2025-07-09",
    "endDate" => "2025-07-11",
    "numRooms" => 1,
    "adults" => 2
];

echo "=== 基本查询示例 ===\n";
echo "curl -X POST /api/v1/sabre/multipleHotelsBestPrices \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($basicRequest, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "'\n\n";

// ============================================================================
// 带儿童的查询示例
// ============================================================================

$familyRequest = [
    "hotelIds" => [100823, 100824, 100825],
    "startDate" => "2025-08-15",
    "endDate" => "2025-08-18",
    "numRooms" => 2,
    "adults" => 4,
    "children" => 2,
    "childrenAge" => "8,12"
];

echo "=== 家庭出行查询示例 ===\n";
echo "curl -X POST /api/v1/sabre/multipleHotelsBestPrices \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($familyRequest, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "'\n\n";

// ============================================================================
// 带促销代码的查询示例
// ============================================================================

$promoRequest = [
    "hotelIds" => [100823, 100824, 100825, 100826, 100827],
    "startDate" => "2025-09-01",
    "endDate" => "2025-09-03",
    "numRooms" => 1,
    "adults" => 2,
    "accessCode" => "AUTUMN2025"
];

echo "=== 促销价格查询示例 ===\n";
echo "curl -X POST /api/v1/sabre/multipleHotelsBestPrices \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($promoRequest, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "'\n\n";

// ============================================================================
// 模拟响应数据
// ============================================================================

$mockResponse = [
    "code" => 200,
    "message" => "success",
    "data" => [
        "success" => true,
        "hotels" => [
            [
                "hotelId" => 100823,
                "bestPrice" => [
                    "currency" => "USD",
                    "originalPrice" => 25000,
                    "cnyPrice" => 180000,
                    "priceDisplay" => "¥1,800.00",
                    "rateCode" => "BAR",
                    "rateName" => "Best Available Rate",
                    "isMemberRate" => false,
                    "guaranteePolicy" => "GCC_CRD",
                    "cancelRuleString" => "可免费取消至入住前24小时"
                ],
                "room" => [
                    "roomCode" => "STD",
                    "roomName" => "标准房",
                    "roomDescription" => "舒适的标准客房，配备现代化设施"
                ],
                "availableRoomsCount" => 3,
                "totalRatesCount" => 5
            ],
            [
                "hotelId" => 100824,
                "bestPrice" => [
                    "currency" => "USD",
                    "originalPrice" => 28000,
                    "cnyPrice" => 201600,
                    "priceDisplay" => "¥2,016.00",
                    "rateCode" => "MEM",
                    "rateName" => "Member Rate",
                    "isMemberRate" => true,
                    "guaranteePolicy" => "GCC_CRD",
                    "cancelRuleString" => "可免费取消至入住前48小时"
                ],
                "room" => [
                    "roomCode" => "DLX",
                    "roomName" => "豪华房",
                    "roomDescription" => "宽敞的豪华客房，享有城市景观"
                ],
                "availableRoomsCount" => 2,
                "totalRatesCount" => 4
            ],
            [
                "hotelId" => 100825,
                "bestPrice" => [
                    "currency" => "USD",
                    "originalPrice" => 32000,
                    "cnyPrice" => 230400,
                    "priceDisplay" => "¥2,304.00",
                    "rateCode" => "PROMO",
                    "rateName" => "Promotional Rate",
                    "isMemberRate" => false,
                    "guaranteePolicy" => "GCC_CRD",
                    "cancelRuleString" => "不可取消"
                ],
                "room" => [
                    "roomCode" => "STE",
                    "roomName" => "套房",
                    "roomDescription" => "豪华套房，包含独立客厅和卧室"
                ],
                "availableRoomsCount" => 1,
                "totalRatesCount" => 2
            ]
        ],
        "total_count" => 3,
        "errors" => [
            [
                "hotelId" => 100826,
                "error" => "该酒店在指定日期无可用房间"
            ]
        ],
        "query_params" => [
            "startDate" => "2025-07-09",
            "endDate" => "2025-07-11",
            "numRooms" => 1,
            "adults" => 2,
            "children" => 0,
            "accessCode" => null
        ]
    ]
];

echo "=== 模拟响应数据 ===\n";
echo json_encode($mockResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// ============================================================================
// 前端使用示例 (JavaScript)
// ============================================================================

$jsExample = <<<'JS'
// JavaScript 前端调用示例
async function getMultipleHotelsBestPrices(hotelIds, checkIn, checkOut, options = {}) {
    const requestData = {
        hotelIds: hotelIds,
        startDate: checkIn,
        endDate: checkOut,
        numRooms: options.numRooms || 1,
        adults: options.adults || 2,
        children: options.children || 0,
        childrenAge: options.childrenAge || null,
        accessCode: options.accessCode || null
    };

    try {
        const response = await fetch('/api/v1/sabre/multipleHotelsBestPrices', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + getAuthToken()
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (result.code === 200) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('查询酒店价格失败:', error);
        throw error;
    }
}

// 使用示例
const hotelIds = [100823, 100824, 100825];
const checkIn = '2025-07-09';
const checkOut = '2025-07-11';

getMultipleHotelsBestPrices(hotelIds, checkIn, checkOut, {
    numRooms: 1,
    adults: 2,
    children: 1,
    childrenAge: '8'
}).then(data => {
    console.log('查询成功:', data);

    // 处理成功的酒店数据
    data.hotels.forEach(hotel => {
        console.log(`酒店 ${hotel.hotelId}: ${hotel.bestPrice.priceDisplay}`);
    });

    // 处理错误的酒店
    if (data.errors.length > 0) {
        console.log('部分酒店查询失败:', data.errors);
    }
}).catch(error => {
    console.error('查询失败:', error);
});
JS;

echo "=== 前端使用示例 ===\n";
echo $jsExample . "\n\n";

// ============================================================================
// 使用场景说明
// ============================================================================

echo "=== 使用场景说明 ===\n";
echo "1. 酒店列表页面 - 显示多个酒店的起始价格\n";
echo "2. 搜索结果页面 - 快速获取搜索结果中所有酒店的价格\n";
echo "3. 比价功能 - 帮助用户比较不同酒店的价格\n";
echo "4. 推荐系统 - 根据价格推荐性价比高的酒店\n";
echo "5. 价格监控 - 监控多个酒店的价格变化\n\n";

echo "=== 性能优势 ===\n";
echo "1. 并发查询 - 同时查询多个酒店，减少总响应时间\n";
echo "2. 缓存机制 - 相同参数的查询会使用缓存结果\n";
echo "3. 部分成功 - 即使部分酒店查询失败也会返回成功的结果\n";
echo "4. 最佳价格 - 自动筛选出每个酒店的最低价格\n";
echo "5. 货币转换 - 自动转换为人民币显示\n\n";

echo "=== 注意事项 ===\n";
echo "1. 一次最多查询20个酒店\n";
echo "2. 儿童年龄格式：多个年龄用逗号分隔\n";
echo "3. 促销代码可选，会优先返回促销价格\n";
echo "4. 价格以分为单位存储，显示时需要除以100\n";
echo "5. 接口支持部分成功，需要检查errors数组\n";
