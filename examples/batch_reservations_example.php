<?php

/**
 * 批量下单接口使用示例
 *
 * 此示例展示如何使用批量下单接口来创建多个预订，
 * 第一个订单生成行程号，后续订单将使用这个行程号进行关联
 */

// 批量下单请求示例 - 直接传递订单数组，不需要reservations包装key
$batchReservationData = [
    // 第一个订单 - 将生成行程号
    [
        'hotelId' => 12345,
        'checkInDate' => '2024-12-20',
        'checkOutDate' => '2024-12-22',
        'numRooms' => 1,
        'adults' => 2,
        'children' => 0,
        'roomCode' => 'STD',
        'rateCode' => 'BAR',
        'primaryGuest' => [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'address' => [
                'line1' => '123 Main St',
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'US',
                'postalCode' => '10001',
            ],
        ],
        'payment' => [
            'cardType' => 'VI',
            'cardNumber' => '****************',
            'cardHolder' => 'John Doe',
            'expiryMonth' => 12,
            'expiryYear' => 2026,
        ],
    ],
    // 第二个订单 - 将使用第一个订单的行程号
    [
        'hotelId' => 12346,
        'checkInDate' => '2024-12-22',
        'checkOutDate' => '2024-12-24',
        'numRooms' => 1,
        'adults' => 1,
        'children' => 1,
        'childrenAges' => [8],
        'roomCode' => 'DEL',
        'rateCode' => 'AAA',
        'primaryGuest' => [
            'firstName' => 'Jane',
            'lastName' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '+1234567891',
            'address' => [
                'line1' => '456 Oak Ave',
                'city' => 'Los Angeles',
                'state' => 'CA',
                'country' => 'US',
                'postalCode' => '90210',
            ],
        ],
        'payment' => [
            'cardType' => 'MC',
            'cardNumber' => '****************',
            'cardHolder' => 'Jane Smith',
            'expiryMonth' => 8,
            'expiryYear' => 2025,
        ],
    ],
    // 第三个订单 - 也将使用第一个订单的行程号
    [
        'hotelId' => 12347,
        'checkInDate' => '2024-12-24',
        'checkOutDate' => '2024-12-26',
        'numRooms' => 1,
        'adults' => 2,
        'children' => 0,
        'roomCode' => 'SUI',
        'rateCode' => 'PROMO',
        'primaryGuest' => [
            'firstName' => 'Bob',
            'lastName' => 'Wilson',
            'email' => '<EMAIL>',
            'phone' => '+1234567892',
            'address' => [
                'line1' => '789 Pine Rd',
                'city' => 'Chicago',
                'state' => 'IL',
                'country' => 'US',
                'postalCode' => '60601',
            ],
        ],
        'payment' => [
            'cardType' => 'AX',
            'cardNumber' => '***************',
            'cardHolder' => 'Bob Wilson',
            'expiryMonth' => 6,
            'expiryYear' => 2027,
        ],
    ],
];

/**
 * 发送批量下单请求
 */
function sendBatchReservationRequest($data)
{
    $url = 'http://your-domain.com/api/v1/sabre/createBatchReservations';

    $options = [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json',
            // 如果需要认证，添加授权头
            // 'Authorization: Bearer your-access-token'
        ],
    ];

    $curl = curl_init();
    curl_setopt_array($curl, $options);
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true),
    ];
}

// 发送请求
$result = sendBatchReservationRequest($batchReservationData);

// 处理响应
if ($result['http_code'] === 200) {
    $response = $result['response'];

    if ($response['success']) {
        echo "批量下单请求成功处理！\n";
        echo '行程号: '.$response['data']['itinerary_number']."\n";
        echo '总订单数: '.$response['data']['total_reservations']."\n";
        echo '成功数量: '.$response['data']['success_count']."\n";
        echo '失败数量: '.$response['data']['failed_count']."\n";
        echo '消息: '.$response['message']."\n";

        // 显示每个订单的详细结果
        echo "\n详细结果：\n";
        foreach ($response['data']['reservations'] as $reservation) {
            echo '订单 '.($reservation['index'] + 1).': ';
            if ($reservation['success']) {
                echo '成功 - 确认号: '.($reservation['confirmation_number'] ?? 'N/A')."\n";
            } else {
                echo '失败 - 错误: '.($reservation['error'] ?? '未知错误')."\n";
            }
        }

    } else {
        echo '批量下单请求失败: '.$response['message']."\n";
        if (isset($response['errors'])) {
            echo '错误详情: '.json_encode($response['errors'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n";
        }
    }
} else {
    echo 'HTTP请求失败，状态码: '.$result['http_code']."\n";
    echo '响应: '.json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n";
}

/**
 * 批量下单接口响应格式示例：
 *
 * {
 *   "success": true,
 *   "message": "全部下单成功",
 *   "data": {
 *     "itinerary_number": "ABC123456",
 *     "total_reservations": 3,
 *     "success_count": 3,
 *     "failed_count": 0,
 *     "reservations": [
 *       {
 *         "index": 0,
 *         "success": true,
 *         "reservation_id": "ed8b4a84-15e4-4c3c-97da-22e9976641dd",
 *         "confirmation_number": "CONF123",
 *         "itinerary_number": "ABC123456",
 *         "status": "Confirmed"
 *       },
 *       {
 *         "index": 1,
 *         "success": true,
 *         "reservation_id": "048e5643-f731-4388-bb40-c85996f322f6",
 *         "confirmation_number": "CONF456",
 *         "itinerary_number": "ABC123456",
 *         "status": "Confirmed"
 *       },
 *       {
 *         "index": 2,
 *         "success": true,
 *         "reservation_id": "12345678-1234-1234-1234-123456789abc",
 *         "confirmation_number": "CONF789",
 *         "itinerary_number": "ABC123456",
 *         "status": "Confirmed"
 *       }
 *     ]
 *   },
 *   "status_code": 200
 * }
 */

/**
 * 使用说明：
 *
 * 1. 接口地址：POST /api/v1/sabre/createBatchReservations
 *
 * 2. 请求格式：
 *    - Content-Type: application/json
 *    - 请求体：直接传递订单数组，每个元素是一个订单的完整信息（不需要reservations包装key）
 *
 * 3. 关键特性：
 *    - 支持1-10个订单的批量下单
 *    - 第一个成功的订单会生成行程号
 *    - 后续订单会自动使用这个行程号进行关联
 *    - 即使部分订单失败，成功的订单仍会保留
 *    - 每个订单的结果都会在响应中详细显示
 *
 * 4. 错误处理：
 *    - 单个订单失败不会影响其他订单的处理
 *    - 所有错误信息都会在响应中详细显示
 *    - 支持部分成功的场景
 *
 * 5. 行程关联：
 *    - 所有成功下单的订单都会使用同一个行程号
 *    - 行程号从第一个成功的订单中获取
 *    - 后续订单在下单时会自动添加这个行程号
 */
