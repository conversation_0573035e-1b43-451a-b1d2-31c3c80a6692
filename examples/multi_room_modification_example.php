<?php

/**
 * 多房间预订修改API使用示例
 *
 * 本示例展示了如何使用GHA后端系统的多房间修改功能
 */

require_once __DIR__.'/../vendor/autoload.php';

// API基本配置
$baseUrl = 'https://your-api-domain.com/api/v1';
$apiKey = 'your-api-key'; // 如果需要API密钥

/**
 * 单房间修改示例（统一格式 - 使用status字段）
 */
function singleRoomModificationExampleUnified()
{
    global $baseUrl, $apiKey;

    $data = [
        // 操作类型
        'status' => 'modify',

        // 修改预订必需的确认号
        'confirmationNumber' => 'CONF123456',

        // 基本预订信息（与创建格式一致）
        'hotelId' => 12345,
        'checkInDate' => '2024-12-15',
        'checkOutDate' => '2024-12-17',
        'adults' => 2,
        'children' => 1,
        'childrenAges' => [10],

        // 新的房型和价格信息
        'roomCode' => 'DLX',
        'rateCode' => 'MEMBER_RATE',

        // 客人信息
        'primaryGuest' => [
            'firstName' => '张',
            'lastName' => '三',
            'email' => '<EMAIL>',
            'phone' => '+86-13800138000',
            'address' => [
                'line1' => '北京市朝阳区建国路88号',
                'city' => '北京',
                'state' => '北京',
                'country' => 'CN',
                'postalCode' => '100025',
            ],
        ],

        // 支付信息
        'payment' => [
            'cardType' => 'VISA',
            'cardNumber' => '****************',
            'cardHolder' => '张三',
            'expiryMonth' => 12,
            'expiryYear' => 2026,
        ],

        // 可选参数
        'loyaltyNumber' => 'GHA123456789',
        'promoCode' => 'WINTER2024',
        'specialRequests' => '希望安排靠近电梯的房间',
        'sendConfirmationEmail' => true,
    ];

    return callApi('POST', '/sabre/reservation', $data);
}

/**
 * 单房间修改示例（向后兼容 - 原有接口）
 */
function singleRoomModificationExampleLegacy()
{
    global $baseUrl, $apiKey;

    $data = [
        // 修改预订必需的确认号
        'CRS_confirmationNumber' => 'CONF123456',

        // 基本预订信息
        'hotelId' => 12345,
        'checkInDate' => '2024-12-15',
        'checkOutDate' => '2024-12-17',
        'numRooms' => 1,
        'adults' => 2,
        'children' => 1,
        'childrenAges' => [10],

        // 新的房型和价格信息
        'roomCode' => 'DLX',
        'rateCode' => 'MEMBER_RATE',

        // 客人信息
        'primaryGuest' => [
            'firstName' => '张',
            'lastName' => '三',
            'email' => '<EMAIL>',
            'phone' => '+86-13800138000',
            'address' => [
                'line1' => '北京市朝阳区建国路88号',
                'city' => '北京',
                'state' => '北京',
                'country' => 'CN',
                'postalCode' => '100025',
            ],
        ],

        // 支付信息
        'payment' => [
            'cardType' => 'VISA',
            'cardNumber' => '****************',
            'cardHolder' => '张三',
            'expiryMonth' => 12,
            'expiryYear' => 2026,
        ],

        // 可选参数
        'loyaltyNumber' => 'GHA123456789',
        'promoCode' => 'WINTER2024',
        'specialRequests' => '希望安排靠近电梯的房间',
        'sendConfirmationEmail' => true,
    ];

    return callApi('POST', '/sabre/modifyReservation', $data);
}

/**
 * 多房间修改示例（统一格式 - 使用status字段）
 */
function multiRoomModificationExampleUnified()
{
    global $baseUrl, $apiKey;

    $data = [
        // 操作类型
        'status' => 'modify',

        // 修改多房间预订必需的行程号
        'itineraryNumber' => 'ITN987654321',

        // 基本预订信息（与创建格式一致）
        'hotelId' => 12345,
        'checkInDate' => '2024-12-15',
        'checkOutDate' => '2024-12-18',

        // 多个房间信息
        'rooms' => [
            // 房间1
            [
                'roomCode' => 'STD',
                'rateCode' => 'MEMBER_RATE',
                'adults' => 2,
                'children' => 2,
                'childrenAges' => [8, 12],
                'confirmationNumber' => 'ROOM001',
                'specialRequests' => '需要加床',
            ],
            // 房间2
            [
                'roomCode' => 'DLX',
                'rateCode' => 'PROMO_RATE',
                'adults' => 2,
                'children' => 0,
                'childrenAges' => [],
                'confirmationNumber' => 'ROOM002',
                'specialRequests' => '高层房间，海景',
            ],
            // 房间3
            [
                'roomCode' => 'STE',
                'rateCode' => 'SUITE_RATE',
                'adults' => 3,
                'children' => 1,
                'childrenAges' => [5],
                'confirmationNumber' => 'ROOM003',
                'specialRequests' => '套房，需要沙发床',
            ],
        ],

        // 主要联系人信息
        'primaryGuest' => [
            'firstName' => '李',
            'lastName' => '四',
            'email' => '<EMAIL>',
            'phone' => '+86-13900139000',
            'address' => [
                'line1' => '上海市浦东新区世纪大道1000号',
                'city' => '上海',
                'state' => '上海',
                'country' => 'CN',
                'postalCode' => '200120',
            ],
        ],

        // 支付信息
        'payment' => [
            'cardType' => 'MASTERCARD',
            'cardNumber' => '****************',
            'cardHolder' => '李四',
            'expiryMonth' => 6,
            'expiryYear' => 2027,
        ],

        // 可选参数
        'loyaltyNumber' => 'GHA987654321',
        'loyaltyLevel' => 'GOLD',
        'promoCode' => 'FAMILY2024',
        'specialRequests' => '家庭出行，需要相邻房间',
        'sendConfirmationEmail' => true,
    ];

    return callApi('POST', '/sabre/reservation', $data);
}

/**
 * 多房间修改示例（支持房间级别状态 - modify, create, cancel）
 */
function multiRoomModificationWithRoomLevelStatus()
{
    global $baseUrl, $apiKey;

    $data = [
        // 操作类型
        'status' => 'modify',

        // 修改多房间预订必需的行程号
        'itineraryNumber' => 'ITN987654321',

        // 基本预订信息
        'hotelId' => 12345,
        'checkInDate' => '2024-12-15',
        'checkOutDate' => '2024-12-18',

        // 多个房间信息，每个房间有自己的操作状态
        'rooms' => [
            // 房间1 - 修改现有房间
            [
                'status' => 'modify',  // 修改现有房间
                'roomCode' => 'DLX',   // 从STD升级到DLX
                'rateCode' => 'MEMBER_RATE',
                'adults' => 2,
                'children' => 1,       // 从2个儿童改为1个
                'childrenAges' => [10], // 只保留10岁的儿童
                'confirmationNumber' => 'ROOM001', // 修改需要确认号
                'specialRequests' => '升级到豪华房，需要加床',
            ],
            // 房间2 - 取消现有房间
            [
                'status' => 'cancel',  // 取消这个房间
                'roomCode' => 'DLX',   // 保持原有信息用于识别
                'rateCode' => 'PROMO_RATE',
                'adults' => 2,
                'children' => 0,
                'childrenAges' => [],
                'confirmationNumber' => 'ROOM002', // 取消需要确认号
                'specialRequests' => '',
            ],
            // 房间3 - 保持不变（默认为modify状态）
            [
                // 'status' => 'modify', // 可省略，默认为modify
                'roomCode' => 'STE',
                'rateCode' => 'SUITE_RATE',
                'adults' => 3,
                'children' => 1,
                'childrenAges' => [5],
                'confirmationNumber' => 'ROOM003',
                'specialRequests' => '套房，需要沙发床',
            ],
            // 房间4 - 新增房间到现有订单
            [
                'status' => 'create',  // 创建新房间加入订单
                'roomCode' => 'FAM',   // 家庭房
                'rateCode' => 'FAMILY_RATE',
                'adults' => 2,
                'children' => 2,
                'childrenAges' => [6, 8],
                // 'confirmationNumber' 创建新房间时不需要确认号
                'specialRequests' => '家庭房，需要连通房间',
            ],
        ],

        // 主要联系人信息
        'primaryGuest' => [
            'firstName' => '王',
            'lastName' => '五',
            'email' => '<EMAIL>',
            'phone' => '+86-13800138888',
            'address' => [
                'line1' => '广州市天河区珠江新城花城大道123号',
                'city' => '广州',
                'state' => '广东',
                'country' => 'CN',
                'postalCode' => '510623',
            ],
        ],

        // 支付信息
        'payment' => [
            'cardType' => 'VISA',
            'cardNumber' => '****************',
            'cardHolder' => '王五',
            'expiryMonth' => 9,
            'expiryYear' => 2028,
        ],

        // 可选参数
        'loyaltyNumber' => 'GHA999888777',
        'loyaltyLevel' => 'PLATINUM',
        'promoCode' => 'UPGRADE2024',
        'specialRequests' => '家庭出行，需要相邻房间，其中一间需要连通',
        'sendConfirmationEmail' => true,
    ];

    return callApi('POST', '/sabre/reservation', $data);
}

/**
 * 多房间创建示例（统一格式 - 使用status字段）
 */
function multiRoomCreateExampleUnified()
{
    global $baseUrl, $apiKey;

    $data = [
        // 操作类型
        'status' => 'create',

        // 基本预订信息（与修改格式一致）
        'hotelId' => 12345,
        'checkInDate' => '2024-12-15',
        'checkOutDate' => '2024-12-18',

        // 多个房间信息（与修改格式一致，不需要confirmationNumber）
        'rooms' => [
            // 房间1
            [
                'roomCode' => 'STD',
                'rateCode' => 'MEMBER_RATE',
                'adults' => 2,
                'children' => 2,
                'childrenAges' => [8, 12],
                'specialRequests' => '需要加床',
            ],
            // 房间2
            [
                'roomCode' => 'DLX',
                'rateCode' => 'PROMO_RATE',
                'adults' => 2,
                'children' => 0,
                'childrenAges' => [],
                'specialRequests' => '高层房间，海景',
            ],
            // 房间3
            [
                'roomCode' => 'STE',
                'rateCode' => 'SUITE_RATE',
                'adults' => 3,
                'children' => 1,
                'childrenAges' => [5],
                'specialRequests' => '套房，需要沙发床',
            ],
        ],

        // 主要联系人信息
        'primaryGuest' => [
            'firstName' => '李',
            'lastName' => '四',
            'email' => '<EMAIL>',
            'phone' => '+86-13900139000',
            'address' => [
                'line1' => '上海市浦东新区世纪大道1000号',
                'city' => '上海',
                'state' => '上海',
                'country' => 'CN',
                'postalCode' => '200120',
            ],
        ],

        // 支付信息
        'payment' => [
            'cardType' => 'MASTERCARD',
            'cardNumber' => '****************',
            'cardHolder' => '李四',
            'expiryMonth' => 6,
            'expiryYear' => 2027,
        ],

        // 可选参数
        'loyaltyNumber' => 'GHA987654321',
        'loyaltyLevel' => 'GOLD',
        'promoCode' => 'FAMILY2024',
        'specialRequests' => '家庭出行，需要相邻房间',
        'sendConfirmationEmail' => true,
    ];

    return callApi('POST', '/sabre/reservation', $data);
}

/**
 * 多房间取消示例（统一格式 - 使用status字段）
 */
function multiRoomCancelExampleUnified()
{
    global $baseUrl, $apiKey;

    $data = [
        // 操作类型
        'status' => 'cancel',

        // 取消预订必需的行程号
        'itineraryNumber' => 'ITN987654321',

        // 基本预订信息（取消时可选，用于确认）
        'hotelId' => 12345,

        // 主要联系人信息（取消时可选）
        'primaryGuest' => [
            'firstName' => '李',
            'lastName' => '四',
            'email' => '<EMAIL>',
            'phone' => '+86-13900139000',
        ],

        // 支付信息（取消时可选）
        'payment' => [
            'cardType' => 'MASTERCARD',
            'cardNumber' => '****************',
            'cardHolder' => '李四',
            'expiryMonth' => 6,
            'expiryYear' => 2027,
        ],
    ];

    return callApi('POST', '/sabre/reservation', $data);
}

/**
 * 部分房间修改示例
 */
function partialRoomModificationExample()
{
    global $baseUrl, $apiKey;

    $data = [
        'itineraryNumber' => 'ITN456789123',
        'hotelId' => 12345,
        'checkInDate' => '2024-12-20',
        'checkOutDate' => '2024-12-23',

        // 只修改其中2个房间
        'rooms' => [
            [
                'roomCode' => 'DLX',
                'rateCode' => 'UPGRADE_RATE',
                'adults' => 2,
                'children' => 0,
                'confirmationNumber' => 'ROOM001', // 升级房间1
                'specialRequests' => '升级到豪华房',
            ],
            [
                'roomCode' => 'STE',
                'rateCode' => 'VIP_RATE',
                'adults' => 2,
                'children' => 1,
                'childrenAges' => [7],
                'confirmationNumber' => 'ROOM003', // 升级房间3到套房
                'specialRequests' => '升级到套房，VIP待遇',
            ],
        ],

        'primaryGuest' => [
            'firstName' => '王',
            'lastName' => '五',
            'email' => '<EMAIL>',
            'phone' => '+86-13700137000',
        ],

        'payment' => [
            'cardType' => 'AMEX',
            'cardNumber' => '***************',
            'cardHolder' => '王五',
            'expiryMonth' => 9,
            'expiryYear' => 2025,
        ],

        'loyaltyNumber' => 'GHA555666777',
        'specialRequests' => '庆祝结婚纪念日，需要房间装饰',
    ];

    return callApi('POST', '/sabre/modify-reservation', $data);
}

/**
 * HTTP API调用辅助函数
 */
function callApi($method, $endpoint, $data = [])
{
    global $baseUrl, $apiKey;

    $url = $baseUrl.$endpoint;
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json',
    ];

    if ($apiKey) {
        $headers[] = 'Authorization: Bearer '.$apiKey;
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true),
        'raw_response' => $response,
    ];
}

/**
 * 运行示例
 */
function runExamples()
{
    echo "=== GHA 多房间预订修改API示例 ===\n\n";

    // 单房间修改示例
    echo "1. 单房间修改示例:\n";
    $result1 = singleRoomModificationExample();
    echo 'HTTP状态码: '.$result1['http_code']."\n";
    echo '响应内容: '.json_encode($result1['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";

    // 多房间修改示例
    echo "2. 多房间修改示例:\n";
    $result2 = multiRoomModificationExample();
    echo 'HTTP状态码: '.$result2['http_code']."\n";
    echo '响应内容: '.json_encode($result2['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";

    // 部分房间修改示例
    echo "3. 部分房间修改示例:\n";
    $result3 = partialRoomModificationExample();
    echo 'HTTP状态码: '.$result3['http_code']."\n";
    echo '响应内容: '.json_encode($result3['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";
}

/**
 * 响应格式说明
 */
function responseFormatExplanation()
{
    echo "=== 响应格式说明 ===\n\n";

    echo "单房间修改成功响应示例:\n";
    $singleRoomResponse = [
        'success' => true,
        'message' => '修改成功',
        'data' => [
            'success' => true,
            'confirmation_number' => 'CONF123456',
            'itinerary_number' => 'ITN987654321',
            'status' => 'Confirmed',
        ],
    ];
    echo json_encode($singleRoomResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";

    echo "多房间修改成功响应示例:\n";
    $multiRoomResponse = [
        'success' => true,
        'message' => '修改成功',
        'data' => [
            'success' => true,
            'itinerary_number' => 'ITN987654321',
            'total_rooms' => 3,
            'success_count' => 3,
            'failed_count' => 0,
            'rooms' => [
                [
                    'success' => true,
                    'room_index' => 0,
                    'confirmation_number' => 'ROOM001',
                ],
                [
                    'success' => true,
                    'room_index' => 1,
                    'confirmation_number' => 'ROOM002',
                ],
                [
                    'success' => true,
                    'room_index' => 2,
                    'confirmation_number' => 'ROOM003',
                ],
            ],
        ],
    ];
    echo json_encode($multiRoomResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";

    echo "部分成功响应示例:\n";
    $partialSuccessResponse = [
        'success' => true,
        'message' => '修改成功',
        'data' => [
            'success' => true,
            'itinerary_number' => 'ITN456789123',
            'total_rooms' => 2,
            'success_count' => 1,
            'failed_count' => 1,
            'rooms' => [
                [
                    'success' => true,
                    'room_index' => 0,
                    'confirmation_number' => 'ROOM001',
                ],
                [
                    'success' => false,
                    'room_index' => 1,
                    'error' => '房间不可用或价格已变更',
                ],
            ],
        ],
    ];
    echo json_encode($partialSuccessResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";
}

// 如果直接运行此文件，则执行示例
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    runExamples();
    responseFormatExplanation();
}
