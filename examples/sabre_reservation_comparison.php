<?php

/**
 * Sabre预订接口优化前后对比示例
 *
 * 此文件展示了优化前后预订接口参数的差异
 */

// ============================================================================
// 优化前的复杂参数结构（客户端需要传递）
// ============================================================================

$oldComplexParams = [
    "hotelId" => 100823,
    "chainId" => 32446, // 固定参数，现在由后端处理
    "guests" => [
        [
            "PersonName" => [
                "GivenName" => "张",
                "Surname" => "三"
            ],
            "EmailAddress" => [
                [
                    "Type" => "Primary",
                    "Value" => "<EMAIL>"
                ]
            ],
            "ContactNumbers" => [
                [
                    "Number" => "13800138000",
                    "Role" => "Home",
                    "Type" => "Mobile",
                    "Use" => "DayTimeContact",
                    "Default" => true
                ]
            ],
            "Locations" => [
                [
                    "Address" => [
                        "AddressLine" => ["北京市朝阳区某某街道123号"],
                        "City" => "北京",
                        "Country" => [
                            "Code" => "CN",
                            "Value" => "中国"
                        ],
                        "PostalCode" => "100000",
                        "StateProv" => [
                            "Code" => "BJ",
                            "Value" => "北京"
                        ],
                        "Type" => "Home",
                        "Default" => true
                    ],
                    "Name" => "Home"
                ]
            ],
            "Payments" => [
                [
                    "Amount" => 0,
                    "PaymentCard" => [
                        "CardCode" => "VI",
                        "CardHolder" => "ZHANG SAN",
                        "CardNumber" => "****************",
                        "ExpireDate" => "1225" // 特殊格式：MMYY
                    ],
                    "Role" => "Primary",
                    "Type" => "CreditCard"
                ]
            ]
        ]
    ],
    "roomStay" => [
        "StartDate" => "2025-07-09T00:00:00", // 需要UTC ISO格式
        "EndDate" => "2025-07-11T00:00:00",   // 需要UTC ISO格式
        "NumRooms" => 1,
        "GuestCount" => [
            [
                "AgeQualifyingCode" => "Adult",
                "NumGuests" => 2
            ],
            [
                "AgeQualifyingCode" => "Child",
                "NumGuests" => 1,
                "Ages" => [8]
            ]
        ],
        "Products" => [
            [
                "StartDate" => "2025-07-09T00:00:00", // 重复的时间信息
                "EndDate" => "2025-07-11T00:00:00",   // 重复的时间信息
                "Primary" => true,
                "Product" => [
                    "RateCode" => "BAR",
                    "RoomCode" => "STD"
                ]
            ]
        ]
    ],
    "loyaltyMemberships" => [
        [
            "Program" => "GHA", // 固定参数
            "Number" => "GHA123456789",
            "Level" => "RED"     // 固定参数
        ]
    ],
    "withMembership" => true,
    "withPromo" => false
];

// ============================================================================
// 优化后的简化参数结构（客户端只需传递核心信息）
// ============================================================================

$newSimplifiedParams = [
    // 基本预订信息
    "hotelId" => 100823,
    "checkInDate" => "2025-07-09",    // 简单日期格式
    "checkOutDate" => "2025-07-11",   // 简单日期格式
    "numRooms" => 1,
    "adults" => 2,
    "children" => 1,
    "childrenAges" => [8],

    // 房型和价格信息
    "roomCode" => "STD",
    "rateCode" => "BAR",

    // 主要客人信息（扁平化结构）
    "primaryGuest" => [
        "firstName" => "张",
        "lastName" => "三",
        "email" => "<EMAIL>",
        "phone" => "13800138000",
        "address" => [
            "line1" => "北京市朝阳区某某街道123号",
            "city" => "北京",
            "state" => "北京",
            "country" => "CN",
            "postalCode" => "100000"
        ]
    ],

    // 支付信息（简化格式）
    "payment" => [
        "cardType" => "VI",
        "cardNumber" => "****************",
        "cardHolder" => "ZHANG SAN",
        "expiryMonth" => 12,  // 标准月份格式
        "expiryYear" => 2025  // 标准年份格式
    ],

    // 可选参数
    "loyaltyNumber" => "GHA123456789",
    "promoCode" => null,
    "specialRequests" => "高层房间，安静环境",
    "sendConfirmationEmail" => true
];

// ============================================================================
// 参数复杂度对比
// ============================================================================

function countParameters($array, $prefix = '') {
    $count = 0;
    foreach ($array as $key => $value) {
        if (is_array($value)) {
            $count += countParameters($value, $prefix . $key . '.');
        } else {
            $count++;
        }
    }
    return $count;
}

$oldParamCount = countParameters($oldComplexParams);
$newParamCount = countParameters($newSimplifiedParams);
$reduction = round((($oldParamCount - $newParamCount) / $oldParamCount) * 100, 1);

echo "=== Sabre预订接口优化对比 ===\n";
echo "优化前参数数量: {$oldParamCount}\n";
echo "优化后参数数量: {$newParamCount}\n";
echo "复杂度减少: {$reduction}%\n\n";

echo "=== 主要改进点 ===\n";
echo "1. 固定参数后端化: chainId、渠道信息、语言设置等\n";
echo "2. 结构扁平化: 减少嵌套层级\n";
echo "3. 格式简化: 日期、支付卡格式自动转换\n";
echo "4. 智能默认值: 自动填充系统配置参数\n";
echo "5. 向后兼容: 保留原有接口，新增简化接口\n\n";

echo "=== 使用示例 ===\n";
echo "// 优化前 - 复杂的API调用\n";
echo "curl -X POST /api/v1/sabre/reservation \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($oldComplexParams, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "'\n\n";

echo "// 优化后 - 简化的API调用\n";
echo "curl -X POST /api/v1/sabre/createReservation \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($newSimplifiedParams, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "'\n\n";

// ============================================================================
// 后端自动处理的固定参数
// ============================================================================

$autoHandledParams = [
    'chainId' => 32446,
    'channels' => [
        'primaryChannel' => 'SYDC',
        'secondaryChannel' => 'DSCVRYLYLTY'
    ],
    'language' => 'zh-CN',
    'marketSource' => 'GHA',
    'entryChannelCode' => 'GHA',
    'subSourceCode' => 'GHA',
    'context' => 'WBSVC',
    'loyaltyProgram' => 'GHA',
    'defaultLoyaltyLevel' => 'RED'
];

echo "=== 后端自动处理的固定参数 ===\n";
echo json_encode($autoHandledParams, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
