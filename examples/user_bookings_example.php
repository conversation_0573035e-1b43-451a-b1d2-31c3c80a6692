<?php

/**
 * 用户预订列表接口示例
 *
 * 接口路径: GET /api/v1/user/bookings
 * 需要认证: 是 (Bearer Token)
 *
 * 此接口使用GXP服务获取当前登录用户的预订列表
 */

// 基本用法 - 获取所有预订
$example1 = [
    'url' => '/api/v1/user/bookings',
    'method' => 'GET',
    'headers' => [
        'Authorization' => 'Bearer {token}',
        'Accept' => 'application/json',
    ],
    'query_params' => [],
];

// 分页查询
$example2 = [
    'url' => '/api/v1/user/bookings',
    'method' => 'GET',
    'headers' => [
        'Authorization' => 'Bearer {token}',
        'Accept' => 'application/json',
    ],
    'query_params' => [
        'page' => 0,        // 页码 (从0开始)
        'size' => 20,       // 每页数量 (1-100)
    ],
];

// 排序查询
$example3 = [
    'url' => '/api/v1/user/bookings',
    'method' => 'GET',
    'headers' => [
        'Authorization' => 'Bearer {token}',
        'Accept' => 'application/json',
    ],
    'query_params' => [
        'sort_by' => 'ArrivalDate',     // 排序字段
        'sort_direction' => 'desc',     // 排序方向 (asc/desc)
    ],
];

// 状态过滤
$example4 = [
    'url' => '/api/v1/user/bookings',
    'method' => 'GET',
    'headers' => [
        'Authorization' => 'Bearer {token}',
        'Accept' => 'application/json',
    ],
    'query_params' => [
        'status' => 'CURRENT,UPCOMING', // 状态过滤 (逗号分隔)
        // 或者使用数组: 'status' => ['CURRENT', 'UPCOMING']
        // 可用状态: CURRENT, UPCOMING, PAST, CANCELED
    ],
];

// 日期范围过滤
$example5 = [
    'url' => '/api/v1/user/bookings',
    'method' => 'GET',
    'headers' => [
        'Authorization' => 'Bearer {token}',
        'Accept' => 'application/json',
    ],
    'query_params' => [
        'start_date' => '2024-01-01',   // 开始日期 (YYYY-MM-DD)
        'end_date' => '2024-12-31',     // 结束日期 (YYYY-MM-DD)
    ],
];

// 酒店过滤
$example6 = [
    'url' => '/api/v1/user/bookings',
    'method' => 'GET',
    'headers' => [
        'Authorization' => 'Bearer {token}',
        'Accept' => 'application/json',
    ],
    'query_params' => [
        'hotel_id' => 100820,           // 酒店ID过滤
    ],
];

// 完整参数示例
$example7 = [
    'url' => '/api/v1/user/bookings',
    'method' => 'GET',
    'headers' => [
        'Authorization' => 'Bearer {token}',
        'Accept' => 'application/json',
    ],
    'query_params' => [
        'page' => 0,
        'size' => 10,
        'sort_by' => 'ArrivalDate',
        'sort_direction' => 'desc',
        'status' => 'CURRENT,UPCOMING',
        'start_date' => '2024-01-01',
        'end_date' => '2024-12-31',
        'hotel_id' => 100820,
    ],
];

// 成功响应示例
$success_response = [
    'code' => 200,
    'message' => '获取预订列表成功',
    'data' => [
        'total' => 1,
        'page' => 0,
        'size' => 20,
        'reservations' => [
            [
                'confirmation_number' => '100820CK000470',
                'hotel_code' => 'CH0001',
                'arrival_date_time' => '2025-10-01T00:00:00',
                'departure_date_time' => '2025-10-02T00:00:00',
                'source' => 'GHA',
                'status' => 'UPCOMING',
                'stay_eligibility' => true,
                'number_of_adults' => 2,
                'number_of_children' => 1,
                'cancellation_numbers' => [],
                'number_of_rooms' => 1,
                'total_price' => 444.72,
                'currency' => 'USD',
                'profile_id' => '0',
                'has_full_details' => true,
                'discovery_dollars' => 0,
                'hotel_id' => '100820',
                'chain_id' => '32446',
                'itinerary_number' => '32446B0003226',
                // 关联的酒店详细信息
                'hotel' => [
                    'id' => 1,
                    'code' => 'CH0001',
                    'synxis_hotel_id' => '100820',
                    'hotel_name' => '上海浦东丽思卡尔顿酒店',
                    'hotel_name_en' => 'The Ritz-Carlton Shanghai, Pudong',
                    'country_code' => 'CN',
                    'city_name' => '上海',
                    'brand_code' => 'RC',
                    'brand_name' => 'The Ritz-Carlton',
                    'address' => '上海市浦东新区陆家嘴环路1717号',
                    'latitude' => 31.2304,
                    'longitude' => 121.4737,
                    'star_rating' => 5,
                    'phone' => '+86 21 2020 1888',
                    'images' => [
                        'https://example.com/hotel/image1.jpg',
                        'https://example.com/hotel/image2.jpg',
                    ],
                    'head_line' => '顶级奢华酒店',
                    'description' => '酒店详细描述...',
                    // ... 其他酒店信息
                ],
            ],
            // ... 更多预订
        ],
    ],
];

// 错误响应示例
$error_responses = [
    // 未登录
    [
        'code' => 401,
        'message' => '请先登录',
    ],

    // GXP服务未配置
    [
        'code' => 500,
        'message' => 'GXP服务未配置，请联系管理员',
    ],

    // 获取失败
    [
        'code' => 500,
        'message' => '获取预订列表失败: 具体错误信息',
    ],
];

// 使用说明
echo "用户预订列表接口 (GXP服务)\n";
echo "================================\n\n";

echo "接口地址: GET /api/v1/user/bookings\n";
echo "认证方式: Bearer Token (必需)\n";
echo "功能描述: 获取当前登录用户的预订列表，使用GXP服务\n\n";

echo "支持的查询参数:\n";
echo "- page: 页码 (从0开始，默认0)\n";
echo "- size: 每页数量 (1-100，默认20)\n";
echo "- sort_by: 排序字段 (默认ArrivalDate)\n";
echo "- sort_direction: 排序方向 (asc/desc，默认desc)\n";
echo "- status: 状态过滤 (CURRENT,UPCOMING,PAST,CANCELED)\n";
echo "- start_date: 开始日期 (YYYY-MM-DD)\n";
echo "- end_date: 结束日期 (YYYY-MM-DD)\n";
echo "- hotel_id: 酒店ID过滤\n\n";

echo "注意事项:\n";
echo "1. 此接口需要用户登录，必须提供有效的Bearer Token\n";
echo "2. 使用GXP服务获取数据，需要GXP服务正确配置\n";
echo "3. 返回的数据格式由GXP服务决定\n";
echo "4. 状态过滤可以使用逗号分隔的字符串或数组\n";
echo "5. 分页从0开始，每页最多100条记录\n";
echo "6. 每个预订会自动关联对应的酒店详细信息\n";
echo "7. 如果酒店信息查询失败，会在hotel字段中包含错误信息\n";
echo "8. 酒店信息通过hotel_id关联查询，包含酒店名称、地址、图片等完整信息\n";
