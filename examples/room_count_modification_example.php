<?php

/**
 * 房间数量变化修改API使用示例
 *
 * 本示例展示了如何使用GHA后端系统处理房间数量的增加和减少
 */

require_once __DIR__.'/../vendor/autoload.php';

// API基本配置
$baseUrl = 'https://your-api-domain.com/api/v1';
$apiKey = 'your-api-key'; // 如果需要API密钥

/**
 * 示例1：增加房间
 * 原本预订了2个房间，现在要增加到4个房间
 */
function addRoomsExample()
{
    global $baseUrl, $apiKey;

    $data = [
        'itineraryNumber' => 'ITN123456789',
        'hotelId' => 12345,
        'hotelCode' => 'HOTEL001',
        'checkInDate' => '2024-12-20',
        'checkOutDate' => '2024-12-23',

        // 当前房间列表（需要修改的现有房间）
        'currentRooms' => [
            [
                'confirmationNumber' => 'ROOM001',
                'roomCode' => 'STD',
                'rateCode' => 'MEMBER_RATE',
            ],
            [
                'confirmationNumber' => 'ROOM002',
                'roomCode' => 'STD',
                'rateCode' => 'MEMBER_RATE',
            ],
        ],

        // 目标房间列表（包括修改后的现有房间和新增的房间）
        'targetRooms' => [
            // 修改第一个房间（升级到豪华房）
            [
                'confirmationNumber' => 'ROOM001', // 有确认号表示这是现有房间
                'roomCode' => 'DLX',
                'rateCode' => 'UPGRADE_RATE',
                'adults' => 2,
                'children' => 1,
                'childrenAges' => [10],
                'specialRequests' => '升级到豪华房间',
            ],
            // 保持第二个房间不变
            [
                'confirmationNumber' => 'ROOM002',
                'roomCode' => 'STD',
                'rateCode' => 'MEMBER_RATE',
                'adults' => 2,
                'children' => 0,
                'childrenAges' => [],
            ],
            // 新增第三个房间
            [
                // 没有确认号表示这是新房间
                'roomCode' => 'STE',
                'rateCode' => 'SUITE_RATE',
                'adults' => 3,
                'children' => 2,
                'childrenAges' => [8, 12],
                'specialRequests' => '新增套房',
            ],
            // 新增第四个房间
            [
                'roomCode' => 'STD',
                'rateCode' => 'STANDARD_RATE',
                'adults' => 2,
                'children' => 0,
                'childrenAges' => [],
                'specialRequests' => '新增标准房',
            ],
        ],

        // 主要联系人信息
        'primaryGuest' => [
            'firstName' => '张',
            'lastName' => '三',
            'email' => '<EMAIL>',
            'phone' => '+86-13800138000',
            'address' => [
                'line1' => '北京市朝阳区建国路88号',
                'city' => '北京',
                'state' => '北京',
                'country' => 'CN',
                'postalCode' => '100025',
            ],
        ],

        // 支付信息
        'payment' => [
            'cardType' => 'VISA',
            'cardNumber' => '****************',
            'cardHolder' => '张三',
            'expiryMonth' => 12,
            'expiryYear' => 2026,
        ],

        'loyaltyNumber' => 'GHA123456789',
        'promoCode' => 'FAMILY2024',
        'specialRequests' => '家庭旅行，需要相邻房间',
        'sendConfirmationEmail' => true,
    ];

    return callApi('POST', '/sabre/modifyReservationWithRoomChanges', $data);
}

/**
 * 示例2：减少房间
 * 原本预订了4个房间，现在要减少到2个房间
 */
function removeRoomsExample()
{
    global $baseUrl, $apiKey;

    $data = [
        'itineraryNumber' => 'ITN987654321',
        'hotelId' => 12345,
        'hotelCode' => 'HOTEL001',
        'checkInDate' => '2024-12-25',
        'checkOutDate' => '2024-12-28',

        // 当前房间列表（所有现有房间）
        'currentRooms' => [
            [
                'confirmationNumber' => 'ROOM101',
                'roomCode' => 'STD',
                'rateCode' => 'STANDARD_RATE',
            ],
            [
                'confirmationNumber' => 'ROOM102',
                'roomCode' => 'DLX',
                'rateCode' => 'DELUXE_RATE',
            ],
            [
                'confirmationNumber' => 'ROOM103',
                'roomCode' => 'STE',
                'rateCode' => 'SUITE_RATE',
            ],
            [
                'confirmationNumber' => 'ROOM104',
                'roomCode' => 'STD',
                'rateCode' => 'STANDARD_RATE',
            ],
        ],

        // 目标房间列表（只保留需要的房间）
        'targetRooms' => [
            // 保留并修改第一个房间
            [
                'confirmationNumber' => 'ROOM101',
                'roomCode' => 'DLX', // 升级房型
                'rateCode' => 'UPGRADE_RATE',
                'adults' => 2,
                'children' => 0,
                'specialRequests' => '升级房型',
            ],
            // 保留第二个房间但修改入住人数
            [
                'confirmationNumber' => 'ROOM102',
                'roomCode' => 'DLX',
                'rateCode' => 'DELUXE_RATE',
                'adults' => 3, // 增加成人数
                'children' => 1, // 增加儿童
                'childrenAges' => [6],
                'specialRequests' => '增加客人',
            ],
            // ROOM103 和 ROOM104 不在目标列表中，将被自动取消
        ],

        'primaryGuest' => [
            'firstName' => '李',
            'lastName' => '四',
            'email' => '<EMAIL>',
            'phone' => '+86-13900139000',
        ],

        'payment' => [
            'cardType' => 'MASTERCARD',
            'cardNumber' => '****************',
            'cardHolder' => '李四',
            'expiryMonth' => 6,
            'expiryYear' => 2027,
        ],

        'loyaltyNumber' => 'GHA987654321',
        'specialRequests' => '减少房间数量，节省成本',
    ];

    return callApi('POST', '/sabre/modifyReservationWithRoomChanges', $data);
}

/**
 * 示例3：复杂的房间替换
 * 取消一些房间，修改一些房间，添加一些新房间
 */
function complexRoomChangesExample()
{
    global $baseUrl, $apiKey;

    $data = [
        'itineraryNumber' => 'ITN456789123',
        'hotelId' => 12345,
        'hotelCode' => 'HOTEL001',
        'checkInDate' => '2024-12-30',
        'checkOutDate' => '2025-01-02',

        // 当前房间列表
        'currentRooms' => [
            [
                'confirmationNumber' => 'ROOM201',
                'roomCode' => 'STD',
                'rateCode' => 'STANDARD_RATE',
            ],
            [
                'confirmationNumber' => 'ROOM202',
                'roomCode' => 'STD',
                'rateCode' => 'STANDARD_RATE',
            ],
            [
                'confirmationNumber' => 'ROOM203',
                'roomCode' => 'DLX',
                'rateCode' => 'DELUXE_RATE',
            ],
        ],

        // 目标房间列表
        'targetRooms' => [
            // 修改 ROOM201
            [
                'confirmationNumber' => 'ROOM201',
                'roomCode' => 'STE', // 升级到套房
                'rateCode' => 'SUITE_RATE',
                'adults' => 2,
                'children' => 2,
                'childrenAges' => [5, 9],
                'specialRequests' => '升级到套房，家庭入住',
            ],
            // ROOM202 将被取消（不在目标列表中）
            // ROOM203 将被取消（不在目标列表中）

            // 添加新的豪华房间
            [
                'roomCode' => 'DLX',
                'rateCode' => 'VIP_RATE',
                'adults' => 2,
                'children' => 0,
                'specialRequests' => '新增VIP豪华房',
            ],
            // 添加另一个标准房间
            [
                'roomCode' => 'STD',
                'rateCode' => 'PROMO_RATE',
                'adults' => 1,
                'children' => 0,
                'specialRequests' => '单人入住促销价',
            ],
        ],

        'primaryGuest' => [
            'firstName' => '王',
            'lastName' => '五',
            'email' => '<EMAIL>',
            'phone' => '+86-13700137000',
        ],

        'payment' => [
            'cardType' => 'AMEX',
            'cardNumber' => '***************',
            'cardHolder' => '王五',
            'expiryMonth' => 9,
            'expiryYear' => 2026,
        ],

        'loyaltyNumber' => 'GHA555666777',
        'promoCode' => 'NEWYEAR2025',
        'specialRequests' => '新年特别安排，需要套房+标准房组合',
    ];

    return callApi('POST', '/sabre/modifyReservationWithRoomChanges', $data);
}

/**
 * HTTP API调用辅助函数
 */
function callApi($method, $endpoint, $data = [])
{
    global $baseUrl, $apiKey;

    $url = $baseUrl.$endpoint;
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json',
    ];

    if ($apiKey) {
        $headers[] = 'Authorization: Bearer '.$apiKey;
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true),
        'raw_response' => $response,
    ];
}

/**
 * 运行示例
 */
function runExamples()
{
    echo "=== GHA 房间数量变化修改API示例 ===\n\n";

    // 示例1：增加房间
    echo "1. 增加房间示例（2房间 → 4房间）:\n";
    $result1 = addRoomsExample();
    echo 'HTTP状态码: '.$result1['http_code']."\n";
    echo '响应内容: '.json_encode($result1['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";

    // 示例2：减少房间
    echo "2. 减少房间示例（4房间 → 2房间）:\n";
    $result2 = removeRoomsExample();
    echo 'HTTP状态码: '.$result2['http_code']."\n";
    echo '响应内容: '.json_encode($result2['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";

    // 示例3：复杂房间变化
    echo "3. 复杂房间变化示例（替换+修改+新增）:\n";
    $result3 = complexRoomChangesExample();
    echo 'HTTP状态码: '.$result3['http_code']."\n";
    echo '响应内容: '.json_encode($result3['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";
}

/**
 * 响应格式说明
 */
function responseFormatExplanation()
{
    echo "=== 响应格式说明 ===\n\n";

    echo "房间数量变化成功响应示例:\n";
    $successResponse = [
        'success' => true,
        'message' => '修改成功',
        'data' => [
            'success' => true,
            'itinerary_number' => 'ITN123456789',
            'modifications' => [
                [
                    'success' => true,
                    'room_index' => 0,
                    'confirmation_number' => 'ROOM001',
                    'operation' => 'modify',
                    'result' => ['success' => true, 'confirmation_number' => 'ROOM001'],
                ],
                [
                    'success' => true,
                    'room_index' => 1,
                    'confirmation_number' => 'ROOM002',
                    'operation' => 'modify',
                    'result' => ['success' => true, 'confirmation_number' => 'ROOM002'],
                ],
            ],
            'cancellations' => [
                [
                    'success' => true,
                    'confirmation_number' => 'ROOM203',
                    'operation' => 'cancel',
                    'result' => ['success' => true, 'status' => 'Cancelled'],
                ],
            ],
            'additions' => [
                [
                    'success' => true,
                    'room_index' => 2,
                    'operation' => 'add',
                    'confirmation_number' => 'ROOM301',
                    'result' => ['success' => true, 'confirmation_number' => 'ROOM301'],
                ],
                [
                    'success' => true,
                    'room_index' => 3,
                    'operation' => 'add',
                    'confirmation_number' => 'ROOM302',
                    'result' => ['success' => true, 'confirmation_number' => 'ROOM302'],
                ],
            ],
            'summary' => [
                'total_operations' => 5,
                'successful_operations' => 5,
                'failed_operations' => 0,
            ],
        ],
    ];
    echo json_encode($successResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";

    echo "部分成功响应示例:\n";
    $partialSuccessResponse = [
        'success' => true,
        'message' => '修改成功',
        'data' => [
            'success' => true,
            'itinerary_number' => 'ITN456789123',
            'modifications' => [
                [
                    'success' => true,
                    'room_index' => 0,
                    'confirmation_number' => 'ROOM201',
                    'operation' => 'modify',
                    'result' => ['success' => true, 'confirmation_number' => 'ROOM201'],
                ],
            ],
            'cancellations' => [
                [
                    'success' => true,
                    'confirmation_number' => 'ROOM202',
                    'operation' => 'cancel',
                    'result' => ['success' => true, 'status' => 'Cancelled'],
                ],
                [
                    'success' => false,
                    'confirmation_number' => 'ROOM203',
                    'operation' => 'cancel',
                    'error' => '房间无法取消，已过取消期限',
                ],
            ],
            'additions' => [
                [
                    'success' => false,
                    'room_index' => 1,
                    'operation' => 'add',
                    'error' => '该房型已售完',
                ],
            ],
            'summary' => [
                'total_operations' => 4,
                'successful_operations' => 2,
                'failed_operations' => 2,
            ],
        ],
    ];
    echo json_encode($partialSuccessResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)."\n\n";
}

/**
 * 使用说明
 */
function usageInstructions()
{
    echo "=== 使用说明 ===\n\n";

    echo "1. API端点: POST /api/v1/sabre/modifyReservationWithRoomChanges\n\n";

    echo "2. 关键参数说明:\n";
    echo "   - itineraryNumber: 必填，现有预订的行程号\n";
    echo "   - currentRooms: 必填，当前所有房间的列表（包含confirmationNumber）\n";
    echo "   - targetRooms: 必填，期望的最终房间配置\n";
    echo "     * 有confirmationNumber：修改现有房间\n";
    echo "     * 无confirmationNumber：添加新房间\n";
    echo "     * 不在targetRooms中的currentRooms：将被取消\n\n";

    echo "3. 操作逻辑:\n";
    echo "   - 系统会自动比较currentRooms和targetRooms\n";
    echo "   - 对于每个targetRooms中有confirmationNumber的房间，执行修改操作\n";
    echo "   - 对于每个targetRooms中没有confirmationNumber的房间，执行添加操作\n";
    echo "   - 对于currentRooms中不在targetRooms里的房间，执行取消操作\n\n";

    echo "4. 响应结构:\n";
    echo "   - modifications: 修改操作的结果\n";
    echo "   - cancellations: 取消操作的结果\n";
    echo "   - additions: 添加操作的结果\n";
    echo "   - summary: 操作汇总统计\n\n";

    echo "5. 注意事项:\n";
    echo "   - 所有操作按顺序执行：修改 → 取消 → 添加\n";
    echo "   - 部分操作失败不会影响其他操作的执行\n";
    echo "   - 新增房间会使用现有的行程号\n";
    echo "   - 建议在操作前先查询当前预订状态\n\n";
}

// 如果直接运行此文件，则执行示例
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    usageInstructions();
    runExamples();
    responseFormatExplanation();
}
