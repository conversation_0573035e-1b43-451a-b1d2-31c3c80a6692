<?php

namespace Tests\Feature;

use Tests\TestCase;

class MultiRoomReservationModificationTest extends TestCase
{
    /**
     * 测试单房间修改（保持向后兼容性）
     */
    public function test_single_room_modification(): void
    {
        $response = $this->postJson('/api/v1/sabre/modify-reservation', [
            'CRS_confirmationNumber' => 'TEST123456',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'numRooms' => 1,
            'adults' => 2,
            'children' => 0,
            'childrenAges' => [],
            'roomCode' => 'STD',
            'rateCode' => 'RATE001',
            'primaryGuest' => [
                'firstName' => '张',
                'lastName' => '三',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
                'address' => [
                    'line1' => '北京市朝阳区建国路88号',
                    'city' => '北京',
                    'state' => '北京',
                    'country' => 'CN',
                    'postalCode' => '100025',
                ],
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '张三',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
            'loyaltyNumber' => 'GHA123456789',
            'specialRequests' => '靠近电梯',
            'sendConfirmationEmail' => true,
        ]);

        // 注意：由于这是集成测试，实际的API调用可能会失败
        // 在真实环境中，你可能需要模拟Sabre API响应
        $response->assertStatus(200);
    }

    /**
     * 测试多房间修改功能
     */
    public function test_multi_room_modification(): void
    {
        $response = $this->postJson('/api/v1/sabre/modify-reservation', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 1,
                    'childrenAges' => [8],
                    'confirmationNumber' => 'ROOM001',
                    'specialRequests' => '靠近电梯',
                ],
                [
                    'roomCode' => 'DLX',
                    'rateCode' => 'RATE002',
                    'adults' => 2,
                    'children' => 0,
                    'childrenAges' => [],
                    'confirmationNumber' => 'ROOM002',
                    'specialRequests' => '高层房间',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '李',
                'lastName' => '四',
                'email' => '<EMAIL>',
                'phone' => '+86-13900139000',
                'address' => [
                    'line1' => '上海市浦东新区世纪大道1000号',
                    'city' => '上海',
                    'state' => '上海',
                    'country' => 'CN',
                    'postalCode' => '200120',
                ],
            ],
            'payment' => [
                'cardType' => 'MASTERCARD',
                'cardNumber' => '****************',
                'cardHolder' => '李四',
                'expiryMonth' => 6,
                'expiryYear' => 2027,
            ],
            'loyaltyNumber' => 'GHA987654321',
            'promoCode' => 'PROMO2024',
            'specialRequests' => '需要无障碍房间',
            'sendConfirmationEmail' => true,
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试缺少行程号时的多房间修改验证
     */
    public function test_multi_room_modification_without_itinerary_number(): void
    {
        $response = $this->postJson('/api/v1/sabre/modify-reservation', [
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 0,
                    'confirmationNumber' => 'ROOM001',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '王',
                'lastName' => '五',
                'email' => '<EMAIL>',
                'phone' => '+86-13700137000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '王五',
                'expiryMonth' => 3,
                'expiryYear' => 2025,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['itineraryNumber']);
    }

    /**
     * 测试房间缺少确认号时的验证
     */
    public function test_multi_room_modification_without_room_confirmation_number(): void
    {
        $response = $this->postJson('/api/v1/sabre/modify-reservation', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 0,
                    // 缺少 confirmationNumber
                ],
            ],
            'primaryGuest' => [
                'firstName' => '赵',
                'lastName' => '六',
                'email' => '<EMAIL>',
                'phone' => '+86-13600136000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '赵六',
                'expiryMonth' => 9,
                'expiryYear' => 2025,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['rooms.0.confirmationNumber']);
    }

    /**
     * 测试儿童年龄验证
     */
    public function test_multi_room_modification_children_age_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modify-reservation', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 2,
                    'childrenAges' => [8], // 只提供了1个年龄，但有2个儿童
                    'confirmationNumber' => 'ROOM001',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '钱',
                'lastName' => '七',
                'email' => '<EMAIL>',
                'phone' => '+86-13500135000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '钱七',
                'expiryMonth' => 11,
                'expiryYear' => 2025,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => '房间 1 的儿童数量(2)与提供的年龄数量(1)不匹配',
            ]);
    }

    /**
     * 测试儿童年龄范围验证
     */
    public function test_multi_room_modification_children_age_range_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modify-reservation', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 1,
                    'childrenAges' => [20], // 年龄超出范围
                    'confirmationNumber' => 'ROOM001',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '孙',
                'lastName' => '八',
                'email' => '<EMAIL>',
                'phone' => '+86-13400134000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '孙八',
                'expiryMonth' => 7,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => '房间 1 的儿童年龄必须在0-17岁之间，当前: 20岁',
            ]);
    }

    /**
     * 测试房间数量限制
     */
    public function test_multi_room_modification_room_limit(): void
    {
        $rooms = [];
        for ($i = 0; $i < 15; $i++) { // 超过10个房间的限制
            $rooms[] = [
                'roomCode' => 'STD',
                'rateCode' => 'RATE001',
                'adults' => 2,
                'children' => 0,
                'confirmationNumber' => 'ROOM'.str_pad($i + 1, 3, '0', STR_PAD_LEFT),
            ];
        }

        $response = $this->postJson('/api/v1/sabre/modify-reservation', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => $rooms,
            'primaryGuest' => [
                'firstName' => '周',
                'lastName' => '九',
                'email' => '<EMAIL>',
                'phone' => '+86-13300133000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '周九',
                'expiryMonth' => 4,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['rooms']);
    }

    /**
     * 测试空房间数组验证
     */
    public function test_multi_room_modification_empty_rooms(): void
    {
        $response = $this->postJson('/api/v1/sabre/modify-reservation', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [], // 空房间数组
            'primaryGuest' => [
                'firstName' => '吴',
                'lastName' => '十',
                'email' => '<EMAIL>',
                'phone' => '+86-13200132000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '吴十',
                'expiryMonth' => 8,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['rooms']);
    }
}
