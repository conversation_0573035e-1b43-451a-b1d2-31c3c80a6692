<?php

namespace Tests\Feature;

use Tests\TestCase;

class RoomCountModificationTest extends TestCase
{
    /**
     * 测试增加房间的API请求验证
     */
    public function test_add_rooms_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-20',
            'checkOutDate' => '2024-12-23',
            'currentRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                ],
            ],
            'targetRooms' => [
                // 保留现有房间
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'DLX',
                    'rateCode' => 'UPGRADE_RATE',
                    'adults' => 2,
                    'children' => 0,
                ],
                // 添加新房间
                [
                    'roomCode' => 'STE',
                    'rateCode' => 'SUITE_RATE',
                    'adults' => 3,
                    'children' => 1,
                    'childrenAges' => [8],
                ],
            ],
            'primaryGuest' => [
                'firstName' => '张',
                'lastName' => '三',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '张三',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试减少房间的API请求验证
     */
    public function test_remove_rooms_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN987654321',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-25',
            'checkOutDate' => '2024-12-28',
            'currentRooms' => [
                [
                    'confirmationNumber' => 'ROOM101',
                    'roomCode' => 'STD',
                    'rateCode' => 'STANDARD_RATE',
                ],
                [
                    'confirmationNumber' => 'ROOM102',
                    'roomCode' => 'DLX',
                    'rateCode' => 'DELUXE_RATE',
                ],
                [
                    'confirmationNumber' => 'ROOM103',
                    'roomCode' => 'STE',
                    'rateCode' => 'SUITE_RATE',
                ],
            ],
            'targetRooms' => [
                // 只保留一个房间
                [
                    'confirmationNumber' => 'ROOM101',
                    'roomCode' => 'DLX',
                    'rateCode' => 'UPGRADE_RATE',
                    'adults' => 2,
                    'children' => 0,
                ],
            ],
            'primaryGuest' => [
                'firstName' => '李',
                'lastName' => '四',
                'email' => '<EMAIL>',
                'phone' => '+86-13900139000',
            ],
            'payment' => [
                'cardType' => 'MASTERCARD',
                'cardNumber' => '****************',
                'cardHolder' => '李四',
                'expiryMonth' => 6,
                'expiryYear' => 2027,
            ],
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试缺少必填参数时的验证错误
     */
    public function test_missing_itinerary_number_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            // 缺少 itineraryNumber
            'hotelId' => 12345,
            'checkInDate' => '2024-12-20',
            'checkOutDate' => '2024-12-23',
            'currentRooms' => [],
            'targetRooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'STANDARD_RATE',
                    'adults' => 2,
                    'children' => 0,
                ],
            ],
            'primaryGuest' => [
                'firstName' => '王',
                'lastName' => '五',
                'email' => '<EMAIL>',
                'phone' => '+86-13700137000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '王五',
                'expiryMonth' => 3,
                'expiryYear' => 2025,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['itineraryNumber']);
    }

    /**
     * 测试空目标房间列表的验证错误
     */
    public function test_empty_target_rooms_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-20',
            'checkOutDate' => '2024-12-23',
            'currentRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                ],
            ],
            'targetRooms' => [], // 空的目标房间列表
            'primaryGuest' => [
                'firstName' => '赵',
                'lastName' => '六',
                'email' => '<EMAIL>',
                'phone' => '+86-13600136000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '赵六',
                'expiryMonth' => 9,
                'expiryYear' => 2025,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['targetRooms']);
    }

    /**
     * 测试目标房间超过限制的验证错误
     */
    public function test_too_many_target_rooms_validation(): void
    {
        $targetRooms = [];
        for ($i = 0; $i < 15; $i++) { // 超过10个房间限制
            $targetRooms[] = [
                'roomCode' => 'STD',
                'rateCode' => 'STANDARD_RATE',
                'adults' => 2,
                'children' => 0,
            ];
        }

        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-20',
            'checkOutDate' => '2024-12-23',
            'currentRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                ],
            ],
            'targetRooms' => $targetRooms,
            'primaryGuest' => [
                'firstName' => '钱',
                'lastName' => '七',
                'email' => '<EMAIL>',
                'phone' => '+86-13500135000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '钱七',
                'expiryMonth' => 11,
                'expiryYear' => 2025,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['targetRooms']);
    }

    /**
     * 测试儿童年龄验证错误
     */
    public function test_children_age_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-20',
            'checkOutDate' => '2024-12-23',
            'currentRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                ],
            ],
            'targetRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                    'adults' => 2,
                    'children' => 2,
                    'childrenAges' => [8], // 只提供了1个年龄，但有2个儿童
                ],
            ],
            'primaryGuest' => [
                'firstName' => '孙',
                'lastName' => '八',
                'email' => '<EMAIL>',
                'phone' => '+86-13400134000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '孙八',
                'expiryMonth' => 7,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => '房间 1 的儿童数量(2)与提供的年龄数量(1)不匹配',
            ]);
    }

    /**
     * 测试儿童年龄范围验证错误
     */
    public function test_children_age_range_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-20',
            'checkOutDate' => '2024-12-23',
            'currentRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                ],
            ],
            'targetRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                    'adults' => 2,
                    'children' => 1,
                    'childrenAges' => [20], // 年龄超出范围
                ],
            ],
            'primaryGuest' => [
                'firstName' => '周',
                'lastName' => '九',
                'email' => '<EMAIL>',
                'phone' => '+86-13300133000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '周九',
                'expiryMonth' => 4,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => '房间 1 的儿童年龄必须在0-17岁之间，当前: 20岁',
            ]);
    }

    /**
     * 测试当前房间列表缺少确认号的验证错误
     */
    public function test_current_rooms_missing_confirmation_number(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-20',
            'checkOutDate' => '2024-12-23',
            'currentRooms' => [
                [
                    // 缺少 confirmationNumber
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                ],
            ],
            'targetRooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                    'adults' => 2,
                    'children' => 0,
                ],
            ],
            'primaryGuest' => [
                'firstName' => '吴',
                'lastName' => '十',
                'email' => '<EMAIL>',
                'phone' => '+86-13200132000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '吴十',
                'expiryMonth' => 8,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['currentRooms.0.confirmationNumber']);
    }

    /**
     * 测试目标房间缺少房间代码的验证错误
     */
    public function test_target_rooms_missing_room_code(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-20',
            'checkOutDate' => '2024-12-23',
            'currentRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    'roomCode' => 'STD',
                    'rateCode' => 'MEMBER_RATE',
                ],
            ],
            'targetRooms' => [
                [
                    'confirmationNumber' => 'ROOM001',
                    // 缺少 roomCode
                    'rateCode' => 'MEMBER_RATE',
                    'adults' => 2,
                    'children' => 0,
                ],
            ],
            'primaryGuest' => [
                'firstName' => '郑',
                'lastName' => '十一',
                'email' => '<EMAIL>',
                'phone' => '+86-13100131000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '郑十一',
                'expiryMonth' => 5,
                'expiryYear' => 2027,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['targetRooms.0.roomCode']);
    }

    /**
     * 测试复杂的房间变化场景
     */
    public function test_complex_room_changes_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/modifyReservationWithRoomChanges', [
            'itineraryNumber' => 'ITN456789123',
            'hotelId' => 12345,
            'hotelCode' => 'HOTEL001',
            'checkInDate' => '2024-12-30',
            'checkOutDate' => '2025-01-02',
            'currentRooms' => [
                [
                    'confirmationNumber' => 'ROOM201',
                    'roomCode' => 'STD',
                    'rateCode' => 'STANDARD_RATE',
                ],
                [
                    'confirmationNumber' => 'ROOM202',
                    'roomCode' => 'STD',
                    'rateCode' => 'STANDARD_RATE',
                ],
                [
                    'confirmationNumber' => 'ROOM203',
                    'roomCode' => 'DLX',
                    'rateCode' => 'DELUXE_RATE',
                ],
            ],
            'targetRooms' => [
                // 修改 ROOM201
                [
                    'confirmationNumber' => 'ROOM201',
                    'roomCode' => 'STE',
                    'rateCode' => 'SUITE_RATE',
                    'adults' => 2,
                    'children' => 2,
                    'childrenAges' => [5, 9],
                    'specialRequests' => '升级到套房，家庭入住',
                ],
                // 添加新房间1
                [
                    'roomCode' => 'DLX',
                    'rateCode' => 'VIP_RATE',
                    'adults' => 2,
                    'children' => 0,
                    'specialRequests' => '新增VIP豪华房',
                ],
                // 添加新房间2
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'PROMO_RATE',
                    'adults' => 1,
                    'children' => 0,
                    'specialRequests' => '单人入住促销价',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '王',
                'lastName' => '五',
                'email' => '<EMAIL>',
                'phone' => '+86-13700137000',
                'address' => [
                    'line1' => '广州市天河区珠江大道888号',
                    'city' => '广州',
                    'state' => '广东',
                    'country' => 'CN',
                    'postalCode' => '510000',
                ],
            ],
            'payment' => [
                'cardType' => 'AMEX',
                'cardNumber' => '***************',
                'cardHolder' => '王五',
                'expiryMonth' => 9,
                'expiryYear' => 2026,
            ],
            'loyaltyNumber' => 'GHA555666777',
            'promoCode' => 'NEWYEAR2025',
            'specialRequests' => '新年特别安排，需要套房+标准房组合',
            'sendConfirmationEmail' => true,
        ]);

        $response->assertStatus(200);
    }
}
