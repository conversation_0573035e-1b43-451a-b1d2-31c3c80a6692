<?php

namespace Tests\Feature;

use Tests\TestCase;

class UnifiedReservationProcessTest extends TestCase
{
    /**
     * 测试统一接口 - 单房间创建
     */
    public function test_unified_single_room_create(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'create',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'adults' => 2,
            'children' => 1,
            'childrenAges' => [8],
            'roomCode' => 'STD',
            'rateCode' => 'RATE001',
            'primaryGuest' => [
                'firstName' => '张',
                'lastName' => '三',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
                'address' => [
                    'line1' => '北京市朝阳区建国路88号',
                    'city' => '北京',
                    'state' => '北京',
                    'country' => 'CN',
                    'postalCode' => '100025',
                ],
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '张三',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
            'loyaltyNumber' => 'GHA123456789',
            'specialRequests' => '靠近电梯',
            'sendConfirmationEmail' => true,
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试统一接口 - 多房间创建
     */
    public function test_unified_multi_room_create(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'create',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 1,
                    'childrenAges' => [8],
                    'specialRequests' => '靠近电梯',
                ],
                [
                    'roomCode' => 'DLX',
                    'rateCode' => 'RATE002',
                    'adults' => 2,
                    'children' => 0,
                    'childrenAges' => [],
                    'specialRequests' => '高层房间',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '李',
                'lastName' => '四',
                'email' => '<EMAIL>',
                'phone' => '+86-13900139000',
                'address' => [
                    'line1' => '上海市浦东新区世纪大道1000号',
                    'city' => '上海',
                    'state' => '上海',
                    'country' => 'CN',
                    'postalCode' => '200120',
                ],
            ],
            'payment' => [
                'cardType' => 'MASTERCARD',
                'cardNumber' => '****************',
                'cardHolder' => '李四',
                'expiryMonth' => 6,
                'expiryYear' => 2027,
            ],
            'loyaltyNumber' => 'GHA987654321',
            'promoCode' => 'PROMO2024',
            'specialRequests' => '需要无障碍房间',
            'sendConfirmationEmail' => true,
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试统一接口 - 单房间修改
     */
    public function test_unified_single_room_modify(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'modify',
            'confirmationNumber' => 'CONF123456',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'adults' => 2,
            'children' => 0,
            'childrenAges' => [],
            'roomCode' => 'DLX',
            'rateCode' => 'MEMBER_RATE',
            'primaryGuest' => [
                'firstName' => '张',
                'lastName' => '三',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
                'address' => [
                    'line1' => '北京市朝阳区建国路88号',
                    'city' => '北京',
                    'state' => '北京',
                    'country' => 'CN',
                    'postalCode' => '100025',
                ],
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '张三',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
            'loyaltyNumber' => 'GHA123456789',
            'specialRequests' => '升级房间',
            'sendConfirmationEmail' => true,
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试统一接口 - 多房间修改
     */
    public function test_unified_multi_room_modify(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'modify',
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 1,
                    'childrenAges' => [8],
                    'confirmationNumber' => 'ROOM001',
                    'specialRequests' => '靠近电梯',
                ],
                [
                    'roomCode' => 'DLX',
                    'rateCode' => 'RATE002',
                    'adults' => 2,
                    'children' => 0,
                    'childrenAges' => [],
                    'confirmationNumber' => 'ROOM002',
                    'specialRequests' => '高层房间',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '李',
                'lastName' => '四',
                'email' => '<EMAIL>',
                'phone' => '+86-13900139000',
                'address' => [
                    'line1' => '上海市浦东新区世纪大道1000号',
                    'city' => '上海',
                    'state' => '上海',
                    'country' => 'CN',
                    'postalCode' => '200120',
                ],
            ],
            'payment' => [
                'cardType' => 'MASTERCARD',
                'cardNumber' => '****************',
                'cardHolder' => '李四',
                'expiryMonth' => 6,
                'expiryYear' => 2027,
            ],
            'loyaltyNumber' => 'GHA987654321',
            'specialRequests' => '需要相邻房间',
            'sendConfirmationEmail' => true,
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试统一接口 - 取消预订
     */
    public function test_unified_cancel_reservation(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'cancel',
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'primaryGuest' => [
                'firstName' => '李',
                'lastName' => '四',
                'email' => '<EMAIL>',
                'phone' => '+86-13900139000',
            ],
            'payment' => [
                'cardType' => 'MASTERCARD',
                'cardNumber' => '****************',
                'cardHolder' => '李四',
                'expiryMonth' => 6,
                'expiryYear' => 2027,
            ],
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试缺少status字段的验证
     */
    public function test_unified_missing_status_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'roomCode' => 'STD',
            'rateCode' => 'RATE001',
            'primaryGuest' => [
                'firstName' => '张',
                'lastName' => '三',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '张三',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => '缺少操作类型(status)参数',
            ]);
    }

    /**
     * 测试无效status字段的验证
     */
    public function test_unified_invalid_status_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'invalid_operation',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'roomCode' => 'STD',
            'rateCode' => 'RATE001',
            'primaryGuest' => [
                'firstName' => '张',
                'lastName' => '三',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '张三',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => '无效的操作类型，支持的类型：create, modify, cancel',
            ]);
    }

    /**
     * 测试多房间修改缺少行程号的验证
     */
    public function test_unified_multi_room_modify_missing_itinerary(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'modify',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 0,
                    'confirmationNumber' => 'ROOM001',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '李',
                'lastName' => '四',
                'email' => '<EMAIL>',
                'phone' => '+86-13900139000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '李四',
                'expiryMonth' => 3,
                'expiryYear' => 2025,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['itineraryNumber']);
    }

    /**
     * 测试房间缺少确认号时的验证
     */
    public function test_unified_room_missing_confirmation_number(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'modify',
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 0,
                    // 缺少 confirmationNumber
                ],
            ],
            'primaryGuest' => [
                'firstName' => '赵',
                'lastName' => '六',
                'email' => '<EMAIL>',
                'phone' => '+86-13600136000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '赵六',
                'expiryMonth' => 9,
                'expiryYear' => 2025,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['rooms.0.confirmationNumber']);
    }

    /**
     * 测试多房间修改 - 支持房间级别状态
     */
    public function test_unified_multi_room_modify_with_room_level_status(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'modify',
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                // 修改现有房间
                [
                    'status' => 'modify',
                    'roomCode' => 'DLX',
                    'rateCode' => 'MEMBER_RATE',
                    'adults' => 2,
                    'children' => 1,
                    'childrenAges' => [10],
                    'confirmationNumber' => 'ROOM001',
                    'specialRequests' => '升级到豪华房',
                ],
                // 取消现有房间
                [
                    'status' => 'cancel',
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 0,
                    'childrenAges' => [],
                    'confirmationNumber' => 'ROOM002',
                ],
                // 新增房间
                [
                    'status' => 'create',
                    'roomCode' => 'FAM',
                    'rateCode' => 'FAMILY_RATE',
                    'adults' => 2,
                    'children' => 2,
                    'childrenAges' => [6, 8],
                    'specialRequests' => '家庭房',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '王',
                'lastName' => '五',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138888',
                'address' => [
                    'line1' => '广州市天河区珠江新城花城大道123号',
                    'city' => '广州',
                    'state' => '广东',
                    'country' => 'CN',
                    'postalCode' => '510623',
                ],
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '王五',
                'expiryMonth' => 9,
                'expiryYear' => 2028,
            ],
            'loyaltyNumber' => 'GHA999888777',
            'sendConfirmationEmail' => true,
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试房间状态验证 - 无效状态
     */
    public function test_unified_room_invalid_status_validation(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'modify',
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'status' => 'invalid_status', // 无效状态
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 0,
                    'confirmationNumber' => 'ROOM001',
                ],
            ],
            'primaryGuest' => [
                'firstName' => '测试',
                'lastName' => '用户',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '测试用户',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['rooms.0.status']);
    }

    /**
     * 测试创建房间不需要确认号
     */
    public function test_unified_create_room_no_confirmation_required(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'modify',
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'status' => 'create', // 创建房间不需要确认号
                    'roomCode' => 'FAM',
                    'rateCode' => 'FAMILY_RATE',
                    'adults' => 2,
                    'children' => 2,
                    'childrenAges' => [6, 8],
                    'specialRequests' => '家庭房',
                    // 注意：没有 confirmationNumber
                ],
            ],
            'primaryGuest' => [
                'firstName' => '测试',
                'lastName' => '用户',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '测试用户',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试取消房间需要确认号
     */
    public function test_unified_cancel_room_requires_confirmation(): void
    {
        $response = $this->postJson('/api/v1/sabre/reservation', [
            'status' => 'modify',
            'itineraryNumber' => 'ITN123456789',
            'hotelId' => 12345,
            'checkInDate' => '2024-12-01',
            'checkOutDate' => '2024-12-03',
            'rooms' => [
                [
                    'status' => 'cancel', // 取消房间需要确认号
                    'roomCode' => 'STD',
                    'rateCode' => 'RATE001',
                    'adults' => 2,
                    'children' => 0,
                    'childrenAges' => [],
                    // 缺少 confirmationNumber
                ],
            ],
            'primaryGuest' => [
                'firstName' => '测试',
                'lastName' => '用户',
                'email' => '<EMAIL>',
                'phone' => '+86-13800138000',
            ],
            'payment' => [
                'cardType' => 'VISA',
                'cardNumber' => '****************',
                'cardHolder' => '测试用户',
                'expiryMonth' => 12,
                'expiryYear' => 2026,
            ],
        ]);

        $response->assertStatus(400)
            ->assertJsonStructure([
                'success',
                'message',
            ]);
    }
}
