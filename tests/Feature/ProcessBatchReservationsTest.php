<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProcessBatchReservationsTest extends TestCase
{
    /**
     * 测试批量处理预订接口的基本功能
     */
    public function test_process_batch_reservations_with_mixed_operations()
    {
        $requestData = [
            [
                "CRS_confirmationNumber" => "100820CK000421",
                "itineraryNumber" => "32446B0003254",
                "status" => "modify",
                "hotelId" => 100820,
                "chainId" => "32446",
                "checkInDate" => "2025-10-01",
                "checkOutDate" => "2025-10-02",
                "numRooms" => 1,
                "adults" => 2,
                "children" => 1,
                "childrenAges" => [8],
                "roomCode" => "1STE",
                "rateCode" => "BAR",
                "primaryGuest" => [
                    "firstName" => "张",
                    "lastName" => "三",
                    "email" => "<EMAIL>",
                    "phone" => "13800138000"
                ],
                "payment" => [
                    "cardNumber" => "****************",
                    "cardHolder" => "ZHANG SAN",
                    "expiryMonth" => 12,
                    "expiryYear" => 2025
                ],
                "loyaltyNumber" => "111",
                "sendConfirmationEmail" => true
            ],
            [
                "itineraryNumber" => "32446B0003254",
                "status" => "create",
                "hotelId" => 100820,
                "chainId" => "32446",
                "checkInDate" => "2025-10-01",
                "checkOutDate" => "2025-10-02",
                "numRooms" => 1,
                "adults" => 2,
                "children" => 1,
                "childrenAges" => [8],
                "roomCode" => "1STE",
                "rateCode" => "BAR",
                "primaryGuest" => [
                    "firstName" => "张",
                    "lastName" => "三",
                    "email" => "<EMAIL>",
                    "phone" => "13800138000"
                ],
                "payment" => [
                    "cardNumber" => "****************",
                    "cardHolder" => "ZHANG SAN",
                    "expiryMonth" => 12,
                    "expiryYear" => 2025
                ],
                "loyaltyNumber" => "111",
                "sendConfirmationEmail" => true
            ]
        ];

        $response = $this->postJson('/api/v1/sabre/processBatchReservations', $requestData);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'itinerary_number',
                'total_operations',
                'success_count',
                'failed_count',
                'operations' => [
                    '*' => [
                        'index',
                        'status',
                        'success',
                        'confirmation_number',
                        'itinerary_number',
                        'room_code',
                        'rate_code',
                        'operation'
                    ]
                ]
            ]
        ]);
    }

    /**
     * 测试参数验证
     */
    public function test_validation_errors()
    {
        // 测试缺少必需字段
        $invalidData = [
            [
                "status" => "modify",
                // 缺少 CRS_confirmationNumber
                "hotelId" => 100820,
            ]
        ];

        $response = $this->postJson('/api/v1/sabre/processBatchReservations', $invalidData);
        $response->assertStatus(400);
        $response->assertJsonPath('message', '参数验证失败');
    }

    /**
     * 测试儿童年龄验证
     */
    public function test_children_age_validation()
    {
        $invalidData = [
            [
                "status" => "create",
                "hotelId" => 100820,
                "checkInDate" => "2025-10-01",
                "checkOutDate" => "2025-10-02",
                "children" => 2,
                "childrenAges" => [8], // 只提供了1个年龄，但children是2
                "roomCode" => "1STE",
                "rateCode" => "BAR",
                "primaryGuest" => [
                    "firstName" => "张",
                    "lastName" => "三",
                    "email" => "<EMAIL>",
                    "phone" => "13800138000"
                ],
                "payment" => [
                    "cardNumber" => "****************",
                    "cardHolder" => "ZHANG SAN",
                    "expiryMonth" => 12,
                    "expiryYear" => 2025
                ]
            ]
        ];

        $response = $this->postJson('/api/v1/sabre/processBatchReservations', $invalidData);
        $response->assertStatus(400);
        $response->assertJsonPath('message', '第1个订单的儿童数量(2)与提供的年龄数量(1)不匹配');
    }
}
