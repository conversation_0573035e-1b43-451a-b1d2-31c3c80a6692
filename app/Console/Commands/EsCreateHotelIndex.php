<?php

namespace App\Console\Commands;

use App\Services\ElasticsearchService;
use Illuminate\Console\Command;

class EsCreateHotelIndex extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:create-hotel-index';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建hotel索引';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = ElasticsearchService::client();

        // 如果存在先删除
        if ($client->indices()->exists(['index' => '.hotel'])) {
            $client->indices()->delete(['index' => '.hotel']);
        }

        $params = [
            'index' => '.hotel',
            'body' => [
                'settings' => [
                    'number_of_shards' => 3,
                    'number_of_replicas' => 1,
                    'analysis' => [
                        'analyzer' => [
                            'lowercase_analyzer' => [
                                'type' => 'custom',
                                'tokenizer' => 'keyword',
                                'filter' => ['lowercase'],
                            ],
                        ],
                    ],
                ],
                'mappings' => [
                    'doc' => [
                        'properties' => [
                            'id' => ['type' => 'integer'],
                            'code' => ['type' => 'keyword'],
                            'hotelcode' => ['type' => 'keyword'],
                            'hotel_name' => ['type' => 'keyword'],
                            'hotel_name_en' => ['type' => 'keyword'],
                            'brand_name' => ['type' => 'keyword'],
                            'brand_nameEn' => ['type' => 'keyword'],
                            'address' => ['type' => 'keyword'],
                            'address_en' => ['type' => 'keyword'],
                            'brand_img' => ['type' => 'text'],
                            'gradient_color' => ['type' => 'keyword'],
                            'latitude' => ['type' => 'float'],
                            'longitude' => ['type' => 'float'],
                            'hotel_type' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => ['type' => 'integer'],
                                    'name' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                    'name_en' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                ],
                            ],
                            'city_code' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => ['type' => 'integer'],
                                    'name' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                    'name_en' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                ],
                            ],
                            'country_code' => ['type' => 'keyword'],
                            'created_at' => ['type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis'],
                            'new_hotel' => ['type' => 'text'],
                            'images' => ['type' => 'keyword'],
                            // nested fields
                            'destinations' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => ['type' => 'integer'],
                                    'name' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                    'name_en' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                ],
                            ],
                            'interests' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => ['type' => 'integer'],
                                    'name' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                    'name_en' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                ],
                            ],
                            'eco_certificates' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => ['type' => 'integer'],
                                    'name' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                    'name_en' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                ],
                            ],
                            'categories' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => ['type' => 'integer'],
                                    'name' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                    'name_en' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                ],
                            ],
                            'neighborhood_tag' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => ['type' => 'integer'],
                                    'name' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                    'name_en' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                ],
                            ],

                            'condition_code' => ['type' => 'keyword'],
                            'new_hotel_start_date' => ['type' => 'integer'],
                            'new_hotel_end_date' => ['type' => 'integer'],
                            'synxis_hotel_id' => ['type' => 'keyword'],
                            'synxis_chain_id' => ['type' => 'keyword'],
                            'feature' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => ['type' => 'integer'],
                                    'name' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                    'name_en' => ['type' => 'text', 'analyzer' => 'lowercase_analyzer', 'fields' => ['keyword' => ['type' => 'keyword']]],
                                ],
                            ],
                            'city' => ['type' => 'keyword'],
                            'country' => ['type' => 'keyword'],
                            'head_line' => ['type' => 'keyword'],
                            'url' => ['type' => 'keyword'],
                        ],
                    ],
                ],
            ],
        ];

        $client->indices()->create($params);
        $this->info('索引创建完成');
    }
}
