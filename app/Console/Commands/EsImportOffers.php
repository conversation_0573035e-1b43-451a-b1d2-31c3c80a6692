<?php

namespace App\Console\Commands;

use App\Models\Hotel;
use App\Models\HotelBrand;
use App\Models\HotelOffer;
use App\Services\ElasticsearchService;
use Illuminate\Console\Command;

class EsImportOffers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:import-offers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '导入offers模型数据至Elasticsearch';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = ElasticsearchService::client();
        $totalImported = 0;
        $totalErrors = 0;
        HotelOffer::chunk(300, function ($offers) use ($client, &$totalImported, &$totalErrors) {
            $params = ['body' => []];
            foreach ($offers as $offer) {
                $params['body'][] = [
                    'index' => [
                        '_index' => '.offers',
                        '_type' => 'doc',
                        '_id' => $offer->id,
                    ],
                ];
                $hotel = Hotel::where('code', $offer->hotels[0])->select(['brand_code', 'longitude', 'latitude', 'city_code'])->first();
                $brand_id = HotelBrand::where('code', $hotel['brand_code'])->value('id');
                $brand_name = $hotel['brand_name'] ?? '';
                $country_name = '暂无';
                $city_name = $hotel['city_name'] ?? '';
                $brand_img = $hotel['brand_img'] ?? '';
                $longitude = $hotel['longitude'] ?? '';
                $latitude = $hotel['latitude'] ?? '';
                $data = [
                    'id' => $offer->id,
                    'external_id' => $offer->external_id ?? '',
                    'title' => $offer->title ?? '',
                    'title_cn' => $offer->title_cn ?? '',
                    'hotels' => $offer->hotels ?? '',
                    'url' => $offer->url ?? '',
                    'related_rate_codes' => $offer->related_rate_codes ?? '',
                    'start_date' => $offer->start_date ?? '',
                    'end_date' => $offer->end_date ?? '',
                    'stay_start_date' => $offer->stay_start_date ?? '',
                    'stay_end_date' => $offer->stay_end_date ?? '',
                    'hotel_name' => $offer->hotel_name ?? '',
                    'hotel_name_en' => $offer->hotel_name_en ?? '',
                    'rate_code1' => $offer->rate_code1 ?? '',
                    'rate_code2' => $offer->rate_code2 ?? '',
                    'rate_code3' => $offer->rate_code3 ?? '',
                    'rate_code4' => $offer->rate_code4 ?? '',
                    'rate_checked' => $offer->rate_checked ?? '',
                    'featured' => $offer->featured ?? '',
                    'offer_types' => $offer->offer_types ?? '',
                    'offer_types_en' => $offer->offer_types_en ?? '',
                    'promo_type' => $offer->promo_type ?? '',
                    'promo_code' => $offer->promo_code ?? '',
                    'description' => $offer->description ?? '',
                    'description_en' => $offer->description_en ?? '',
                    'distribution' => $offer->distribution ?? '',
                    'highlights' => $offer->highlights ?? '',
                    'offerIncludes' => $offer->offerIncludes ?? '',
                    'offerIncludes_en' => $offer->offerIncludes_en ?? '',
                    'terms_conditions' => $offer->terms_conditions ?? '',
                    'terms_conditions_en' => $offer->terms_conditions_en ?? '',
                    'taxes' => $offer->taxes ?? '',
                    'taxes_en' => $offer->taxes_en ?? '',
                    'images' => $offer->images ?? '',
                    'image' => $offer->image ?? '',
                    'members_only' => $offer->members_only ?? '',
                    'percentage_off' => $offer->percentage_off ?? '',
                    'head_line' => $offer->head_line ?? '',
                    'created_at' => $offer->created_at ? $offer->created_at->toDateTimeString() : null,
                    'updated_at' => $offer->updated_at ? $offer->created_at->toDateTimeString() : null,
                    'country_name' => $country_name,
                    'brand_name' => $brand_name,
                    'city_name' => $city_name,
                    'brand_id' => $brand_id,
                    'brand_img' => $brand_img,
                    'longitude' => $longitude,
                    'latitude' => $latitude,
                ];
                // 移除null值，避免类型冲突
                $data = array_filter($data, function ($value) {
                    return $value !== null;
                });
                $params['body'][] = $data;
            }
            try {
                $response = $client->bulk($params);
                if ($response['errors']) {
                    $errorCount = count(array_filter($response['items'], function ($item) {
                        if (isset($item['index'])) {
                            return isset($item['index']['error']);
                        }
                    }));
                    $totalErrors += $errorCount;
                    $this->warn("批量导入中有 {$errorCount} 个错误");
                }
                $totalImported += count($offers);
                $this->info("已导入 {$totalImported} 条记录，累计错误: {$totalErrors}");
            } catch (\Exception $e) {
                $this->error('导入失败: '.$e->getMessage());
                $totalErrors += count($offers);
            }
        });
        $this->info("全部导入完成！总计: {$totalImported} 条，错误: {$totalErrors} 条");
    }

    /**
     * 处理数组字段
     */
    protected function processArrayField($value)
    {
        if (empty($value)) {
            return null;
        }

        // 如果已经是数组，提取name字段或直接返回
        if (is_array($value)) {
            if (! empty($value) && is_array($value[0]) && isset($value[0]['name'])) {
                // 从对象数组中提取name字段
                return array_column($value, 'name');
            }

            return $value; // 已经是简单数组
        }

        // 如果是JSON字符串
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                if (isset($decoded[0]['name'])) {
                    return array_column($decoded, 'name');
                }

                return $decoded;
            }

            // 单个值
            return [$value];
        }

        // 数字或字符串
        return [(string) $value];
    }
}
