<?php

namespace App\Console\Commands;

use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Hotel;
use App\Models\HotelEco;
use App\Models\HotelInterest;
use App\Models\HotelLoop;
use App\Models\HotelNeighborhood;
use App\Models\HotelRom;
use App\Models\HotelType;
use App\Services\ElasticsearchService;
use Illuminate\Console\Command;

class EsImportHotal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:import-hotal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '导入Hotel模型数据至Elasticsearch';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = ElasticsearchService::client();
        $totalImported = 0;
        $totalErrors = 0;
        Hotel::with('brand')->chunk(100, function ($hotels) use ($client, &$totalImported, &$totalErrors) {
            $params = ['body' => []];
            foreach ($hotels as $hotel) {
                $params['body'][] = [
                    'index' => [
                        '_index' => '.hotel',
                        '_type' => 'doc',
                        '_id' => $hotel->id,
                    ],
                ];
                $city_name = '';
                // 城市获取
                $city_code = $hotel->city_code;
                if ($city_code && is_array($city_code)) {
                    foreach ($city_code as $key => $value) {
                        $city_code[$key]['name_en'] = $value['name'];
                        $city_code[$key]['name'] = city::where('city_name_en', $value['name'])->value('city_name');
                        $city_name = $city_code[$key]['name'];
                    }
                }
                // 目的处理
                $destinations = $hotel->destinations;
                if ($destinations && is_array($destinations)) {
                    foreach ($destinations as $key => $value) {
                        $destinations[$key]['name_en'] = $value['name'];
                        $destinations[$key]['name'] = Continent::where('name_en', $value['name'])->value('name');
                    }
                }
                // 兴趣爱好
                $interests = $hotel->interests;
                if ($interests && is_array($interests)) {
                    foreach ($interests as $key => $value) {
                        $interests[$key]['name_en'] = $value['name'];
                        $interests[$key]['name'] = HotelInterest::where('name_en', $value['name'])->value('name');
                    }
                }
                // 酒店设施
                $features = $hotel->feature;
                if ($features && is_array($features)) {
                    foreach ($features as $key => $value) {
                        $features[$key]['name_en'] = $value['name'];
                        $features[$key]['name'] = HotelRom::where('name_en', $value['name'])->value('name');
                    }
                }
                $head_line = '';
                // 酒店系列
                $categories = $hotel->categories;
                if ($categories && is_array($categories)) {
                    foreach ($categories as $key => $value) {
                        $categories[$key]['name_en'] = $value['name'];
                        $categories[$key]['name'] = HotelLoop::where('name_en', $value['name'])->value('name');
                        $head_line = $categories[$key]['name'];
                    }
                }
                // 酒店类型
                $hotel_type = $hotel->hotel_type;
                if ($hotel_type && is_array($hotel_type)) {
                    foreach ($hotel_type as $key => $value) {
                        $hotel_type[$key]['name_en'] = $value['name'];
                        $hotel_type[$key]['name'] = HotelType::where('name_en', $value['name'])->value('name');
                    }
                }
                // 生态证书
                $eco_certificates = $hotel->eco_certificates;
                if ($eco_certificates && is_array($eco_certificates)) {
                    foreach ($eco_certificates as $key => $value) {
                        $eco_certificates[$key]['name_en'] = $value['name'];
                        $eco_certificates[$key]['name'] = HotelEco::where('name_en', $value['name'])->value('name');
                    }
                }
                // 社区标签
                $neighborhood_tag = $hotel->neighborhood_tag;
                if ($neighborhood_tag && is_array($neighborhood_tag)) {
                    foreach ($neighborhood_tag as $key => $value) {
                        $neighborhood_tag[$key]['name_en'] = $value['name'];
                        $neighborhood_tag[$key]['name'] = HotelNeighborhood::where('name_en', $value['name'])->value('name');
                    }
                }
                if ($hotel['country_code']) {
                    $country_name = Country::where('original_id', $hotel['country_code'])->value('country_name');
                } else {
                    $country_name = '';
                }

                $data = [
                    'id' => $hotel->id,
                    'code' => $hotel->code ?? '',
                    'hotelcode' => $hotel->hotelcode ?? '',
                    'hotel_name' => $hotel->hotel_name ?? '',
                    'hotel_name_en' => $hotel->hotel_name_en ?? '',
                    'gradient_color' => $hotel->gradient_color ?? '',
                    'city_code' => $city_code ?? [],
                    'hotel_type' => $hotel_type ?? [],
                    'brand_code' => $hotel->brand_code ?? '',
                    'brand_id' => $hotel->brand->id ?? '',     // 关联品牌名称
                    'brand_name' => $hotel->brand->name ?? '',     // 关联品牌名称
                    'brand_img' => $hotel->brand->logo_svg ?? '',     // 关联品牌名称
                    'brand_nameEn' => $hotel->brand->name_en ?? '',     // 关联品牌名称
                    'longitude' => $hotel->longitude ?? '',
                    'latitude' => $hotel->latitude ?? '',
                    'created_at' => $hotel->created_at ? $hotel->created_at->toDateTimeString() : null,
                    'country_code' => $hotel->country_code ?? '',
                    'new_hotel' => $hotel->new_hotel ?? '',
                    'images' => $hotel->images ?? '',
                    'destinations' => $destinations ?? [],
                    'interests' => $interests ?? [],
                    'eco_certificates' => $eco_certificates ?? [],
                    'condition_code' => $hotel->condition_code ?? '',
                    'new_hotel_start_date' => $hotel->new_hotel_start_date ?? '',
                    'new_hotel_end_date' => $hotel->new_hotel_end_date ?? '',
                    'synxis_hotel_id' => $hotel->synxis_hotel_id ?? '',
                    'synxis_chain_id' => $hotel->synxis_chain_id ?? '',
                    'feature' => $features ?? [],
                    'categories' => $categories ?? [],
                    'neighborhood_tag' => $neighborhood_tag ?? [],
                    'city_name' => $city_name ?? '',
                    'city' => $city_name ?? '',
                    'country_name' => $country_name ?? '',
                    'country' => $country_name ?? '',
                    'head_line' => $head_line ?? '',
                    'url' => $hotel->url ?? '',
                ];
                // 移除null值，避免类型冲突
                $data = array_filter($data, function ($value) {
                    return $value !== null;
                });
                $params['body'][] = $data;
            }
            try {
                $response = $client->bulk($params);
                if ($response['errors']) {
                    $errorCount = count(array_filter($response['items'], function ($item) {
                        if (isset($item['index'])) {
                            if (isset($item['index']['error'])) {
                                return 1;
                            }
                        }
                    }));
                    $totalErrors += $errorCount;
                    $this->warn("批量导入中有 {$errorCount} 个错误");
                }
                $totalImported += count($hotels);
                $this->info("已导入 {$totalImported} 条记录，累计错误: {$totalErrors}");
            } catch (\Exception $e) {
                $this->error('导入失败: '.$e->getMessage());
                $totalErrors += count($hotels);
            }
        });
        $this->info("全部导入完成！总计: {$totalImported} 条，错误: {$totalErrors} 条");
    }

    /**
     * 处理数组字段
     */
    protected function processArrayField($value)
    {
        if (empty($value)) {
            return null;
        }

        // 如果已经是数组，提取name字段或直接返回
        if (is_array($value)) {
            if (! empty($value) && is_array($value[0]) && isset($value[0]['name'])) {
                // 从对象数组中提取name字段
                return array_column($value, 'name');
            }

            return $value; // 已经是简单数组
        }

        // 如果是JSON字符串
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                if (isset($decoded[0]['name'])) {
                    return array_column($decoded, 'name');
                }

                return $decoded;
            }

            // 单个值
            return [$value];
        }

        // 数字或字符串
        return [(string) $value];
    }
}
