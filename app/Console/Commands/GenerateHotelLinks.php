<?php

namespace App\Console\Commands;

use App\Models\Hotel;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateHotelLinks extends Command
{
    protected $signature = 'hotel:generate-links {--force : Force update existing links}';

    protected $description = 'Generate SEO-friendly hotel links based on English names';

    public function handle()
    {
        $this->info('Starting hotel link generation...');

        $query = Hotel::query();

        if (! $this->option('force')) {
            $query->where(function ($q) {
                $q->whereNull('hotel_link')->orWhere('hotel_link', '');
            });
        }

        $hotels = $query->whereNotNull('hotel_name_en')
            ->where('hotel_name_en', '!=', '')
            ->get();
        if ($hotels->isEmpty()) {
            $this->warn('No hotels found with English names to process.');

            return 0;
        }

        $this->info("Found {$hotels->count()} hotels to process.");

        $progressBar = $this->output->createProgressBar($hotels->count());
        $progressBar->start();

        $updated = 0;
        $duplicateMap = [];

        foreach ($hotels as $hotel) {
            $link = $this->generateSeoLink($hotel->hotel_name_en);

            // Handle duplicates by appending hotel ID
            if (isset($duplicateMap[$link])) {
                $link = $link.'-'.$hotel->id;
            }
            $duplicateMap[$link] = true;

            $hotel->update(['hotel_link' => $link]);
            $updated++;

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->info("Successfully updated {$updated} hotel links.");

        // Show some examples
        $this->newLine();
        $this->info('Sample generated links:');
        $examples = Hotel::whereNotNull('hotel_link')
            ->where('hotel_link', '!=', '')
            ->select('hotel_name_en', 'hotel_link')
            ->limit(5)
            ->get();

        foreach ($examples as $example) {
            $this->line("  {$example->hotel_name_en} → {$example->hotel_link}");
        }

        return 0;
    }

    private function generateSeoLink(string $hotelName): string
    {
        // Convert to lowercase
        $link = strtolower($hotelName);

        // Remove common hotel words that are not SEO-friendly
        $commonWords = [
            ' hotel', ' resort', ' spa', ' suites', ' inn', ' lodge',
            ' by ', ' the ', ' a ', ' an ', ' and ', ' of ', ' in ', ' at ',
            ' & ', ' + ', 'hotel ', 'resort ', 'spa ', 'suites ', 'inn ', 'lodge ',
        ];

        foreach ($commonWords as $word) {
            $link = str_replace($word, ' ', $link);
        }

        // Replace special characters and spaces with hyphens
        $link = preg_replace('/[^a-z0-9\s-]/', '', $link);
        $link = preg_replace('/\s+/', '-', trim($link));

        // Remove multiple consecutive hyphens
        $link = preg_replace('/-+/', '-', $link);

        // Remove leading/trailing hyphens
        $link = trim($link, '-');

        // Ensure it's not too long (SEO best practice)
        if (strlen($link) > 60) {
            $words = explode('-', $link);
            $truncated = '';
            foreach ($words as $word) {
                if (strlen($truncated.'-'.$word) <= 60) {
                    $truncated = $truncated ? $truncated.'-'.$word : $word;
                } else {
                    break;
                }
            }
            $link = $truncated;
        }

        // Fallback if link is empty or too short
        if (strlen($link) < 3) {
            $link = 'hotel-'.Str::random(6);
        }

        return $link;
    }
}
