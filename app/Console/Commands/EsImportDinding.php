<?php

namespace App\Console\Commands;

use App\Models\HotelDining;
use App\Services\ElasticsearchService;
use Illuminate\Console\Command;

class EsImportDinding extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:import-dining';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '导入dining模型数据至Elasticsearch';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = ElasticsearchService::client();
        $totalImported = 0;
        $totalErrors = 0;
        HotelDining::chunk(300, function ($hotels) use ($client, &$totalImported, &$totalErrors) {
            $params = ['body' => []];
            foreach ($hotels as $hotel) {
                $params['body'][] = [
                    'index' => [
                        '_index' => '.dining',
                        '_type' => 'doc',
                        '_id' => $hotel->id,
                    ],
                ];
                $data = [
                    'id' => $hotel->id,
                    'code' => $hotel->code ?? '',
                    'hotelcode' => $hotel->hotelcode ?? '',
                    'hotel_name' => $hotel->hotel_name ?? '',
                    'gradient_color' => $hotel->gradient_color ?? '',
                    'city_code' => $city_code ?? [],
                    'hotel_type' => $hotel_type ?? [],
                    'brand_code' => $hotel->brand_code ?? '',
                    'brand_id' => $hotel->brand->id ?? '',     // 关联品牌名称
                    'brand_name' => $hotel->brand->name ?? '',     // 关联品牌名称
                    'brand_img' => $hotel->brand->logo_svg ?? '',     // 关联品牌名称
                    'brand_nameEn' => $hotel->brand->name_en ?? '',     // 关联品牌名称
                    'longitude' => $hotel->longitude ?? '',
                    'latitude' => $hotel->latitude ?? '',
                    'created_at' => $hotel->created_at ? $hotel->created_at->toDateTimeString() : null,
                    'country_code' => $hotel->country_code ?? '',
                    'new_hotel' => $hotel->new_hotel ?? '',
                    'images' => $hotel->images ?? '',
                    'destinations' => $destinations ?? [],
                    'interests' => $interests ?? [],
                    'eco_certificates' => $eco_certificates ?? [],
                    'condition_code' => $hotel->condition_code ?? '',
                    'new_hotel_start_date' => $hotel->new_hotel_start_date ?? '',
                    'new_hotel_end_date' => $hotel->new_hotel_end_date ?? '',
                    'synxis_hotel_id' => $hotel->synxis_hotel_id ?? '',
                    'synxis_chain_id' => $hotel->synxis_chain_id ?? '',
                    'feature' => $features ?? [],
                    'categories' => $categories ?? [],
                    'neighborhood_tag' => $neighborhood_tag ?? [],
                ];
                // 移除null值，避免类型冲突
                $data = array_filter($data, function ($value) {
                    return $value !== null;
                });
                $params['body'][] = $data;
            }
            try {
                $response = $client->bulk($params);
                if ($response['errors']) {
                    $errorCount = count(array_filter($response['items'], function ($item) {
                        if (isset($item['index'])) {
                            return isset($item['index']['error']);
                        }
                    }));
                    $totalErrors += $errorCount;
                    $this->warn("批量导入中有 {$errorCount} 个错误");
                }
                $totalImported += count($hotels);
                $this->info("已导入 {$totalImported} 条记录，累计错误: {$totalErrors}");
            } catch (\Exception $e) {
                $this->error('导入失败: '.$e->getMessage());
                $totalErrors += count($hotels);
            }
        });
        $this->info("全部导入完成！总计: {$totalImported} 条，错误: {$totalErrors} 条");
    }

    /**
     * 处理数组字段
     */
    protected function processArrayField($value)
    {
        if (empty($value)) {
            return null;
        }

        // 如果已经是数组，提取name字段或直接返回
        if (is_array($value)) {
            if (! empty($value) && is_array($value[0]) && isset($value[0]['name'])) {
                // 从对象数组中提取name字段
                return array_column($value, 'name');
            }

            return $value; // 已经是简单数组
        }

        // 如果是JSON字符串
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                if (isset($decoded[0]['name'])) {
                    return array_column($decoded, 'name');
                }

                return $decoded;
            }

            // 单个值
            return [$value];
        }

        // 数字或字符串
        return [(string) $value];
    }
}
