<?php

namespace App\Listeners;

use App\Events\ReservationStatusChangedEvent;
use App\Services\GxpService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class GxpReservationStatusListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected GxpService $gxpService;

    /**
     * Create the event listener.
     */
    public function __construct(GxpService $gxpService)
    {
        $this->gxpService = $gxpService;
    }

    /**
     * Handle the event.
     */
    public function handle(ReservationStatusChangedEvent $event): void
    {
        try {
            // 检查GXP服务是否已初始化
            if (! $this->gxpService->isInitialized()) {
                Log::warning('GXP服务未初始化，跳过状态上报', [
                    'itinerary_number' => $event->itineraryNumber,
                    'status' => $event->status,
                    'profile_id' => $event->profileId,
                ]);

                return;
            }

            Log::info('开始GXP订单状态上报', [
                'itinerary_number' => $event->itineraryNumber,
                'status' => $event->status,
                'profile_id' => $event->profileId,
            ]);

            // 调用GXP状态上报
            $result = $this->gxpService->reportReservationStatus($event->toArray());

            Log::info('GXP订单状态上报成功', [
                'itinerary_number' => $event->itineraryNumber,
                'status' => $event->status,
                'result' => $result,
            ]);

        } catch (Exception $e) {
            Log::error('GXP订单状态上报失败', [
                'itinerary_number' => $event->itineraryNumber,
                'status' => $event->status,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发队列重试机制
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(ReservationStatusChangedEvent $event, Exception $exception): void
    {
        Log::error('GXP订单状态上报最终失败', [
            'itinerary_number' => $event->itineraryNumber,
            'status' => $event->status,
            'error' => $exception->getMessage(),
        ]);
    }
}
