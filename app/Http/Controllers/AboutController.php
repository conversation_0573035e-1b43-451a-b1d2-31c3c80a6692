<?php

namespace App\Http\Controllers;

use App\Admin\Controllers\NewsController;
use App\Admin\Controllers\WechatHighlightController;
use App\Http\Controllers\V1\RoomController;
use App\Models\Activity;
use App\Models\Hotel;
use App\Models\HotelBrand;
use App\Models\HotelOffer;
use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;

class AboutController extends BaseController
{
    public function index()
    {

        return view('about.index');
    }

    public function brands()
    {
        return view('about.brands');
    }

    public function brandsDetail($id)
    {
        $brand = HotelBrand::where('id', $id)->first();
        if (empty($brand)) {
            return redirect('404');
        }
        $arr['brand'] = $brand;
        // 品牌酒店
        $hotels = Hotel::getBrandHotelList($brand['brand_code']);
        $arr['hotels'] = $hotels;
        // 住宿优惠
        // 获取品牌下所有酒店code
        $hotels_codes = Hotel::getEsBrandHotelCodes($brand['brand_code']);
        $offer = HotelOffer::getHotelOfferList($hotels_codes);
        $arr['offer'] = $offer;

        return view('about.brands-detail', $arr);
    }

    public function partners()
    {
        return view('about.partners');
    }

    public function news(Request $request)
    {
        $source = $request->route('source', 'news_list');
        $year = $request->get('year', date('Y'));
        $page = $request->get('page', 1);
        $page_size = $request->get('page_size', 20);
        $res = null;
        if ($source == 'wechat_list') {
            $res = WechatHighlightController::getWechatList($year, $page, $page_size);
        } else {
            $res = NewsController::getNewsList($year, $page, $page_size);
        }

        $yearsList = [date('Y'), date('Y', strtotime('-1 year')), date('Y', strtotime('-2 year'))];

        return view('about.news', ['year' => $year, 'news' => $res['list'], 'total' => $res['total'], 'yearsList' => $yearsList, 'source' => $source]);
    }

    public function newsDetail($id)
    {
        $article = NewsController::getNewsDetail($id);
        $next = News::where('id', '>', $id)->first('id');
        $prev = News::where('id', '<', $id)->value('id');

        return view('about.news-detail', ['article' => $article, 'next' => $next, 'prev' => $prev]);
    }

    public function wechatNews(Request $request)
    {
        $year = $request->get('year', date('Y'));
        $res = NewsController::getNewsList($year, 1, 20);
        $yearsList = [date('Y'), date('Y', strtotime('-1 year')), date('Y', strtotime('-2 year'))];

        return view('about.news', ['year' => $year, 'news' => $res['list'], 'total' => $res['total'], 'yearsList' => $yearsList, 'source' => $source]);
    }

    public function regentSeven()
    {
        return view('about.regent-seven-seas-cruises');
    }

    public function activityDetail($id)
    {
        if (empty($id)) {
            return redirect('404');
        }
        $article = Activity::where('id', $id)->first();
        $arr['article'] = $article;

        return view('rich-content', $arr);
    }

    /**
     * 获取新闻列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function newsList(Request $request)
    {
        $year = $request->get('year', '');
        $page = $request->get('page', 1);
        $page_size = $request->get('page_size', 20);

        $res = NewsController::getNewsList($year, $page, $page_size);

        return successResponse($res, 'true');
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\View\View|object
     */
    public function sitemap()
    {
        // 获取所有品牌下的酒店
        $arr['brands'] = HotelBrand::getBrandChildHotelList();

        return view('sitemap', $arr);
    }
}
