<?php

namespace App\Http\Controllers;

use App\Models\Continent;
use App\Models\Hotel;
use App\Models\HotelBrand;
use App\Models\HotelDining;
use App\Models\HotelInterest;
use App\Models\HotelLoop;
use App\Models\HotelOffer;
use App\Models\HotelOffersType;
use App\Models\HotelRom;
use App\Models\HotelType;
use App\Models\Room;
use App\Services\ElasticsearchService;
use Illuminate\Http\Request;

class HotelController extends BaseController
{
    public function list()
    {
        return view('hotel.list');
    }

    public function detail($id)
    {
        $hotel = Hotel::where('id', $id)->first();
        if (empty($hotel)) {
            return redirect('404');
        }
        // 酒店系列
        $arr['interest'] = [];
        if ($hotel->interests) {
            $interests = array_column($hotel->interests, 'id');
            $Interest = HotelInterest::whereIn('original_id', $interests)->select(['original_id', 'name'])->get()->toArray();
            $arr['interest'] = $Interest;
        }
        // 酒店设施
        $arr['roms'] = [];
        if ($hotel->feature) {
            $rom_id = array_column($hotel->feature, 'id');
            $roms = HotelRom::whereIn('original_id', $rom_id)->select(['original_id', 'name', 'image'])->get()->toArray();
            $arr['roms'] = $roms;
        }
        $images = [
            ['label' => '酒店', 'images' => $hotel['images'] ?? []],
            ['label' => '房型', 'images' => Room::getESImages($hotel->code)],
            ['label' => '活动', 'images' => HotelOffer::getESImages($hotel->code)],
            ['label' => '餐厅', 'images' => HotelDining::getImages($hotel->code)],
        ];
        // 筛选掉images为空的数组元素
        $images = array_filter($images, function ($item) {
            return ! empty($item['images']) && count($item['images']) > 0;
        });
        // 图片集合
        $arr['images'] = $images;
        $arr['hotel'] = $hotel;
        // 房型介绍
        $room = Room::getEsRoomList($hotel->code, $hotel->brand_code_cn);
        $arr['room'] = $room;
        // 酒店住宿优惠
        $offer = HotelOffer::getHotelOfferList($hotel->code);
        $arr['offer'] = $offer;
        // 餐饮与健康
        $dining = HotelDining::getHotelDiningList($hotel->code);
        $arr['dining'] = $dining;
        // 其他酒店
        $city = $hotel->city_code;
        $arr['other'] = [];
        $arr['city_name'] = '';
        if (! empty($city)) {
            $service = new ElasticsearchService('hotel');
            $result = $service->searchHotel([
                'city_code' => $city[0]['name'] ?? '',
                'page_size' => 10,
                'page' => 1,
            ]);
            foreach ($result['data'] as $key => &$value) {
                $value['image'] = $value['images'][0] ?? '';
            }
            $arr['other'] = $result['data'];
            $arr['city_name'] = $city[0]['name'] ?? '';
        }

        return view('hotel.hotel-detail', $arr);
    }

    public function detailUrl(Request $request)
    {
        $currentUrl = $request->path();
        $url = '/'.$currentUrl;
        $hotel = Hotel::where('url', $url)->first();
        if (empty($hotel)) {
            return redirect('404');
        }
        // 酒店系列
        $arr['interest'] = [];
        if ($hotel->interests) {
            $interests = array_column($hotel->interests, 'id');
            $Interest = HotelInterest::whereIn('original_id', $interests)->select(['original_id', 'name'])->get()->toArray();
            $arr['interest'] = $Interest;
        }
        // 酒店设施
        $arr['roms'] = [];
        if ($hotel->feature) {
            $rom_id = array_column($hotel->feature, 'id');
            $roms = HotelRom::whereIn('original_id', $rom_id)->select(['original_id', 'name', 'image'])->get()->toArray();
            $arr['roms'] = $roms;
        }
        $images = [
            ['label' => '酒店', 'images' => $hotel['images'] ?? []],
            ['label' => '房型', 'images' => Room::getESImages($hotel->code)],
            ['label' => '活动', 'images' => HotelOffer::getESImages($hotel->code)],
            ['label' => '餐厅', 'images' => HotelDining::getImages($hotel->code)],
        ];
        // 筛选掉images为空的数组元素
        $images = array_filter($images, function ($item) {
            return ! empty($item['images']) && count($item['images']) > 0;
        });
        // 图片集合
        $arr['images'] = $images;
        $arr['hotel'] = $hotel;
        // 房型介绍
        $room = Room::getEsRoomList($hotel->code, $hotel->brand_code_cn);
        $arr['room'] = $room;
        // 酒店住宿优惠
        $offer = HotelOffer::getHotelOfferList($hotel->code);
        $arr['offer'] = $offer;
        // 餐饮与健康
        $dining = HotelDining::getHotelDiningList($hotel->code);
        $arr['dining'] = $dining;
        // 其他酒店
        $city = $hotel->city_code;
        $arr['other'] = [];
        $arr['city_name'] = '';
        if (! empty($city)) {
            $service = new ElasticsearchService('hotel');
            $result = $service->searchHotel([
                'city_code' => $city[0]['name'] ?? '',
                'page_size' => 10,
                'page' => 1,
            ]);
            foreach ($result['data'] as $key => &$value) {
                $value['image'] = $value['images'][0] ?? '';
            }
            $arr['other'] = $result['data'];
            $arr['city_name'] = $city[0]['name'] ?? '';
        }

        return view('hotel.hotel-detail', $arr);
    }

    /**
     * 搜索酒店
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\View\View|object
     */
    public function search($keyword)
    {
        // 酒店类型
        $category = HotelType::getTypeList();
        $arr['category'] = $category;
        // 酒店系列
        $loop = HotelLoop::getLoopList();
        $arr['loop'] = $loop;
        // 酒店设施
        $roms = HotelRom::getRomList();
        $arr['roms'] = $roms;
        $arr['slug'] = $keyword;
        $type = HotelOffersType::geTypeList();
        $arr['type'] = $type;

        return view('search.index', $arr);
    }

    /**
     * 获取品牌列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function home(Request $request)
    {
        // 最新酒店
        $list['hotel'] = Hotel::where('new_hotel_start_date', '<', time())
            ->where('new_hotel_end_date', '>', time())
            ->select(['id', 'hotel_name', 'country_code', 'city_code', 'hotelcode', 'images','url'])
            ->get();
        foreach ($list['hotel'] as $key => &$value) {
            $value['image'] = $value['images'][0] ?? '';
        }
        // 目的地
        $list['continent'] = Continent::select(['id', 'name', 'image'])->get();
        // 品牌
        $list['hotel_brand'] = HotelBrand::select(['id', 'name', 'logo'])->get();

        return successResponse($list, 'true');
    }
}
