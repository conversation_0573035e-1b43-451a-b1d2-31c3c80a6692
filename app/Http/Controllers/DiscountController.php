<?php

namespace App\Http\Controllers;

use App\Models\Hotel;
use App\Models\HotelOffer;
use App\Models\HotelOffersType;

class DiscountController extends BaseController
{
    public function index()
    {
        $type = HotelOffersType::geTypeList();
        $arr['type'] = $type;

        return view('discount.index', $arr);
    }

    public function search()
    {
        $type = HotelOffersType::geTypeList();
        $arr['type'] = $type;

        return view('discount.search', $arr);
    }

    public function detail($id)
    {
        $offer = HotelOffer::where('id', $id)
            ->select(['id', 'title', 'offer_types', 'hotels', 'url', 'related_rate_codes', 'hotel_name', 'start_date', 'end_date', 'stay_start_date', 'stay_end_date', 'description', 'offerIncludes', 'terms_conditions', 'image','rate_code1','rate_code2','rate_code3','rate_code4'])
            ->first();
        if (! $offer) {
            return redirect('404');
        }
        $hotels = Hotel::whereIn('code', $offer->hotels)->select(['id', 'hotelcode', 'brand_code', 'longitude', 'latitude', 'brand_name', 'address', 'hotel_name', 'synxis_hotel_id', 'synxis_chain_id', 'city_code','country_code','url'])->get();
        $offer['hotel_id'] = $hotels[0]['id'] ?? '';
        $offer['brand_code'] = $hotels[0]['brand_code'] ?? '';
        $offer['brand_name'] = $hotels[0]['brand_name'] ?? '';
        $offer['brand_img'] = $hotels[0]['brand_img'] ?? '';
        $offer['longitude'] = $hotels[0]['longitude'] ?? '';
        $offer['latitude'] = $hotels[0]['latitude'] ?? '';
        $offer['address'] = $hotels[0]['address'] ?? '';
        $offer['city_name'] = $hotels[0]['city_name'] ?? '';
        $offer['country_name'] = $hotels[0]['country_name'] ?? '';
        $offer['hotel_name'] = $hotels[0]['hotel_name'] ?? '';
        $rate_code=[$offer['rate_code1'],$offer['rate_code2'],$offer['rate_code3'],$offer['rate_code4']];
        //过滤掉空数组
        $rate_code= array_filter($rate_code, fn($v) => !empty($v));
        $offer['rate_code'] = $rate_code;
        $arr['offer'] = $offer;
        $arr['hotels'] = $hotels;
        return view('discount.detail', $arr);
    }
}
