<?php

namespace App\Http\Controllers;

use App\Mail\ResetPwd;
use App\Models\Traits\UserTrait;
use App\Models\User;
use App\Models\User as UserModel;
use App\Models\UserCollect;
use App\Services\GhaHttpService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class AuthController extends BaseController
{
    use UserTrait;

    public function login(Request $request)
    {
        if (Auth::guard('web')->check()) {
            // 跳转
            return redirect('/user/dashboard');
        }

        return view('auth.login');
    }

    public function register()
    {
        $arr['tips'] = [
            [
                'title' => '高额优惠的住宿体验',
                'msg' => '免费注册GHA会员忠诚计划，即可享受全球多地的酒店及度假村的800多项超值优惠及体验',
            ],
            [
                'title' => '额外获得奖励金回馈',
                'msg' => '会员消费即可获最高7%的高额DISCOVERY奖励金回馈货币，用于下一次的灵活消费',
            ],
            [
                'title' => '所心所欲的体验探索',
                'msg' => '会员无需入住，即可畅享本地生活优惠及特色体验优惠，无需远行也能制造难忘的旅行瞬间',
            ],
        ];

        return view('auth.register', $arr);
    }

    public function active()
    {
        return view('auth.active');
    }

    public function resetPassword()
    {
        return view('auth.resetPassword');
    }

    public function resetPasswordConfirm(Request $request)
    {
        $arr['token'] = $request->get('token');
        $arr['email'] = $request->get('email');

        return view('auth.resetPasswordConfirm', $arr);
    }

    public function forgetAccount()
    {
        return view('auth.forgetAccount');
    }

    public function loginUser(Request $request)
    {
        $data = $request->all();
        $email = $data['email'];
        $password = $data['password'];

        // 调用 Gha 登录接口
        $gha = new GhaHttpService;
        $userInfo = $gha->post('memberLogin', [
            'login' => $email,
            'password' => $password,
            'primaryResultsOnlyYN' => 'Y',
        ], ['include_datamart_memberships' => 'ACTIVE']);

        if ($userInfo['code'] != 200) {
            return errorResponse($userInfo['message']);
        }

        $userData = $userInfo['data'];

        try {
            // 查询本地用户，没有就创建
            $user = User::where('email', $email)->first();
            if (! $user) {
                $user = User::create([
                    'name' => $userData['first_name'].$userData['last_name'],
                    'email' => $email,
                    'password' => bcrypt($password),
                    'first_name' => $userData['first_name'],
                    'last_name' => $userData['last_name'],
                    'country' => $userData['country'] ?? '',
                    'city' => $userData['city'] ?? '',
                    'is_message' => 1,
                    'is_auth' => 1,
                    'status' => User::IS_SHOW,
                ]);
            }

            // 更新 profile 信息
            $user->profile_id = $userData['member_balance']['gha_name_ID'] ?? null;
            $user->membership_card_no = $userData['member_balance']['membership_card_no'] ?? null;
            $user->membership_level = $userData['member_balance']['membership_level'] ?? null;
            $user->save();

            // 登录 session
            Auth::login($user);

            // 创建 API token
            $token = $user->createToken('api-token')->plainTextToken;

            // 缓存用户信息
            Cache::put('user_'.$user->id, $user, 60 * 60 * 24);

            return successResponse([
                'token' => $token,
                'token_type' => 'Bearer',
                'user' => $userData,
            ], '登录成功');

        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     *  用户注册
     *
     * @return JsonResponse
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    public function userRegister(Request $request)
    {
        $data = $request->all();
        if (User::where('email', $data['email'])->first()) {
            return errorResponse('该邮箱已注册过', 500);
        }
        if ($data['password'] != $data['confirm_password']) {
            return errorResponse('两次密码不一致', 500);
        }
        $password = bcrypt($data['password']);

        // 调用平台注册接口
        $gha = new GhaHttpService;
        $userInfo = $gha->post('createMember', [
            'email' => $data['email'],
            'password' => $data['password'],
            'firstName' => $data['first_name'],
            'lastName' => $data['last_name'],
            'ghaMarketingYn' => $data['is_message'],
            'enrollmentCode' => 'AN', // todo字段待定
            'language' => $data['language'],
        ]);
        if ($userInfo['code'] != 200) {
            return errorResponse($userInfo['message'], 500);
        }
        DB::beginTransaction();
        try {
            $user = User::create([
                'name' => $data['first_name'].$data['last_name'],
                'email' => $data['email'],
                'password' => $password,
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'country' => $data['country'],
                'city' => $data['city'],
                'is_message' => $data['is_message'],
                'is_auth' => $data['is_auth'],
                'status' => User::IS_SHOW,
            ]);
            $user->profile_id = $userInfo['data']['profileId'];
            $user->membership_card_no = $userInfo['data']['membershipCardNo'];
            $user->membership_level = $userInfo['data']['membershipLevel'] ?? null;
            $user->save();
            DB::commit();
            // 发送邮件
            //            $html = file_get_contents(resource_path('email/register.html'));
            //            // 定义替换数组
            //            $replacements = [
            //                '{{$email}}' => $user['email'],
            //                '{{$database_id}}' => $userInfo['data']['profileId'],
            //                '{{$language}}'=> $data['language'],
            //                '{{$first_name}}' =>$userInfo['data']['firstName'],
            //                '{{$last_name}}' =>$userInfo['data']['lastName'],
            //                '{{$membership_level}}' => $userInfo['data']['membershipLevel'],
            //                '{{$$membership_card_no}}'=>$userInfo['data']['membershipCardNo'],
            //            ];
            //            // 执行替换
            //            foreach ($replacements as $search => $replace) {
            //                $html = str_replace($search, $replace, $html);
            //            }
            //            dispatch(function () use ($user, $html) {
            //                Mail::to($user->email)->send(new ResetPwd("欢迎来到一个回报丰厚的新世界！", $html));
            //            })->afterResponse();

            // 创建 API token
            $token = $user->createToken('api-token')->plainTextToken;
            // 缓存用户信息
            Cache::put('user_'.$user->id, $user, 60 * 60 * 24);
            // 登录 session
            Auth::loginUsingId($user->id);

            return successResponse([
                'token' => $token,
                'token_type' => 'Bearer',
                'user' => $user,
            ], '登录成功');
        } catch (\Exception $exception) {
            DB::rollBack();

            return errorResponse($exception->getMessage(), 500);
        }
    }

    /**
     * 发送重置密码邮件
     *
     * @return JsonResponse
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function resetPwd(Request $request)
    {
        $this->validate($request->all(), [
            'email' => 'required|email',
        ]);
        $user = UserModel::where('email', $request->email)->first();
        if (! $user) {
            return errorResponse('用户不存在');
        }
        $gha = new GhaHttpService;
        $info = $gha->post('resetPassword', [
            'login' => $request->email,
            'ghaInitiatedForgotPWemail_YN' => 'Y',
        ]);
        if ($info['code'] != 200) {
            return errorResponse($info['message']);
        }
        //        return successResponse($info['data']['content'],'重置密码邮件已发送');
        // 发送邮件
        //        dispatch(function () use ($request,$info) {
        //        Mail::to($request->email)->send(new ResetPwd("重置密码", "请点击此链接重置密码：" .
        //            url('/auth/reset-password-confirm?token=' . $info['data']['resetPasswordToken'] . "&email=" . $request->email)));
        //        })->afterResponse();

        return successResponse(['email' => $request->email], '重置密码邮件已发送');
    }

    /**
     * 重置密码
     *
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function setPwd(Request $request)
    {
        $this->validate($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:8|regex:/[A-Z]/|regex:/[a-z]/|regex:/[0-9]/|regex:/[^A-Za-z0-9]/',
            'confirm_password' => 'sometimes|required',
            'token' => 'required',
        ]);
        try {
            $res = $this->userResetPwd($request->all());

            return successResponse($res, '重置密码成功');
        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function activeU(Request $request)
    {
        $this->validate($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:8|regex:/[A-Z]/|regex:/[a-z]/|regex:/[0-9]/|regex:/[^A-Za-z0-9]/',
            'confirm_password' => 'sometimes|required',
            'membershipCardNo' => ' required',
            'ghaMarketingYn' => 'required',
        ]);
        try {
            $res = $this->activeUser($request->all());

            return successResponse($res, '激活成功');
        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     * 用户退出登录
     *
     * @return JsonResponse
     */
    public function logout(Request $request)
    {
        try {
            // 获取当前认证用户
            $user = auth()->guard('web')->user();
            // 删除用户的所有令牌（撤销所有访问权限）
            $user->tokens()->delete();
            Auth::logout();
            $request->session()->invalidate();

            return successResponse(null, '退出登录成功');
        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     * 获取个人信息
     *
     * @return JsonResponse
     */
    public function userInfo(Request $request)
    {
        try {
            // 获取当前认证用户
            $user = auth()->guard('web')->user();
            $gha = new GhaHttpService;
            $info = $gha->get('getProfile', [
                'profile_id' => $user->profile_id,
                'last_name' => $user->last_name,
                'include_datamart_memberships' => 'ALL',
            ]);
            if ($info['code'] != 200) {
                return errorResponse($info['message']);
            }
            $list = $info['data'];

            return successResponse($list, 'true');
        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     * 修改密码
     *
     * @return JsonResponse
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function upPwd(Request $request)
    {
        try {
            // 获取当前认证用户
            $user = auth()->guard('web')->user();
            $this->validate($request->all(), [
                'new_password' => 'required',
                'confirm_password' => 'required',
                'old_password' => 'required',
            ], [
                'new_password' => '新密码必填',
                'confirm_password' => '确认密码必填',
                'old_password' => '旧密码必填',
            ]);
            $param = $request->all();
            if ($param['new_password'] != $param['confirm_password']) {
                return errorResponse('两次密码不一致', 500);
            }
            if ($param['new_password'] == $param['old_password']) {
                return errorResponse('新密码与旧密码不能一样', 500);
            }
            $param['profile_id'] = $user->profile_id;
            $param['email'] = $user->email;
            $res = $this->userUpPwd($param);

            return successResponse($res, '修改密码成功');
        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     * 收藏
     */
    public function addCollect(Request $request)
    {
        if (! auth()->guard('web')->check()) {
            return errorResponse('请先登录', 401);
        }
        $this->validate($request->all(), [
            'type' => 'required',
            'id' => 'required',
        ], [
            'type' => '类型必填',
            'id' => 'id必填',
        ]);
        try {
            $res = UserCollect::addCollect($request->all());

            return successResponse($res['data'], $res['msg']);
        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }
}
