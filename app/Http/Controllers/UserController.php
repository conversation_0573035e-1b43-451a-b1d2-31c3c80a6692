<?php

namespace App\Http\Controllers;

use App\Admin\Controllers\UserPreferencesConfigController;
use App\Services\GhaHttpService;
use Illuminate\Support\Facades\Cache;

class UserController extends BaseController
{
    public function dashboard()
    {
        if (! AuthCheck()) {
            return redirect('/auth/login');
        }
        $user = $this->getUserInfo();
        $arr['user'] = $user;

        return view('user.dashboard', $arr);
    }

    public function orders()
    {
        if (! AuthCheck()) {
            return redirect('/auth/login');
        }
        $user = $this->getUserInfo();
        $arr['user'] = $user;

        return view('user.orders', $arr);
    }

    public function favorite()
    {
        if (! AuthCheck()) {
            return redirect('/auth/login');
        }
        $user = $this->getUserInfo();
        $arr['user'] = $user;

        return view('user.favorite', $arr);
    }

    public function profile()
    {
        if (! AuthCheck()) {
            return redirect('/auth/login');
        }
        $user = $this->getUserInfo();
        $arr['user'] = $user;

        return view('user.profile', $arr);
    }

    public function settings()
    {
        if (! AuthCheck()) {
            return redirect('/auth/login');
        }
        $user = $this->getUserInfo();
        $arr['user'] = $user;
        // 喜好设置
        $preferences = UserPreferencesConfigController::getPreferences(AuthUser()->id, $user['preferences']);
        $arr['preferences'] = $preferences;

        return view('user.settings', $arr);
    }

    public function resetPassword()
    {
        if (! AuthCheck()) {
            return redirect('/auth/login');
        }
        $user = $this->getUserInfo();
        $arr['user'] = $user;

        return view('user.reset-password', $arr);
    }

    public function getUserInfo()
    {
        $userInfo = Cache::get('user_profile_'.AuthUser()->id);
        if ($userInfo) {
            return $userInfo;
        }
        // 调用平台注册接口
        $gha = new GhaHttpService;
        $userInfo = $gha->get('getProfile', [
            'profile_id' => AuthUser()->profile_id,
            'last_name' => AuthUser()->last_name,
            'include_datamart_memberships' => 'ALL',
        ]);

        if ($userInfo['code'] != 200) {
            return errorResponse($userInfo['message']);
        }
        $user_info = $userInfo['data'];
        Cache::set('user_profile_'.AuthUser()->id, $user_info, 24 * 60 * 60);

        return $user_info;
    }
}
