<?php

namespace App\Http\Controllers\V1;

use App\DTOs\SimplifiedReservationRequestDTO;
use App\Events\ReservationStatusChangedEvent;
use App\Exceptions\SabreApiException;
use App\Http\Controllers\Controller;
use App\Services\SabreAvailabilityService;
use App\Services\SabreHotelService;
use App\Services\SabreReservationService;
use App\Services\SabreReservationTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SabreController extends Controller
{
    protected SabreAvailabilityService $availabilityService;

    protected SabreReservationService $reservationService;

    protected SabreHotelService $hotelService;

    protected SabreReservationTransformer $reservationTransformer;

    public function __construct(
        SabreAvailabilityService $availabilityService,
        SabreReservationService $reservationService,
        SabreHotelService $hotelService,
        SabreReservationTransformer $reservationTransformer
    ) {
        $this->availabilityService = $availabilityService;
        $this->reservationService = $reservationService;
        $this->hotelService = $hotelService;
        $this->reservationTransformer = $reservationTransformer;
    }

    /**
     * 查询酒店可用性
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer',
            'startDate' => 'required|date|after:today',
            'endDate' => 'required|date|after:startDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAge' => 'string|regex:/^(\d{1,2})(,\d{1,2})*(;\d{1,2}(,\d{1,2})*)*$/',
            //            'loyaltyProgram' => 'string',
            //            'loyaltyLevel' => 'string',
            //            'accessType' => 'string|in:Promotion,Group,Corporate',
            'accessCode' => 'string',
            'onlyPromo' => 'boolean',
            //            'format' => 'string|in:sabre,standard', // 响应格式：sabre原始格式或standard标准格式
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }
        $params = $request->all();
        $params['accessType'] = 'Promotion'; // Promotion,Group,Corporate
        $params['loyaltyProgram'] = 'GHA';
        $params['loyaltyLevel'] = 'RED';
        $params['format'] = 'standard';

        // 验证儿童年龄与儿童数量是否匹配
        if ($request->has('children') && $request->has('childrenAge')) {
            $childrenCount = $request->input('children', 0);
            $childrenAge = $request->input('childrenAge', '');

            if ($childrenCount > 0 && ! empty($childrenAge)) {
                $ageValidation = $this->validateChildrenAge($childrenCount, $childrenAge, $request->input('numRooms', 1));
                if (! $ageValidation['valid']) {
                    return errorResponse($ageValidation['message']);
                }
            }
        }

        try {
            $format = $request->input('format', 'standard'); // 默认使用标准格式

            // 根据参数选择不同的查询方法
            if ($format === 'standard') {
                // 返回标准格式
                if ($request->boolean('onlyPromo') && ! empty($params['accessCode'])) {
                    $result = $this->availabilityService->checkPromoRateOnlyStandard($params);
                } elseif (! empty($params['accessCode'])) {
                    $result = $this->availabilityService->checkMemberAndNonMemberRatesWithPromoStandard($params);
                } elseif (($params['numRooms'] ?? 1) > 1) {
                    $result = $this->availabilityService->checkMultiRoomAvailabilityStandard($params);
                } else {
                    $result = $this->availabilityService->checkMemberAndNonMemberRatesStandard($params);
                }
                $rooms = $result->toArray();

                return successResponse($rooms);
            } else {
                // 返回Sabre原始格式
                if ($request->boolean('onlyPromo') && ! empty($params['accessCode'])) {
                    $result = $this->availabilityService->checkPromoRateOnly($params);
                } elseif (! empty($params['accessCode'])) {
                    $result = $this->availabilityService->checkMemberAndNonMemberRatesWithPromo($params);
                } elseif (($params['numRooms'] ?? 1) > 1) {
                    $result = $this->availabilityService->checkMultiRoomAvailability($params);
                } else {
                    $result = $this->availabilityService->checkMemberAndNonMemberRates($params);
                }

                $parsed = $this->availabilityService->parseAvailabilityResponse($result);

                return successResponse($parsed);
            }

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 创建预订 - 简化版本
     */
    public function createReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            // 基本预订信息
            'hotelId' => 'required|integer',
            'checkInDate' => 'required|date|after:today',
            'checkOutDate' => 'required|date|after:checkInDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAges' => 'array',
            'childrenAges.*' => 'integer|min:0|max:17',

            // 房型和价格信息
            'roomCode' => 'required|string',
            'rateCode' => 'required|string',

            // 主要客人信息（简化）
            'primaryGuest' => 'required|array',
            'primaryGuest.firstName' => 'required|string|max:50',
            'primaryGuest.lastName' => 'required|string|max:50',
            'primaryGuest.email' => 'required|email',
            'primaryGuest.phone' => 'required|string',
            'primaryGuest.address' => 'array',
            'primaryGuest.address.line1' => 'string|max:100',
            'primaryGuest.address.city' => 'string|max:50',
            'primaryGuest.address.state' => 'string|max:50',
            'primaryGuest.address.country' => 'string|size:2',
            'primaryGuest.address.postalCode' => 'string|max:20',

            // 支付信息（简化）
            'payment' => 'required|array',
            'payment.cardType' => 'required|string',
            'payment.cardNumber' => 'required|string',
            'payment.cardHolder' => 'required|string|max:50',
            'payment.expiryMonth' => 'required|integer|min:1|max:12',
            'payment.expiryYear' => 'required|integer|min:'.date('Y'),

            // 可选参数
            //            'loyaltyNumber' => 'string',
            //            'promoCode' => 'string',
            //            'specialRequests' => 'string|max:500',
            //            'sendConfirmationEmail' => 'boolean',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }
        try {
            // 创建简化预订请求DTO
            $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($request->all());
            /**
             * 会员格式
             */
            // 使用转换服务将简化参数转换为Sabre格式
            $hasLoyalty = $simplifiedRequest->hasLoyalty();
            $hasPromo = $simplifiedRequest->hasPromo();

            $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                $simplifiedRequest,
                $hasLoyalty,
                $hasPromo
            );

            // 根据参数选择不同的创建方法
            if ($hasLoyalty && $hasPromo) {
                $result = $this->reservationService->createSingleRoomBookingWithMembershipAndPromo($sabreParams);
            } elseif ($hasLoyalty) {
                $result = $this->reservationService->createSingleRoomBookingWithMembership($sabreParams);
            } elseif ($hasPromo) {
                $result = $this->reservationService->createSingleRoomBookingNonMemberWithPromo($sabreParams);
            } else {
                $result = $this->reservationService->createSingleRoomBookingNonMember($sabreParams);
            }
            Log::info('订单创建结果：'.json_encode($result));
            // 订单创建成功后触发事件
            if (isset($result['success']) && $result['success']) {
                $this->dispatchReservationStatusEvent($result, 'CREATE', $request);
            }

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试'.$e->getMessage(), 500);
        }
    }

    /**
     * 查询预订
     */
    public function getReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'itineraryNumber' => 'string',
            'confirmationNumber' => 'string',
            'hotelId' => 'required_with:confirmationNumber|integer',
            'chainId' => 'integer',
            'channel' => 'string',
            'format' => 'string|in:sabre,standard,simplified,availability', // 响应格式：sabre原始格式、standard标准格式、simplified简化格式或availability房间格式
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        if (! $request->has('itineraryNumber') && ! $request->has('confirmationNumber')) {
            return errorResponse('必须提供行程号或确认号', 400);
        }

        try {
            $params = $request->all();
            $format = $request->input('format', 'simplified'); // 默认使用简化格式

            $result = $this->reservationService->getReservation($params);

            // 根据format参数决定返回格式
            if ($format === 'availability') {
                // 返回与 availability 接口一致的房间格式
                return successResponse($result);
            } elseif ($format === 'standard') {
                // 返回标准格式
                return successResponse($result);
            } elseif ($format === 'simplified') {
                // 返回简化格式
                return successResponse($result);
            } else {
                // sabre 格式 - 返回原始Sabre格式（保持向后兼容）
                if (isset($result['success']) && $result['success']) {
                    return successResponse($result['data']['reservations'][0] ?? []);
                } else {
                    return successResponse($result);
                }
            }

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 统一预订处理接口（支持create、modify、cancel操作）
     */
    public function processReservation(Request $request): JsonResponse
    {
        $status = $request->input('status');

        if (! $status) {
            return errorResponse('缺少操作类型(status)参数', 400);
        }

        switch ($status) {
            case 'create':
                return $this->handleCreateReservation($request);
            case 'modify':
                return $this->handleModifyReservation($request);
            case 'cancel':
                return $this->handleCancelReservation($request);
            default:
                return errorResponse('无效的操作类型，支持的类型：create, modify, cancel', 400);
        }
    }

    /**
     * 修改预订（支持单房间和多房间）
     */
    public function modifyReservation(Request $request): JsonResponse
    {
        return $this->handleModifyReservation($request);
    }

    /**
     * 处理修改预订请求
     */
    private function handleModifyReservation(Request $request): JsonResponse
    {
        // 检查是否是多房间修改请求
        $isMultiRoom = $request->has('rooms') && is_array($request->input('rooms'));

        if ($isMultiRoom) {
            return $this->modifyMultiRoomReservation($request);
        } else {
            return $this->modifySingleRoomReservation($request);
        }
    }

    /**
     * 处理创建预订请求
     */
    private function handleCreateReservation(Request $request): JsonResponse
    {
        // 检查是否是批量创建
        if ($request->has('rooms') && is_array($request->input('rooms'))) {
            return $this->createMultiRoomReservation($request);
        } else {
            return $this->createReservation($request);
        }
    }

    /**
     * 处理取消预订请求
     */
    private function handleCancelReservation(Request $request): JsonResponse
    {
        return $this->cancelReservation($request);
    }

    /**
     * 创建多房间预订（统一格式）
     */
    private function createMultiRoomReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            // 基本预订信息
            'hotelId' => 'required|integer',
            'checkInDate' => 'required|date|after:today',
            'checkOutDate' => 'required|date|after:checkInDate',

            // 房间数组
            'rooms' => 'required|array|min:1|max:10',
            'rooms.*.roomCode' => 'required|string',
            'rooms.*.rateCode' => 'required|string',
            'rooms.*.adults' => 'integer|min:1|max:20',
            'rooms.*.children' => 'integer|min:0|max:10',
            'rooms.*.childrenAges' => 'array',
            'rooms.*.childrenAges.*' => 'integer|min:0|max:17',

            // 主要客人信息
            'primaryGuest' => 'required|array',
            'primaryGuest.firstName' => 'required|string|max:50',
            'primaryGuest.lastName' => 'required|string|max:50',
            'primaryGuest.email' => 'required|email',
            'primaryGuest.phone' => 'required|string',
            'primaryGuest.address' => 'array',
            'primaryGuest.address.line1' => 'string|max:100',
            'primaryGuest.address.city' => 'string|max:50',
            'primaryGuest.address.state' => 'string|max:50',
            'primaryGuest.address.country' => 'string|size:2',
            'primaryGuest.address.postalCode' => 'string|max:20',

            // 支付信息
            'payment' => 'required|array',
            'payment.cardType' => 'required|string',
            'payment.cardNumber' => 'required|string',
            'payment.cardHolder' => 'required|string|max:50',
            'payment.expiryMonth' => 'required|integer|min:1|max:12',
            'payment.expiryYear' => 'required|integer|min:'.date('Y'),

            // 可选参数
            'loyaltyNumber' => 'string',
            'promoCode' => 'string',
            'specialRequests' => 'string|max:500',
            'sendConfirmationEmail' => 'boolean',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        // 验证每个房间的儿童年龄
        foreach ($request->input('rooms', []) as $index => $room) {
            if (isset($room['children']) && $room['children'] > 0 && isset($room['childrenAges'])) {
                $childrenCount = $room['children'];
                $childrenAges = $room['childrenAges'];

                if (count($childrenAges) !== $childrenCount) {
                    return errorResponse('房间 '.($index + 1)." 的儿童数量({$childrenCount})与提供的年龄数量(".count($childrenAges).')不匹配', 400);
                }

                foreach ($childrenAges as $age) {
                    if ($age < 0 || $age > 17) {
                        return errorResponse('房间 '.($index + 1)." 的儿童年龄必须在0-17岁之间，当前: {$age}岁", 400);
                    }
                }
            }
        }

        try {
            // 将统一格式转换为批量创建格式
            $reservations = [];
            foreach ($request->input('rooms') as $room) {
                $reservationData = array_merge($request->only([
                    'hotelId', 'checkInDate', 'checkOutDate', 'primaryGuest', 'payment',
                    'loyaltyNumber', 'promoCode', 'specialRequests', 'sendConfirmationEmail',
                ]), [
                    'roomCode' => $room['roomCode'],
                    'rateCode' => $room['rateCode'],
                    'adults' => $room['adults'] ?? 1,
                    'children' => $room['children'] ?? 0,
                    'childrenAges' => $room['childrenAges'] ?? [],
                    'numRooms' => 1, // 每个房间都是单独的预订
                ]);

                $reservations[] = $reservationData;
            }

            $results = [];
            $itineraryNumber = null;
            $successCount = 0;
            $failedCount = 0;

            // 遍历每个房间进行下单
            foreach ($reservations as $index => $reservationData) {
                try {
                    // 如果不是第一个订单，添加行程号
                    if ($itineraryNumber) {
                        $reservationData['itineraryNumber'] = $itineraryNumber;
                    }
                    $reservationData['payment']['cardType'] = getCardType($reservationData['payment']['cardNumber']);

                    // 创建简化预订请求DTO
                    $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($reservationData);

                    // 使用转换服务将简化参数转换为Sabre格式
                    $hasLoyalty = $simplifiedRequest->hasLoyalty();
                    $hasPromo = $simplifiedRequest->hasPromo();

                    $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                        $simplifiedRequest,
                        $hasLoyalty,
                        $hasPromo
                    );

                    // 根据参数选择不同的创建方法
                    if ($hasLoyalty && $hasPromo) {
                        $result = $this->reservationService->createSingleRoomBookingWithMembershipAndPromo($sabreParams);
                    } elseif ($hasLoyalty) {
                        $result = $this->reservationService->createSingleRoomBookingWithMembership($sabreParams);
                    } elseif ($hasPromo) {
                        $result = $this->reservationService->createSingleRoomBookingNonMemberWithPromo($sabreParams);
                    } else {
                        $result = $this->reservationService->createSingleRoomBookingNonMember($sabreParams);
                    }

                    // 记录结果
                    $results[] = [
                        'success' => $result['success'] ?? false,
                        'room_index' => $index,
                        'confirmation_number' => $result['confirmation_number'] ?? null,
                        'room_code' => $reservationData['roomCode'],
                        'rate_code' => $reservationData['rateCode'],
                    ];

                    if ($result['success'] ?? false) {
                        $successCount++;

                        // 从第一个成功的订单获取行程号
                        if ($itineraryNumber === null) {
                            $itineraryNumber = $result['itinerary_number'] ?? null;
                        }

                        // 订单创建成功后触发事件
                        $this->dispatchReservationStatusEvent($result, 'CREATE',
                            new Request($reservationData));
                    } else {
                        $failedCount++;
                    }

                } catch (SabreApiException $e) {
                    $failedCount++;
                    $results[] = [
                        'success' => false,
                        'room_index' => $index,
                        'error' => $e->getUserMessage(),
                        'room_code' => $reservationData['roomCode'],
                        'rate_code' => $reservationData['rateCode'],
                    ];
                } catch (\Exception $e) {
                    $failedCount++;
                    $results[] = [
                        'success' => false,
                        'room_index' => $index,
                        'error' => '系统错误，请稍后再试',
                        'room_code' => $reservationData['roomCode'],
                        'rate_code' => $reservationData['rateCode'],
                    ];
                }
            }

            // 构建响应数据
            $responseData = [
                'success' => $successCount > 0,
                'itinerary_number' => $itineraryNumber,
                'total_rooms' => count($results),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'rooms' => $results,
            ];

            return successResponse($responseData);

        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试'.$e->getMessage(), 500);
        }
    }

    /**
     * 修改单房间预订
     */
    protected function modifySingleRoomReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            // 修改预订必需的确认号
            'CRS_confirmationNumber' => 'required|string',

            // 基本预订信息（与创建预订相同）
            'hotelId' => 'required|integer',
            'checkInDate' => 'required|date',
            'checkOutDate' => 'required|date|after:checkInDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAges' => 'array',
            'childrenAges.*' => 'integer|min:0|max:17',

            // 房型和价格信息（与创建预订相同）
            'roomCode' => 'required|string',
            'rateCode' => 'required|string',

            // 主要客人信息（与创建预订相同）
            'primaryGuest' => 'required|array',
            'primaryGuest.firstName' => 'required|string|max:50',
            'primaryGuest.lastName' => 'required|string|max:50',
            'primaryGuest.email' => 'required|email',
            'primaryGuest.phone' => 'required|string',
            'primaryGuest.address' => 'array',
            'primaryGuest.address.line1' => 'string|max:100',
            'primaryGuest.address.city' => 'string|max:50',
            'primaryGuest.address.state' => 'string|max:50',
            'primaryGuest.address.country' => 'string|size:2',
            'primaryGuest.address.postalCode' => 'string|max:20',

            // 支付信息（与创建预订相同）
            'payment' => 'required|array',
            'payment.cardType' => 'required|string',
            'payment.cardNumber' => 'required|string',
            'payment.cardHolder' => 'required|string|max:50',
            'payment.expiryMonth' => 'required|integer|min:1|max:12',
            'payment.expiryYear' => 'required|integer|min:'.date('Y'),

            // 可选参数（与创建预订相同）
            'loyaltyNumber' => 'string',
            'promoCode' => 'string',
            'specialRequests' => 'string|max:500',
            'sendConfirmationEmail' => 'boolean',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        try {
            // 创建简化预订请求DTO
            $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($request->all());

            // 使用转换服务将简化参数转换为Sabre格式
            $hasLoyalty = $simplifiedRequest->hasLoyalty();
            $hasPromo = $simplifiedRequest->hasPromo();

            $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                $simplifiedRequest,
                $hasLoyalty,
                $hasPromo
            );

            // 添加修改预订特有的确认号
            $sabreParams['CRS_confirmationNumber'] = $request->input('CRS_confirmationNumber');

            $result = $this->reservationService->modifyReservation($sabreParams);

            // 订单修改成功后触发事件（只有用户登录时）
            if (isset($result['success']) && $result['success'] && AuthCheck()) {
                $this->dispatchReservationStatusEvent($result, 'MODIFY', $request);
            }

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试'.$e->getMessage(), 500);
        }
    }

    /**
     * 修改多房间预订
     */
    protected function modifyMultiRoomReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            // 修改预订必需的行程号
            'itineraryNumber' => 'required|string',

            // 基本预订信息
            'hotelId' => 'required|integer',
            'checkInDate' => 'required|date',
            'checkOutDate' => 'required|date|after:checkInDate',

            // 房间数组
            'rooms' => 'required|array|min:1|max:10',
            'rooms.*.roomCode' => 'required|string',
            'rooms.*.rateCode' => 'required|string',
            'rooms.*.adults' => 'integer|min:1|max:20',
            'rooms.*.children' => 'integer|min:0|max:10',
            'rooms.*.childrenAges' => 'array',
            'rooms.*.childrenAges.*' => 'integer|min:0|max:17',
            'rooms.*.status' => 'string|in:modify,create,cancel', // 房间操作类型
            'rooms.*.confirmationNumber' => 'required_if:rooms.*.status,modify,cancel|string', // 修改和取消时需要确认号

            // 主要客人信息
            'primaryGuest' => 'required|array',
            'primaryGuest.firstName' => 'required|string|max:50',
            'primaryGuest.lastName' => 'required|string|max:50',
            'primaryGuest.email' => 'required|email',
            'primaryGuest.phone' => 'required|string',
            'primaryGuest.address' => 'array',
            'primaryGuest.address.line1' => 'string|max:100',
            'primaryGuest.address.city' => 'string|max:50',
            'primaryGuest.address.state' => 'string|max:50',
            'primaryGuest.address.country' => 'string|size:2',
            'primaryGuest.address.postalCode' => 'string|max:20',

            // 支付信息
            'payment' => 'required|array',
            'payment.cardType' => 'required|string',
            'payment.cardNumber' => 'required|string',
            'payment.cardHolder' => 'required|string|max:50',
            'payment.expiryMonth' => 'required|integer|min:1|max:12',
            'payment.expiryYear' => 'required|integer|min:'.date('Y'),

            // 可选参数
            'loyaltyNumber' => 'string',
            'promoCode' => 'string',
            'specialRequests' => 'string|max:500',
            'sendConfirmationEmail' => 'boolean',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        // 验证每个房间的儿童年龄和状态
        foreach ($request->input('rooms', []) as $index => $room) {
            // 验证儿童年龄
            if (isset($room['children']) && $room['children'] > 0 && isset($room['childrenAges'])) {
                $childrenCount = $room['children'];
                $childrenAges = $room['childrenAges'];

                if (count($childrenAges) !== $childrenCount) {
                    return errorResponse('房间 '.($index + 1)." 的儿童数量({$childrenCount})与提供的年龄数量(".count($childrenAges).')不匹配', 400);
                }

                foreach ($childrenAges as $age) {
                    if ($age < 0 || $age > 17) {
                        return errorResponse('房间 '.($index + 1)." 的儿童年龄必须在0-17岁之间，当前: {$age}岁", 400);
                    }
                }
            }

            // 验证房间状态和确认号的关系
            $roomStatus = $room['status'] ?? 'modify';
            if (in_array($roomStatus, ['modify', 'cancel']) && empty($room['confirmationNumber'])) {
                return errorResponse('房间 '.($index + 1)." 的操作类型为 {$roomStatus} 时必须提供确认号", 400);
            }
        }

        try {
            $results = [];
            $itineraryNumber = $request->input('itineraryNumber');
            $successCount = 0;
            $failedCount = 0;

            // 遍历每个房间进行不同的操作（根据status处理）
            foreach ($request->input('rooms') as $index => $room) {
                try {
                    $roomStatus = $room['status'] ?? 'modify';

                    // 构建基本房间参数
                    $singleRoomParams = array_merge($request->only([
                        'hotelId', 'checkInDate', 'checkOutDate', 'primaryGuest', 'payment',
                        'loyaltyNumber', 'promoCode', 'specialRequests', 'sendConfirmationEmail',
                    ]), [
                        'roomCode' => $room['roomCode'],
                        'rateCode' => $room['rateCode'],
                        'adults' => $room['adults'] ?? 1,
                        'children' => $room['children'] ?? 0,
                        'childrenAges' => $room['childrenAges'] ?? [],
                        'numRooms' => 1,
                    ]);

                    $result = null;

                    switch ($roomStatus) {
                        case 'create':
                            // 创建新房间 - 添加到现有订单
                            if ($itineraryNumber) {
                                $singleRoomParams['itineraryNumber'] = $itineraryNumber;
                            }
                            $result = $this->processCreateRoom($singleRoomParams);
                            break;

                        case 'cancel':
                            // 取消房间
                            $result = $this->processCancelRoom($room['confirmationNumber'], $request->input('hotelId'));
                            break;

                        case 'modify':
                        default:
                            // 修改现有房间
                            $singleRoomParams['CRS_confirmationNumber'] = $room['confirmationNumber'];
                            $result = $this->processModifyRoom($singleRoomParams);
                            break;
                    }

                    $results[] = [
                        'success' => $result['success'] ?? false,
                        'room_index' => $index,
                        'status' => $roomStatus,
                        'confirmation_number' => $result['confirmation_number'] ?? $room['confirmationNumber'] ?? null,
                        'room_code' => $room['roomCode'],
                        'rate_code' => $room['rateCode'],
                        'operation' => $roomStatus,
                    ];

                    if ($result['success'] ?? false) {
                        $successCount++;
                    } else {
                        $failedCount++;
                    }

                } catch (SabreApiException $e) {
                    $failedCount++;
                    $results[] = [
                        'success' => false,
                        'room_index' => $index,
                        'status' => $room['status'] ?? 'modify',
                        'error' => $e->getUserMessage(),
                        'room_code' => $room['roomCode'],
                        'rate_code' => $room['rateCode'],
                        'operation' => $room['status'] ?? 'modify',
                    ];
                } catch (\Exception $e) {
                    $failedCount++;
                    $results[] = [
                        'success' => false,
                        'room_index' => $index,
                        'status' => $room['status'] ?? 'modify',
                        'error' => '系统错误，请稍后再试',
                        'room_code' => $room['roomCode'],
                        'rate_code' => $room['rateCode'],
                        'operation' => $room['status'] ?? 'modify',
                    ];
                }
            }

            // 构建统一的响应格式
            $responseData = [
                'success' => $successCount > 0,
                'itinerary_number' => $itineraryNumber,
                'total_rooms' => count($results),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'rooms' => $results,
            ];

            // 订单修改成功后触发事件（只有用户登录时）
            if ($successCount > 0 && AuthCheck()) {
                $this->dispatchReservationStatusEvent($responseData, 'MODIFY', $request);
            }

            return successResponse($responseData);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试'.$e->getMessage(), 500);
        }
    }

    /**
     * 处理创建房间操作
     */
    private function processCreateRoom(array $singleRoomParams): array
    {
        try {
            // 创建简化预订请求DTO
            $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($singleRoomParams);

            // 使用转换服务将简化参数转换为Sabre格式
            $hasLoyalty = $simplifiedRequest->hasLoyalty();
            $hasPromo = $simplifiedRequest->hasPromo();

            $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                $simplifiedRequest,
                $hasLoyalty,
                $hasPromo
            );

            // 根据参数选择不同的创建方法
            if ($hasLoyalty && $hasPromo) {
                return $this->reservationService->createSingleRoomBookingWithMembershipAndPromo($sabreParams);
            } elseif ($hasLoyalty) {
                return $this->reservationService->createSingleRoomBookingWithMembership($sabreParams);
            } elseif ($hasPromo) {
                return $this->reservationService->createSingleRoomBookingNonMemberWithPromo($sabreParams);
            } else {
                return $this->reservationService->createSingleRoomBookingNonMember($sabreParams);
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '创建房间失败: '.$e->getMessage(),
            ];
        }
    }

    /**
     * 处理修改房间操作
     */
    private function processModifyRoom(array $singleRoomParams): array
    {
        try {
            // 创建简化预订请求DTO
            $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($singleRoomParams);

            // 使用转换服务将简化参数转换为Sabre格式
            $hasLoyalty = $simplifiedRequest->hasLoyalty();
            $hasPromo = $simplifiedRequest->hasPromo();

            $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                $simplifiedRequest,
                $hasLoyalty,
                $hasPromo
            );

            // 添加修改预订特有的确认号
            $sabreParams['CRS_confirmationNumber'] = $singleRoomParams['CRS_confirmationNumber'];

            return $this->reservationService->modifyReservation($sabreParams);

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '修改房间失败: '.$e->getMessage(),
            ];
        }
    }

    /**
     * 处理取消房间操作
     */
    private function processCancelRoom(string $confirmationNumber, int $hotelId): array
    {
        try {
            return $this->reservationService->cancelMultipleReservations(
                [$confirmationNumber],
                $hotelId
            );

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '取消房间失败: '.$e->getMessage(),
            ];
        }
    }

    /**
     * 取消预订
     */
    public function cancelReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'confirmationNumber' => 'array|nullable',
            'confirmationNumber.*' => 'string',
            'itineraryNumber' => 'string|nullable',
            'hotelId' => 'required|integer',
            'hotelCode' => 'string',
            'channel' => 'string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        // 检查必须提供确认号或行程号中的一个
        $confirmationNumbers = $request->input('confirmationNumber');
        $itineraryNumber = $request->input('itineraryNumber');

        if (empty($confirmationNumbers) && empty($itineraryNumber)) {
            return errorResponse('必须提供确认号或行程号', 400);
        }

        if (! empty($confirmationNumbers) && ! empty($itineraryNumber)) {
            return errorResponse('不能同时提供确认号和行程号', 400);
        }

        try {
            // 优先使用确认号，支持批量取消
            if (! empty($confirmationNumbers)) {
                $result = $this->reservationService->cancelMultipleReservations(
                    $confirmationNumbers,
                    $request->input('hotelId'),
                    $request->input('hotelCode')
                );
            } else {
                // 使用行程号取消逻辑
                $result = $this->reservationService->cancelReservationByItinerary(
                    $itineraryNumber,
                    $request->input('hotelId'),
                    $request->input('hotelCode'),
                    $request->input('channel', 'DSCVRYLYLTY')
                );
            }

            // 订单取消成功后触发事件（只有用户登录时）
            if (isset($result['success']) && $result['success'] && AuthCheck()) {
                // 从结果或请求中获取必要的数据来触发事件
                $this->dispatchCancelReservationEvent($result, $request, $itineraryNumber, $confirmationNumbers);
            }

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 获取酒店详情
     */
    public function getHotelDetails(Request $request, int $hotelId): JsonResponse
    {
        try {
            $options = [];
            if ($request->has('include')) {
                $options['include'] = $request->input('include');
            }

            $result = $this->hotelService->getHotelDetails($hotelId, $options);

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 获取酒店支付方式
     */
    public function getHotelPaymentMethods(int $hotelId): JsonResponse
    {
        try {
            $result = $this->hotelService->getHotelPaymentMethods($hotelId);

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 清除缓存
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            $type = $request->input('type', 'all');
            $hotelId = $request->input('hotelId');

            switch ($type) {
                case 'availability':
                    $this->availabilityService->clearAvailabilityCache($request->all());
                    break;
                case 'hotel':
                    $this->hotelService->clearHotelDetailsCache($hotelId);
                    break;
                case 'all':
                default:
                    $this->availabilityService->clearAvailabilityCache();
                    $this->hotelService->clearHotelDetailsCache();
                    break;
            }

            return successResponse();

        } catch (\Exception $e) {
            return errorResponse('清除缓存失败: '.$e->getMessage(), 500);
        }
    }

    /**
     * 查询单个酒店的最低会员价和对应非会员价
     */
    public function checkHotelLowestMemberAndNonMemberPrices(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer|min:1',
            'startDate' => 'required|date|after:today',
            'endDate' => 'required|date|after:startDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAge' => 'string|regex:/^(\d{1,2})(,\d{1,2})*(;\d{1,2}(,\d{1,2})*)*$/',
            'accessCode' => 'string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        $params = $request->all();
        $params['format'] = 'standard';

        // 验证儿童年龄与儿童数量是否匹配
        if ($request->has('children') && $request->has('childrenAge')) {
            $childrenCount = $request->input('children', 0);
            $childrenAge = $request->input('childrenAge', '');

            if ($childrenCount > 0 && ! empty($childrenAge)) {
                $ageValidation = $this->validateChildrenAge($childrenCount, $childrenAge, $request->input('numRooms', 1));
                if (! $ageValidation['valid']) {
                    return errorResponse($ageValidation['message']);
                }
            }
        }

        try {
            $result = $this->availabilityService->checkHotelLowestMemberAndNonMemberPricesStandard($params);

            // 获取房型相关信息并添加到响应中
            $rooms = $result;
            // 记录查询统计
            if ($this->shouldLogQuery()) {
                Log::info('酒店会员价和非会员价查询', [
                    'hotel_id' => $params['hotelId'],
                    'success' => ! empty($rooms['rooms']),
                    'rooms_count' => count($rooms['rooms']),
                ]);
            }

            return successResponse($rooms);

        } catch (\InvalidArgumentException $e) {
            return errorResponse($e->getMessage(), 400);
        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            Log::error('查询酒店会员价和非会员价系统错误', [
                'error' => $e->getMessage(),
                'params' => $params,
            ]);

            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 验证儿童年龄格式和数量
     */
    protected function validateChildrenAge(int $childrenCount, string $childrenAge, int $numRooms): array
    {
        // 按房间分割年龄字符串
        $roomAges = explode(',', $childrenAge);

        $totalAgeCount = 0;
        foreach ($roomAges as $roomIndex => $roomAgeStr) {
            if (empty($roomAgeStr)) {
                continue;
            }

            $ages = explode(',', $roomAgeStr);
            foreach ($ages as $age) {
                $age = trim($age);

                // 验证年龄是否为数字
                if (! is_numeric($age)) {
                    return [
                        'valid' => false,
                        'message' => "无效的年龄格式: {$age}",
                    ];
                }

                $ageInt = (int) $age;

                // 验证年龄范围
                if ($ageInt < 0 || $ageInt > 17) {
                    return [
                        'valid' => false,
                        'message' => "儿童年龄必须在0-17岁之间，当前: {$ageInt}岁",
                    ];
                }

                $totalAgeCount++;
            }
        }

        // 验证总年龄数量是否与儿童数量匹配
        if ($totalAgeCount != $childrenCount) {
            return [
                'valid' => false,
                'message' => "儿童数量({$childrenCount})与提供的年龄数量({$totalAgeCount})不匹配",
            ];
        }

        return ['valid' => true, 'message' => ''];
    }

    /**
     * 判断是否应该记录查询日志
     */
    protected function shouldLogQuery(): bool
    {
        return config('sabre.logging.enabled', false) &&
               config('sabre.logging.log_queries', true);
    }

    /**
     * 触发订单状态变更事件
     */
    protected function dispatchReservationStatusEvent(array $reservationData, string $status, Request $request): void
    {
        try {
            // 从返回的数据中提取必要信息 - 兼容多种字段名格式
            $itineraryNumber = $reservationData['itinerary_number'] ?? $reservationData['itineraryNumber'] ?? null;
            $chainId = $reservationData['chainId'] ?? $request->input('chainId', 'GHA');
            $hotelId = $request->input('hotelId');

            // 构建预订详情 - 兼容多种字段名格式
            $reservationDetails = [];
            $confirmationNumber = $reservationData['confirmation_number'] ?? $reservationData['confirmationNumber'] ?? null;

            if (isset($reservationData['confirmationNumbers']) && is_array($reservationData['confirmationNumbers'])) {
                foreach ($reservationData['confirmationNumbers'] as $confirmNum) {
                    $reservationDetails[] = [
                        'confirmation_number' => $confirmNum,
                        'member_rate_yn' => $request->has('loyaltyNumber') && ! empty($request->input('loyaltyNumber')),
                    ];
                }
            } elseif ($confirmationNumber) {
                $reservationDetails[] = [
                    'confirmation_number' => $confirmationNumber,
                    'member_rate_yn' => $request->has('loyaltyNumber') && ! empty($request->input('loyaltyNumber')),
                ];
            }

            // 只有当用户登录时才触发状态上报事件
            if ($itineraryNumber && $hotelId && ! empty($reservationDetails) && AuthCheck()) {
                $profileId = AuthUser()->id ?? null;
                ReservationStatusChangedEvent::dispatch(
                    $itineraryNumber,
                    $status,
                    (string) $chainId,
                    (string) $hotelId,
                    $reservationDetails,
                    $profileId
                );

                Log::info('订单状态事件已触发', [
                    'itinerary_number' => $itineraryNumber,
                    'status' => $status,
                    'hotel_id' => $hotelId,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('触发订单状态事件失败', [
                'error' => $e->getMessage(),
                'status' => $status,
                'data' => $reservationData,
            ]);
        }
    }

    /**
     * 触发取消订单事件
     */
    protected function dispatchCancelReservationEvent(array $result, Request $request, ?string $itineraryNumber, ?array $confirmationNumbers): void
    {
        try {
            $hotelId = $request->input('hotelId');
            $chainId = $request->input('chainId', 'GHA');
            $profileId = $request->input('profileId') ?? $request->input('profile_id');

            // 如果有行程号，直接使用
            if ($itineraryNumber) {
                $reservationDetails = [];
                if (isset($result['data']['cancelledReservations']) && is_array($result['data']['cancelledReservations'])) {
                    foreach ($result['data']['cancelledReservations'] as $cancelled) {
                        $reservationDetails[] = [
                            'confirmation_number' => $cancelled['confirmationNumber'] ?? '',
                            'member_rate_yn' => false, // 取消时默认为false
                        ];
                    }
                }

                if (! empty($reservationDetails)) {
                    ReservationStatusChangedEvent::dispatch(
                        $itineraryNumber,
                        'CANCEL',
                        (string) $chainId,
                        (string) $hotelId,
                        $reservationDetails,
                        $profileId
                    );
                }
            }
            // 如果有确认号数组，需要为每个确认号触发事件
            elseif ($confirmationNumbers && is_array($confirmationNumbers)) {
                foreach ($confirmationNumbers as $confirmationNumber) {
                    $reservationDetails = [
                        [
                            'confirmation_number' => $confirmationNumber,
                            'member_rate_yn' => false,
                        ],
                    ];

                    // 对于确认号取消，可能没有行程号，使用确认号作为标识
                    ReservationStatusChangedEvent::dispatch(
                        $confirmationNumber, // 临时使用确认号作为行程号
                        'CANCEL',
                        (string) $chainId,
                        (string) $hotelId,
                        $reservationDetails,
                        $profileId
                    );
                }
            }

            Log::info('取消订单事件已触发', [
                'itinerary_number' => $itineraryNumber,
                'confirmation_numbers' => $confirmationNumbers,
                'hotel_id' => $hotelId,
            ]);
        } catch (\Exception $e) {
            Log::error('触发取消订单事件失败', [
                'error' => $e->getMessage(),
                'itinerary_number' => $itineraryNumber,
                'confirmation_numbers' => $confirmationNumbers,
            ]);
        }
    }

    /**
     * 批量创建预订
     */
    public function createBatchReservations(Request $request): JsonResponse
    {
        $validator = Validator::make(['data' => $request->all()], [
            'data' => 'required|array|min:1|max:10',
            'data.*' => 'array',

            // 每个订单的基本预订信息
            'data.*.hotelId' => 'required|integer',
            'data.*.checkInDate' => 'required|date|after:today',
            'data.*.checkOutDate' => 'required|date|after:data.*.checkInDate',
            'data.*.numRooms' => 'integer|min:1|max:10',
            'data.*.adults' => 'integer|min:1|max:20',
            'data.*.children' => 'integer|min:0|max:10',
            'data.*.childrenAges' => 'array',
            'data.*.childrenAges.*' => 'integer|min:0|max:17',

            // 房型和价格信息
            'data.*.roomCode' => 'required|string',
            'data.*.rateCode' => 'required|string',

            // 主要客人信息
            'data.*.primaryGuest' => 'required|array',
            'data.*.primaryGuest.firstName' => 'required|string|max:50',
            'data.*.primaryGuest.lastName' => 'required|string|max:50',
            'data.*.primaryGuest.email' => 'required|email',
            'data.*.primaryGuest.phone' => 'required|string',
            'data.*.primaryGuest.address' => 'array',
            'data.*.primaryGuest.address.line1' => 'string|max:100',
            'data.*.primaryGuest.address.city' => 'string|max:50',
            'data.*.primaryGuest.address.state' => 'string|max:50',
            'data.*.primaryGuest.address.country' => 'string|size:2',
            'data.*.primaryGuest.address.postalCode' => 'string|max:20',

            // 支付信息
            'data.*.payment' => 'required|array',
            'data.*.payment.cardNumber' => 'required|string',
            'data.*.payment.cardHolder' => 'required|string|max:50',
            'data.*.payment.expiryMonth' => 'required|integer|min:1|max:12',
            'data.*.payment.expiryYear' => 'required|integer|min:'.date('Y'),
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        try {
            $reservations = $request->all();
            $results = [];
            $itineraryNumber = null;
            $successCount = 0;
            $failedCount = 0;
            $createdReservations = []; // 用于回滚

            // 遍历每个订单进行下单
            foreach ($reservations as $index => $reservationData) {
                try {
                    // 如果不是第一个订单，添加行程号
                    if ($itineraryNumber) {
                        $reservationData['itineraryNumber'] = $itineraryNumber;
                    }
                    $reservationData['payment']['cardType'] = getCardType($reservationData['payment']['cardNumber']);
                    // 创建简化预订请求DTO
                    $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($reservationData);

                    // 使用转换服务将简化参数转换为Sabre格式
                    $hasLoyalty = $simplifiedRequest->hasLoyalty();
                    $hasPromo = $simplifiedRequest->hasPromo();

                    $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                        $simplifiedRequest,
                        $hasLoyalty,
                        $hasPromo
                    );

                    // 根据参数选择不同的创建方法
                    if ($hasLoyalty && $hasPromo) {
                        $result = $this->reservationService->createSingleRoomBookingWithMembershipAndPromo($sabreParams);
                    } elseif ($hasLoyalty) {
                        $result = $this->reservationService->createSingleRoomBookingWithMembership($sabreParams);
                    } elseif ($hasPromo) {
                        $result = $this->reservationService->createSingleRoomBookingNonMemberWithPromo($sabreParams);
                    } else {
                        $result = $this->reservationService->createSingleRoomBookingNonMember($sabreParams);
                    }

                    // 记录结果 - 简化版本
                    $results[] = [
                        'index' => $index,
                        'success' => $result['success'] ?? false,
                        'reservation_id' => $result['reservation_id'] ?? null,
                        'confirmation_number' => $result['confirmation_number'] ?? null,
                        'itinerary_number' => $result['itinerary_number'] ?? null,
                        'status' => $result['status'] ?? null,
                    ];

                    if ($result['success'] ?? false) {
                        $successCount++;
                        $createdReservations[] = $result;

                        // 从第一个成功的订单获取行程号
                        if ($itineraryNumber === null) {
                            $itineraryNumber = $result['itinerary_number'] ?? null;
                            Log::info('获取到行程号：'.$itineraryNumber);
                        }

                        // 订单创建成功后触发事件
                        $this->dispatchReservationStatusEvent($result, 'CREATE',
                            new Request($reservationData));
                    } else {
                        $failedCount++;
                        Log::error('批量下单中第'.($index + 1).'个订单失败', $result);
                    }

                } catch (SabreApiException $e) {
                    $failedCount++;
                    $errorResult = [
                        'index' => $index,
                        'success' => false,
                        'error' => $e->getUserMessage(),
                        'error_code' => $e->getCode(),
                    ];
                    $results[] = $errorResult;
                    Log::error('批量下单中第'.($index + 1).'个订单异常', [
                        'error' => $e->getMessage(),
                        'reservation_data' => $reservationData,
                    ]);
                } catch (\Exception $e) {
                    $failedCount++;
                    $errorResult = [
                        'index' => $index,
                        'success' => false,
                        'error' => '系统错误，请稍后再试',
                        'error_code' => 500,
                    ];
                    $results[] = $errorResult;
                    Log::error('批量下单中第'.($index + 1).'个订单系统异常', [
                        'error' => $e->getMessage(),
                        'reservation_data' => $reservationData,
                    ]);
                }
            }

            // 构建清晰的响应数据
            $responseData = [
                'itinerary_number' => $itineraryNumber,
                'total_reservations' => count($reservations),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'reservations' => $results,
            ];

            $message = $successCount > 0 ?
                ($failedCount > 0 ? "部分下单成功：{$successCount}个成功，{$failedCount}个失败" : '全部下单成功') :
                '全部下单失败';

            Log::info('批量下单完成', [
                'itinerary_number' => $itineraryNumber,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
            ]);

            return successResponse($responseData, $message);

        } catch (\Exception $e) {
            Log::error('批量下单系统错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 查询酒店可用性 - 返回每个房型的最低会员价和非会员价
     */
    public function getRoomTypesPricing(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer|min:1',
            'startDate' => 'required|date|after:today',
            'endDate' => 'required|date|after:startDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAge' => 'string|regex:/^(\d{1,2})(,\d{1,2})*(;\d{1,2}(,\d{1,2})*)*$/',
            'accessCode' => 'string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        $params = $request->all();

        // 验证儿童年龄与儿童数量是否匹配
        if ($request->has('children') && $request->has('childrenAge')) {
            $childrenCount = $request->input('children', 0);
            $childrenAge = $request->input('childrenAge', '');

            if ($childrenCount > 0 && ! empty($childrenAge)) {
                $ageValidation = $this->validateChildrenAge($childrenCount, $childrenAge, $request->input('numRooms', 1));
                if (! $ageValidation['valid']) {
                    return errorResponse($ageValidation['message'], 400);
                }
            }
        }

        try {
            // 调用Service层方法
            $result = $this->availabilityService->getRoomTypesPricing($params);

            // 记录查询统计
            if ($this->shouldLogQuery()) {
                Log::info('酒店房型价格查询', [
                    'hotel_id' => $params['hotelId'],
                    'success' => ! empty($result['room_types']),
                    'room_types_count' => $result['total_room_types'],
                ]);
            }

            return successResponse($result, '获取房型价格成功');

        } catch (\InvalidArgumentException $e) {
            return errorResponse($e->getMessage(), 400);
        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            Log::error('查询酒店房型价格系统错误', [
                'error' => $e->getMessage(),
                'params' => $params,
            ]);

            return errorResponse('系统错误，请稍后再试', 500);
        }
    }
}
