<?php

namespace App\Http\Controllers\V1;

use App\Models\Activity;
use App\Models\Banner;
use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Hot;
use App\Models\Hotel;
use App\Models\HotelBrand;
use App\Models\HotelDining;
use App\Models\HotelInterest;
use App\Models\HotelLoop;
use App\Models\HotelOffer;
use App\Models\HotelOffersType;
use App\Models\HotelRom;
use App\Models\HotelType;
use App\Models\Room;
use App\Models\UserCollect;
use App\Services\ElasticsearchService;
use App\Services\GhaHttpService;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HotelController extends BaseController
{
    /**
     * 小程序首页
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        // 轮播图
        $banner = Banner::getBannerList(1, 1);
        $arr['banner'] = $banner;
        // 会员尊享
        $huiyuan = Activity::getActivityType(3, 1);
        $arr['huiyuan'] = $huiyuan;
        // 全新探索
        $quanxin = Activity::getActivityType(4, 1);
        $arr['quanxin'] = $quanxin;

        return successResponse($arr, 'true');
    }

    /**
     * 获取品牌列表
     *
     * @return JsonResponse
     */
    public function home(Request $request)
    {
        $banners = Banner::getBannerList(1, 1);
        // 轮播图
        $list['banners'] = $banners;
        // 最新酒店
        $list['hotel'] = Hotel::where('new_hotel_start_date', '<', time())
            ->where('new_hotel_end_date', '>', time())
            ->select(['id', 'hotel_name', 'country_code', 'city_code', 'hotelcode', 'images','url'])
            ->get();
        foreach ($list['hotel'] as $key => &$value) {
            $value['image'] = $value['images'][0] ?? '';
        }
        // 目的地
        $list['continent'] = Continent::getContinentList();
        // 品牌
        $list['hotel_brand'] = HotelBrand::getBrand();

        return successResponse($list, 'true');
    }

    /**
     * 酒店详情
     *
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function detail(Request $request)
    {
        $this->validate($request->all(), [
            'id' => 'required',
        ]);
        $id = $request->id;
        $hotel = Hotel::where('id', $id)->first();
        if (empty($hotel)) {
            return successResponse([], 'true');
        }
        $hotel->image = $hotel['images'][0] ?? '';
        // 酒店系列
        $arr['interest'] = [];
        if ($hotel->interests) {
            $interests = array_column($hotel->interests, 'id');
            $Interest = HotelInterest::whereIn('original_id', $interests)->select(['original_id', 'name'])->get()->toArray();
            $arr['interest'] = $Interest;
        }
        $arr['roms'] = [];
        // 酒店设施
        if ($hotel->feature) {
            $rom_id = array_column($hotel->feature, 'id');
            $roms = HotelRom::whereIn('original_id', $rom_id)->select(['original_id', 'name', 'image'])->get()->toArray();
            $arr['roms'] = $roms;
        }
        // 图片集合
        $arr['images'] = [
            ['key' => 'hotel', 'label' => '酒店', 'images' => $hotel['images'] ?? []],
            ['key' => 'room', 'label' => '房型', 'images' => Room::getEsImages($hotel->code)],
            ['key' => 'offers', 'label' => '活动', 'images' => HotelOffer::getESImages($hotel->code)],
            ['key' => 'dining', 'label' => '餐厅', 'images' => HotelDining::getImages($hotel->code)],
        ];
        $arr['hotel'] = $hotel;
        // 房型介绍
        $room = Room::getESRoomList($hotel->code, $hotel->brand_name);
        $arr['room'] = $room;
        // 酒店住宿优惠
        $offer = HotelOffer::getHotelOfferList($hotel->code);
        $arr['offer'] = $offer;
        // 餐饮与健康
        $dining = HotelDining::getHotelDiningList($hotel->code);
        $arr['dining'] = $dining;
        // 其他酒店
        $city = $hotel->city_code;

        $arr['other'] = [];
        $arr['city_name'] = '';
        if ($city) {
            $service = new ElasticsearchService('hotel');
            $result = $service->searchHotel([
                'city_code' => $city[0]['name'],
                'page_size' => 10,
                'page' => 1,
            ]);
            foreach ($result['data'] as $key => &$value) {
                $value['image'] = $value['images'][0] ?? '';
            }
            $arr['other'] = $result['data'];
            $arr['city_name'] = $city[0]['name'] ?? '';
        }

        return successResponse($arr, 'true');
    }

    /**
     * 搜索关键词
     *
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function searchKey(Request $request)
    {
        $page = $request->post('page', 1);
        $page_size = $request->post('page_size', 20);
        $keyword = $request->post('keyword', '');
        $hotel_type = $request->post('categories', '');
        $categories = $request->post('series', '');
        $brands = $request->post('brands', '');
        $facts = $request->post('facts', '');
        $type = $request->post('type', '');
        $sortPrice = $request->post('order', null); // 'asc'|'desc' 或 null
        $priceType = $request->post('price_type', 'non_member'); // 'member'|'non_member'
        $this->validate($request->all(), [
            'keyword' => 'required',
        ]);
        $hotel_name = '';
        $country_name = '';
        $city_code = '';
        $name_en = '';
        switch ($type) {
            case 'hotel':
                $hotel_name = $keyword;
                break;
            case 'country':
                $country_name = Country::where('country_name', $keyword)->value('country_name');
                break;
            case 'city':
                $city_code = City::where('city_name', $keyword)->value('city_name');
                break;
            case 'brand':
                $name_en = HotelBrand::where('name', $keyword)->value('name');
                break;
        }
        // 酒店类型
        if ($hotel_type) {
            $hotel_type = explode('_', $hotel_type);
            $hotel_type = HotelType::whereIn('id', $hotel_type)->pluck('name')->toArray();
        }
        // 酒店品牌
        if ($brands) {
            $brands = explode('_', $brands);
            $brands = HotelBrand::whereIn('id', $brands)->pluck('name')->toArray();
        }
        // 酒店系列
        if ($categories) {
            $categories = explode('_', $categories);
            $categories = HotelLoop::whereIn('id', $categories)->pluck('name')->toArray();
        }
        // 酒店设施
        if ($facts) {
            $facts = explode('_', $facts);
            $facts = HotelRom::whereIn('id', $facts)->pluck('name')->toArray();
        }
        // 搜索酒店信息
        $service = new ElasticsearchService('hotel');
        $result = $service->searchHotel([
            'brands' => $brands,
            'feature' => $facts,
            'hotel_type' => $hotel_type,
            'categories' => $categories,
            'hotel_name' => $hotel_name,
            'country_name' => $country_name,
            'city_code' => $city_code,
            'brand_nameEn' => $name_en,
            'keyword' => $keyword,
            'page' => $page,
            'page_size' => $page_size,
            'eco_certificates' => $keyword,
            'neighborhood_tag' => $keyword,
        ]);
        $hotelIds = array_column($result['data'], 'synxis_hotel_id');
        // 从 Redis 批量取得需要的价格类型
        $priceMap = RoomController::getPrices($hotelIds, $priceType);
        // 合并价格到结果
        foreach ($result['data'] as &$h) {
            $h['price'] = $priceMap[$h['synxis_hotel_id']] ?? null;
        }
        unset($h);
        // 按价格排序（如果请求）
        if ($sortPrice) {
            usort($result['data'], function ($a, $b) use ($sortPrice) {
                $pa = $a['price'] ?? null;
                $pb = $b['price'] ?? null;
                // null 直接放到最后
                if ($pa === null && $pb === null) {
                    return 0;
                }
                if ($pa === null) {
                    return 1; // a 在后面
                }
                if ($pb === null) {
                    return -1; // b 在后面
                }

                // 正常比较（0 也参与排序）
                return $sortPrice === 'asc' ? ($pa <=> $pb) : ($pb <=> $pa);
            });
        }

        foreach ($result['data'] as $key => &$value) {
            $value['image'] = $value['images'][0] ?? '';
            // 是否收藏
            $value['is_collect'] = UserCollect::getIsCollect('hotel', $value['id']);
        }

        return successResponse($result, 'true');
    }

    /**
     * 搜索关键词获取对应类型
     *
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function searchObscure(Request $request)
    {
        $this->validate($request->all(), [
            'keyword' => 'required|min:1',
        ], [
            'keyword' => '输入长度不能少于3个字符',
        ]);
        // 查询国家
        $list['country'] = Country::where('country_name', 'like', '%'.$request->keyword.'%')->select(['id', 'country_code', 'country_name'])->limit(5)->get()->each(function ($item) {
            $item['count'] = Hotel::where('country_code', $item->country_code)->count();
        });
        // 城市
        $list['city'] = City::where('city_name', 'like', '%'.$request->keyword.'%')->where('status', City::ON)->select(['id', 'city_code', 'city_name'])->limit(5)->get()->each(function ($item) {
            $item['count'] = Hotel::where('city_code', $item->city_code)->count();
        });
        // 酒店
        $list['hotel'] = Hotel::where('hotel_name', 'like', '%'.$request->keyword.'%')->select(['id', 'hotelcode', 'hotel_name', 'hotel_name_en', 'city_code', 'country_code'])->limit(5)->get()->each(function ($item) {
            $item['city_name'] = City::where('city_code', $item->city_code)->value('city_name');
            $item['country_name'] = Country::where('country_code', $item->country_code)->value('country_name');
        });
        // 品牌
        $list['brand'] = HotelBrand::where('name', 'like', '%'.$request->keyword.'%')->select(['id', 'brand_code', 'name'])->limit(5)->get()->each(function ($item) {
            $item['count'] = Hotel::where('brand_code', $item->brand_code)->count();
        });

        return successResponse($list, 'true');
    }

    /**
     * 搜索预定酒店
     *
     * @return JsonResponse
     *
     * @throws GuzzleException
     */
    public function searchHotels(Request $request)
    {
        $param = $request->all();
        $gha = new GhaHttpService;
        $info = $gha->get('search/hotels', [
            'country' => $param['country'] ?? '',
            'start_date' => $param['start_date'] ?? '',
            'end_date' => $param['end_date'] ?? '',
            'rooms' => $param['rooms'] ?? '',
            'brand_code' => $param['brand_code'] ?? 'AN',
            'room_1_adults' => $param['room_1_adults'] ?? '',
            'room_1_children' => $param['room_1_children'] ?? '',
            'longitude' => $param['longitude'] ?? '',
            'latitude' => $param['latitude'] ?? '',
            'hotel' => $param['hotel'] ?? '',
            'city' => $param['city'] ?? '',
            'limit' => $param['limit'] ?? '10',
            'offset' => $param['offset'] ?? '0',
            'contentType' => $param['contentType'] ?? 'json',
            'skip_rate' => $param['contentType'] ?? 'true',
        ]);
        if ($info['code'] != 200) {
            return errorResponse($info['message']);
        }
        $list = $info['data'];

        return successResponse($list, 'true');
    }

    /**
     * 获取热门关键词
     *
     * @return JsonResponse
     */
    public function searchHot(Request $request)
    {
        // 热门搜索
        $hot = Hot::find(1);

        $list['keyword'] = $hot['keyword'] ?? [];
        // 查询酒店
        $hotel = Hotel::select(['id', 'hotel_name', 'hotel_name_en', 'hotelcode','url'])->whereIn('id', $hot['hotel'])->get();
        // 热门酒店
        $list['hotel'] = $hotel ?? [];
        // 热门目的地
        $city = City::select(['id', 'city_name', 'city_code'])->whereIn('id', $hot['city'])->get();
        $list['city'] = $city ?? [];

        return successResponse($list, 'true');
    }

    /**
     * 获取酒店活动
     *
     * @return JsonResponse
     *
     * @throws GuzzleException
     */
    public function searchActivity(Request $request)
    {
        //        $this->validate($request->all(), [
        //            'country' => 'required',
        //            'hotelcode' => 'required'
        //        ]);
        $param = $request->all();
        $gha = new GhaHttpService;
        $info = $gha->get('search/experiences', [
            'limit' => $param['limit'] ?? '5',
            'experience_id' => $param['experience_id'] ?? '100',
            'bookable_web_site' => $param['bookable_web_site'] ?? 'true',
            'bookable_app' => $param['bookable_app'] ?? 'false',
            'organizer_brands' => $param['organizer_brands'] ?? '',
            'organizer_hotels' => $param['organizer_hotels'] ?? '',
            'city_id' => $param['city_id'] ?? '',
            'country_id' => $param['country_id'] ?? '',
            'begin_booking_date' => $param['begin_booking_date'] ?? '',
            'end_booking_date' => $param['end_booking_date'] ?? '',
            'persons' => $param['persons'] ?? '',
            'category' => $param['category'] ?? '',
            'exclude_brand_ids' => $param['exclude_brand_ids'] ?? '',
            'exclude_hotel_ids' => $param['exclude_hotel_ids'] ?? '',
        ]);
        if ($info['code'] != 200) {
            return errorResponse($info['message']);
        }
        $list = $info['data'];

        return successResponse($list, 'true');

    }

    /**
     * 获取优惠类型
     *
     * @return JsonResponse
     */
    public function offersTypeList(Request $request)
    {
        $list = HotelOffersType::orderBy('sort', 'desc')
            ->select(['id', 'name', 'name_en', 'image'])
            ->get();
        foreach ($list as &$item) {
            if (empty($item['image'])) {
                $item['image'] = '';
            } else {
                $item['image'] = Storage::disk('public')->url($item['image']);
            }
        }

        return successResponse($list, 'true');
    }

    /**
     * @throws Exception
     */
    public function List(Request $request)
    {
        $keyword = $request->input('keyword', '');
        $offer_type = $request->input('offer_type', '');
        $page_size = $request->input('page_size', 10);
        $page = $request->input('page', 1);
        $offer_type = HotelOffersType::where('id', $offer_type)->value('name_en');
        $service = new ElasticsearchService('offers');
        $list = $service->searchOffers([
            'keyword' => $keyword,
            'offer_type' => $offer_type,
            'page' => $page,
            'page_size' => $page_size,
        ]);
        foreach ($list['data'] as &$item) {
            if (AuthCheck()) {
                $isCollect = UserCollect::where('type', 'offer')
                    ->where('user_id', AuthUser()->id)
                    ->where('collect', $item['id'])
                    ->first();
                if ($isCollect) {
                    $item['isCollect'] = 1;
                } else {
                    $item['isCollect'] = 0;
                }
            } else {
                $item['isCollect'] = 0;
            }
        }

        return successResponse($list, 'true');
    }

    /**
     * 活动id
     *
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function offersDetail(Request $request)
    {
        $this->validate($request->all(), [
            'id' => 'required',
        ], [
            'id.required' => '请传入id',
        ]);
        $id = $request->input('id');
        $offer = HotelOffer::where('id', $id)
            ->select(['id', 'title', 'offer_types', 'hotels', 'url', 'related_rate_codes', 'hotel_name', 'end_date', 'stay_start_date', 'stay_end_date', 'description', 'offerIncludes', 'terms_conditions', 'image', 'images', 'offer_types', 'rate_code1', 'rate_code2', 'rate_code3', 'rate_code4'])
            ->first();
        if (! $offer) {
            return errorResponse('没有找到该活动');
        }
        $hotels = Hotel::whereIn('code', $offer->hotels)->select(['id', 'hotel_name', 'hotel_name_en', 'brand_code', 'longitude', 'latitude', 'brand_name', 'address','url'])->get();
        $offer['hotel_id'] = $hotels[0]['id'] ?? '';
        $offer['brand_code'] = $hotels[0]['brand_code'] ?? '';
        $offer['brand_name'] = $hotels[0]['brand_name'] ?? '';
        $offer['brand_img'] = $hotels[0]['brand_img'] ?? '';
        $offer['longitude'] = $hotels[0]['longitude'] ?? '';
        $offer['latitude'] = $hotels[0]['latitude'] ?? '';
        $offer['address'] = $hotels[0]['address'] ?? '';
        $offer['city_name'] = $hotels[0]['city_name'] ?? '';
        $offer['country_name'] = $hotels[0]['country_name'] ?? '暂无';
        // 关联酒店
        $offer['hotels'] = $hotels;

        return successResponse($offer, 'true');
    }
}
