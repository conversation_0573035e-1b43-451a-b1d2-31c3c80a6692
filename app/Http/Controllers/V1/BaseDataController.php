<?php

/**
 * 导入基础数据的时候使用属于临时文件
 */

namespace App\Http\Controllers\V1;

use App\Imports\BrandsImports;
use App\Imports\HotelImports;
use App\Imports\RoomImports;
use App\Models\Hotel;
use App\Models\HotelBrand;
use App\Models\HotelInterest;
use App\Models\HotelLoop;
use App\Models\HotelRom;
use App\Models\HotelType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class BaseDataController extends BaseController
{
    public function brand(Request $request)
    {
        // 存储文件
        if ($request->hasFile('excel_file')) {
            if ($request->file('excel_file')->isValid()) {
                if ($request->file('excel_file')->store('merchant')) {
                    try {
                        Excel::import(new BrandsImports, $request->file('excel_file'));

                        return '导入成功';
                    } catch (\Exception $exception) {
                        Log::error('导入异常', [$exception]);

                        return $exception->getMessage();
                    }
                }
            }
        }
    }

    public function hotel(Request $request)
    {
        // 存储文件
        if ($request->hasFile('excel_file')) {
            if ($request->file('excel_file')->isValid()) {
                if ($request->file('excel_file')->store('merchant')) {
                    try {
                        Excel::import(new HotelImports, $request->file('excel_file'));

                        return '导入成功';
                    } catch (\Exception $exception) {
                        Log::error('导入异常', [$exception]);

                        return $exception->getMessage();
                    }
                }
            }
        }
    }

    public function room(Request $request)
    {
        // 存储文件
        if ($request->hasFile('excel_file')) {
            if ($request->file('excel_file')->isValid()) {
                if ($request->file('excel_file')->store('merchant')) {
                    try {
                        Excel::import(new RoomImports, $request->file('excel_file'));

                        return '导入成功';
                    } catch (\Exception $exception) {
                        Log::error('导入异常', [$exception]);

                        return $exception->getMessage();
                    }
                }
            }
        }
    }

    public function hotelConfig(Request $request)
    {
        // 同步酒店设施
        $featuresCollection = collect();
        $loopCollection = collect();
        $interCollection = collect();
        $hotelTypeCollection = collect();

        Hotel::select('id', 'hotel_type', 'feature', 'categories', 'interests')->chunk(300, function ($hotels) use ($featuresCollection, $loopCollection, $interCollection, $hotelTypeCollection) {
            foreach ($hotels as $hotel) {
                // feature 字段
                $features = $hotel->feature;
                if (! empty($features)) {
                    if (is_string($features)) {
                        $features = json_decode($features, true) ?: [];
                    }
                    $featuresCollection->push(...(array) $features);
                }
                // categories 字段
                $categories = $hotel->categories;
                if (! empty($categories)) {
                    if (is_string($categories)) {
                        $categories = json_decode($categories, true) ?: [];
                    }
                    $loopCollection->push(...(array) $categories);
                }

                $interests = $hotel->interests;
                if (! empty($interests)) {
                    if (is_string($interests)) {
                        $interests = json_decode($interests, true) ?: [];
                    }
                    $interCollection->push(...(array) $interests);
                }

                $hotel_type = $hotel->hotel_type;
                if (! empty($hotel_type)) {
                    if (is_string($hotel_type)) {
                        $hotel_type = json_decode($hotel_type, true) ?: [];
                    }
                    $hotelTypeCollection->push(...(array) $hotel_type);
                }

            }
        });
        // 使用集合处理更安全
        $uniqueFeatures = $featuresCollection
            ->filter() // 移除空值
            ->unique() // 去重
            ->values() // 重置键名
            ->pluck('name', 'id')
            ->toArray();
        if (! empty($uniqueFeatures)) {
            foreach ($uniqueFeatures as $key => $value) {
                // 确保每个特征都是字符串
                $insertData[] = [
                    'original_id' => $key,
                    'name_en' => trim($value),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            // 分批插入
            foreach ($insertData as $chunk) {
                try {
                    $info = HotelRom::where('name_en', $chunk['name_en'])->first();
                    if ($info) {
                        $info->original_id = $chunk['original_id'];
                        $info->name_en = $chunk['name_en'];
                        $info->save();
                    } else {
                        HotelRom::insert($chunk);
                    }
                } catch (\Exception $e) {
                    // 记录错误但继续执行
                    \Log::error('批量插入失败: '.$e->getMessage());
                }
            }

        }

        $uniqueLoops = $loopCollection
            ->filter() // 移除空值
            ->unique() // 去重
            ->values() // 重置键名
            ->pluck('name', 'id')
            ->toArray();

        $insertData = [];
        if (! empty($uniqueLoops)) {
            foreach ($uniqueLoops as $key => $value) {
                // 确保每个特征都是字符串
                $insertData[] = [
                    'original_id' => $key,
                    'name_en' => trim($value),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            // 分批插入
            foreach ($insertData as $chunk) {
                try {
                    $info = HotelLoop::where('name_en', $chunk['name_en'])->first();
                    if ($info) {
                        $info->original_id = $chunk['original_id'];
                        $info->name_en = $chunk['name_en'];
                        $info->save();
                    } else {
                        HotelLoop::insert($chunk);
                    }
                } catch (\Exception $e) {
                    // 记录错误但继续执行
                    \Log::error('批量插入失败: '.$e->getMessage());
                }
            }
        }

        $uniqueInter = $interCollection
            ->filter() // 移除空值
            ->unique() // 去重
            ->values() // 重置键名
            ->pluck('name', 'id')
            ->toArray();

        $insertData = [];
        if (! empty($uniqueInter)) {
            foreach ($uniqueInter as $key => $value) {
                // 确保每个特征都是字符串
                $insertData[] = [
                    'original_id' => $key,
                    'name_en' => trim($value),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            // 分批插入
            foreach ($insertData as $chunk) {
                try {
                    $info = HotelInterest::where('name_en', $chunk['name_en'])->first();
                    if ($info) {
                        $info->original_id = $chunk['original_id'];
                        $info->name_en = $chunk['name_en'];
                        $info->save();
                    } else {
                        HotelInterest::insert($chunk);
                    }
                } catch (\Exception $e) {
                    // 记录错误但继续执行
                    \Log::error('批量插入失败: '.$e->getMessage());
                }
            }
        }

        $uniqueHotelType = $hotelTypeCollection
            ->filter() // 移除空值
            ->unique() // 去重
            ->values() // 重置键名
            ->pluck('name', 'id')
            ->toArray();

        $insertData = [];
        if (! empty($uniqueHotelType)) {
            foreach ($uniqueHotelType as $key => $value) {
                // 确保每个特征都是字符串
                $insertData[] = [
                    'original_id' => $key,
                    'name_en' => trim($value),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            // 分批插入
            foreach ($insertData as $chunk) {
                try {
                    $info = HotelType::where('name_en', $chunk['name_en'])->first();
                    if ($info) {
                        $info->original_id = $chunk['original_id'];
                        $info->name_en = $chunk['name_en'];
                        $info->save();
                    } else {
                        HotelType::insert($chunk);
                    }
                } catch (\Exception $e) {
                    // 记录错误但继续执行
                    \Log::error('批量插入失败: '.$e->getMessage());
                }
            }
        }

        return '同步成功';
    }

    public function hotelUrl(Request $request)
    {
        // 同步酒店设施
        Hotel::select('id', 'hotel_name_en', 'brand_nameEn', 'url')->chunk(300, function ($hotels) {
            foreach ($hotels as $hotel) {
                $brand_nameEn = Str::slug($hotel->brand_nameEn, '-');
                $hotel_name_en = Str::slug($hotel->hotel_name_en, '-');
                if ($brand_nameEn && $hotel_name_en) {
                    $url = '/'.$brand_nameEn.'/'.$hotel_name_en;
                } else {
                    $url = '/'.$hotel_name_en;
                }
                $hotel->url = $url ?? '';
                $hotel->save();
            }
        });

        return '同步成功';
    }

    public function brandConfig(Request $request)
    {
        // 扩展中同步logoSvg
        //        HotelBrand::chunk(300, function ($brands) {
        //            foreach ($brands as $brand) {
        //                if (!empty($brand->extend)) {
        //                    $brand->logo_svg = $brand->extend['logoSvg']??'';
        //                    $brand->save();
        //                }
        //            }
        //        });
        // 同步相关数据
        Hotel::chunk(300, function ($brands) {
            foreach ($brands as $brand) {
                if (! empty($brand->extend)) {
                    $brand->categories = $brand->extend['categories'] ?? null;
                    $brand->neighborhood_tag = $brand->extend['neighborhoodTag'] ?? null;
                    $brand->save();
                }
            }
        });

        return '同步成功';
    }
}
