<?php

namespace App\Http\Controllers\V1;

use App\Admin\Controllers\ResvBaseController;
use App\DTOs\SimplifiedReservationRequestDTO;
use App\Events\ReservationStatusChangedEvent;
use App\Exceptions\SabreApiException;
use App\Models\Hotel;
use App\Models\HotelOffer;
use App\Models\ResvBase;
use App\Models\Room;
use App\Services\GhaHttpService;
use App\Services\GxpService;
use App\Services\SabreAvailabilityService;
use App\Services\SabreHotelService;
use App\Services\SabreReservationService;
use App\Services\SabreReservationTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;

class RoomController extends BaseController
{
    protected SabreAvailabilityService $availabilityService;

    protected SabreReservationService $reservationService;

    protected SabreHotelService $hotelService;

    protected SabreReservationTransformer $reservationTransformer;

    protected GxpService $gxpService;

    public function __construct(
        SabreAvailabilityService $availabilityService,
        SabreReservationService $reservationService,
        SabreHotelService $hotelService,
        SabreReservationTransformer $reservationTransformer,
        GxpService $gxpService
    ) {
        $this->gxpService = $gxpService;
        $this->availabilityService = $availabilityService;
        $this->reservationService = $reservationService;
        $this->hotelService = $hotelService;
        $this->reservationTransformer = $reservationTransformer;

    }

    /**
     * 查询酒店 房间可用性
     *
     * @return JsonResponse
     */
    public function checkAvailability(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer',
            'startDate' => 'required|date|after:today',
            'endDate' => 'required|date|after:startDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAge' => 'nullable|string|regex:/^(\d{1,2})(,\d{1,2})*(;\d{1,2}(,\d{1,2})*)*$/',
            'accessCode' => 'nullable|string',
            'onlyPromo' => 'boolean',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }
        $params = $request->all();
        $params['accessType'] = 'Promotion'; // Promotion,Group,Corporate
        $params['loyaltyProgram'] = 'GHA';
        $params['loyaltyLevel'] = 'RED';
        $params['format'] = 'standard';
        $offers = $request->input('offers', '');
        $rate_code = [];
        if ($offers) {
            $offer = HotelOffer::where('id', $offers)->first();
            if ($offer) {
                $rate_code = [$offer['rate_code1'], $offer['rate_code2'], $offer['rate_code3'], $offer['rate_code4']];
                $rate_code = array_filter($rate_code, fn ($v) => ! empty($v));
            }
        }
        // 验证儿童年龄与儿童数量是否匹配
        if ($request->has('children') && $request->has('childrenAge')) {
            $childrenCount = $request->input('children', 0);
            $childrenAge = $request->input('childrenAge', '');

            if ($childrenCount > 0 && ! empty($childrenAge)) {
                $ageValidation = validateChildrenAge($childrenCount, $childrenAge, $request->input('numRooms', 1));
                if (! $ageValidation['valid']) {
                    return errorResponse($ageValidation['message']);
                }
            }
        }
        // 获取天数
        $day = getDaysBetween($params['startDate'], $params['endDate']);
        // 查询$hotelCode
        $hotelCode = Hotel::getHotelCode($params['hotelId']);

        try {
            // 返回标准格式
            if ($request->boolean('onlyPromo') && ! empty($params['accessCode'])) {
                $result = $this->availabilityService->checkPromoRateOnlyStandard($params);
            } elseif (! empty($params['accessCode'])) {
                $result = $this->availabilityService->checkMemberAndNonMemberRatesWithPromoStandard($params);
            } elseif (($params['numRooms'] ?? 1) > 1) {
                $result = $this->availabilityService->checkMultiRoomAvailabilityStandard($params);
            } else {
                $result = $this->availabilityService->checkMemberAndNonMemberRatesStandard($params);
            }
            $rooms = $result->toArray();
            // 获取用户身份
            if (AuthCheck()) {
                $membership_level = AuthUser()->membership_level ?? 'SILVER';
            } else {
                $membership_level = 'SILVER';
            }
            $gha = new GhaHttpService;
            foreach ($rooms['rooms'] as $key => &$value) {
                $room = Room::getEsRoomCode($hotelCode, $value['roomCode']);
                $value['room_name'] = $room['room_name'] ?? $value['roomName'];
                $value['room_desc'] = $room['room_desc'] ?? $value['roomDescription'];
                $value['room_images'] = $room['images'] ?? [];
                $value['room_size'] = $room['size'] ?? '';
                $value['facility'] = [
                    ['name' => '免费WiFi', 'icon' => 'https://cms.ghadiscovery.com/content/download/565/2567?version=2&inline=1'],
                    ['name' => '免费停车', 'icon' => 'https://cms.ghadiscovery.com/content/download/565/2567?version=2&inline=1'],
                    ['name' => '免费早餐', 'icon' => 'https://cms.ghadiscovery.com/content/download/565/2567?version=2&inline=1'],
                    ['name' => '免费接机', 'icon' => 'https://cms.ghadiscovery.com/content/download/565/2567?version=2&inline=1'],
                ];
                foreach ($value['roomRateList'] as $k => &$v) {
                    $res = $gha->get('potentialAccrual', [
                        'membership_level' => strtoupper($membership_level),
                        'base_amount' => bcdiv($v['cny_total_price'], 100, 2),
                        'currency_code' => 'CNY',
                    ]);
                    // 是否高亮
                    if (in_array($v['rateCode'], $rate_code)) {
                        $v['is_highlight'] = true;
                    } else {
                        $v['is_highlight'] = false;
                    }
                    $v['pay_type'] = '每次预订都需要信用卡';
                    $v['potentialDiscoveryAccrual'] = 0;
                    $v['earningPercentage'] = '0%';
                    $v['baseAmount'] = 0;
                    $v['averagePrice'] = bcdiv($v['cny_total_price'], $day);
                    if ($res['code'] = 200) {
                        $d = $res['data'];
                        $v['potentialDiscoveryAccrual'] = $d['potentialDiscoveryAccrual'] ?? 0;
                        $v['earningPercentage'] = $d['earningPercentage'] ?? '0%';
                        $v['baseAmount'] = $d['baseAmount'] ?? 0;
                    }
                }
            }
        } catch (\Exception $e) {
            return errorResponse('该日期已售罄，建议您尝试其他日期。');
        }

        return successResponse($rooms);
    }

    /**
     * 查询单个酒店的最低会员价和对应非会员价
     */
    public function hotelMinPrices(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer',
            'startDate' => 'required|date|after:today',
            'endDate' => 'required|date|after:startDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAge' => 'nullable|string|regex:/^(\d{1,2})(,\d{1,2})*(;\d{1,2}(,\d{1,2})*)*$/',
            'accessCode' => 'nullable|string',
        ]);
        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }
        $params = $request->all();
        $params['format'] = 'standard';

        // 验证儿童年龄与儿童数量是否匹配
        if ($request->has('children') && $request->has('childrenAge')) {
            $childrenCount = $request->input('children', 0);
            $childrenAge = $request->input('childrenAge', '');

            if ($childrenCount > 0 && ! empty($childrenAge)) {
                $ageValidation = validateChildrenAge($childrenCount, $childrenAge, $request->input('numRooms', 1));
                if (! $ageValidation['valid']) {
                    return errorResponse($ageValidation['message']);
                }
            }
        }

        try {
            $result = $this->availabilityService->checkHotelLowestMemberAndNonMemberPricesStandard($params);
            // 获取房型相关信息并添加到响应中
            $rooms = $result;
            // 记录查询统计
            if (shouldLogQuery()) {
                Log::info('酒店会员价和非会员价查询', [
                    'hotel_id' => $params['hotelId'],
                    'success' => ! empty($rooms['rooms']),
                    'rooms_count' => count($rooms['rooms']),
                ]);
            }
            // 保存至redis
            $this->updateOne($params['hotelId'], $rooms['rooms'][0] ?? []);

            return successResponse($rooms['rooms'][0] ?? $rooms);
        } catch (\InvalidArgumentException $e) {
            return errorResponse($e->getMessage(), 400);
        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            Log::error('查询酒店会员价和非会员价系统错误', [
                'error' => $e->getMessage(),
                'params' => $params,
            ]);

            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    public function roomTypesPricing(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer',
            'startDate' => 'required|date|after:today',
            'endDate' => 'required|date|after:startDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAge' => 'nullable|string|regex:/^(\d{1,2})(,\d{1,2})*(;\d{1,2}(,\d{1,2})*)*$/',
            'accessCode' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        $params = $request->all();

        // 验证儿童年龄与儿童数量是否匹配
        if ($request->has('children') && $request->has('childrenAge')) {
            $childrenCount = $request->input('children', 0);
            $childrenAge = $request->input('childrenAge', '');

            if ($childrenCount > 0 && ! empty($childrenAge)) {
                $ageValidation = validateChildrenAge($childrenCount, $childrenAge, $request->input('numRooms', 1));
                if (! $ageValidation['valid']) {
                    return errorResponse($ageValidation['message']);
                }
            }
        }

        try {
            // 调用Service层方法
            $result = $this->availabilityService->getRoomTypesPricing($params);

            return successResponse($result, '获取房型价格成功');

        } catch (\InvalidArgumentException $e) {
            return errorResponse($e->getMessage(), 400);
        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            Log::error('查询酒店房型价格系统错误', [
                'error' => $e->getMessage(),
                'params' => $params,
            ]);

            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 创建预订 - 简化版本
     */
    public function createReservation(Request $request): JsonResponse
    {
        $data = $request->all();
        $validator = Validator::make($data, [
            // 基本预订信息
            'hotelId' => 'required|integer',
            'checkInDate' => 'required|date|after:today',
            'checkOutDate' => 'required|date|after:checkInDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAges' => 'array',
            'childrenAges.*' => 'integer|min:0|max:17',

            // 房型和价格信息
            'roomCode' => 'required|string',
            'rateCode' => 'required|string',

            // 主要客人信息（简化）
            'primaryGuest' => 'required|array',
            'primaryGuest.firstName' => 'required|string|max:50',
            'primaryGuest.lastName' => 'required|string|max:50',
            'primaryGuest.email' => 'required|email',
            'primaryGuest.phone' => 'required|string',
            'primaryGuest.address' => 'array',
            'primaryGuest.address.line1' => 'string|max:100',
            'primaryGuest.address.city' => 'string|max:50',
            'primaryGuest.address.state' => 'string|max:50',
            'primaryGuest.address.country' => 'string|size:2',
            'primaryGuest.address.postalCode' => 'string|max:20',

            // 支付信息（简化）
            'payment' => 'required|array',
            //            'payment.cardType' => 'required|string',
            'payment.cardNumber' => 'required|string',
            'payment.cardHolder' => 'required|string|max:50',
            'payment.expiryMonth' => 'required',
            'payment.expiryYear' => 'required',

            // 可选参数
            //            'loyaltyNumber' => 'string',
            //            'promoCode' => 'nullable|string',
            //            'specialRequests' => 'nullable|string|max:500',
            //            'sendConfirmationEmail' => 'nullable|boolean',
        ]);
        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }
        $loyaltyNumber = '';
        $loyaltyLevel = '';
        if (AuthCheck()) {
            $loyaltyNumber = AuthUser()->membership_card_no ?? '';
            $loyaltyLevel = AuthUser()->membership_level ?? '';
        }
        $request->merge([
            'sendConfirmationEmail' => false,
            'loyaltyNumber' => $loyaltyNumber,
            'loyaltyLevel' => $loyaltyLevel,
        ]);
        $data = $request->all();
        $data['payment']['cardType'] = getCardType($data['payment']['cardNumber']);
        try {
            // 创建简化预订请求DTO
            $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($data);
            // 使用转换服务将简化参数转换为Sabre格式
            $hasLoyalty = $simplifiedRequest->hasLoyalty();
            $hasPromo = $simplifiedRequest->hasPromo();

            $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                $simplifiedRequest,
                $hasLoyalty,
                $hasPromo
            );
            // 根据参数选择不同的创建方法
            if ($hasLoyalty && $hasPromo) {
                $result = $this->reservationService->createSingleRoomBookingWithMembershipAndPromo($sabreParams);
            } elseif ($hasLoyalty) {
                $result = $this->reservationService->createSingleRoomBookingWithMembership($sabreParams);
            } elseif ($hasPromo) {
                $result = $this->reservationService->createSingleRoomBookingNonMemberWithPromo($sabreParams);
            } else {
                $result = $this->reservationService->createSingleRoomBookingNonMember($sabreParams);
            }
            // 订单创建成功后触发事件
            if (isset($result['success']) && $result['success']) {
                $this->dispatchReservationStatusEvent($result, 'CREATE', $request);
            }

            // 成功返回
            if (Arr::get($result, 'confirmation_number') && Arr::get($result, 'success')) {
                $result['StatusCn'] = getReservationStatus(Arr::get($result, 'status'));
                // 写入本地数据
                ResvBase::createResv($data, $result);

                return successResponse($result, '预订订单成功');
            }

            return errorResponse('预订失败');
        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试'.$e->getMessage(), 500);
        }
    }

    /**
     * 查询预订
     */
    public function getReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'itineraryNumber' => 'nullable|string',
            'confirmationNumber' => 'string',
            'hotelId' => 'required_with:confirmationNumber|integer',
            'chainId' => 'integer',
            'channel' => 'string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }
        if (! $request->has('itineraryNumber') && ! $request->has('confirmationNumber')) {
            return errorResponse('必须提供行程号或确认号', 400);
        }

        try {
            $params = $request->all();
            $format = $request->input('format', 'availability'); // 默认使用标准格式
            $params['format'] = $format;
            $result = $this->reservationService->getReservation($params);
            if ($result) {
                $result = collect($result)->map(function ($item) {
                    $item['hotel'] = Hotel::getCode(Arr::get($item, 'hotel.hotelCode'));
                    // 查询状态
                    $item['reservationStatusCn'] = getReservationStatus(Arr::get($item, 'reservationStatus'));

                    return $item;
                })->values();

                return successResponse($result, '查询订单成功');
            }

            return errorResponse('查询订单失败');

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 取消预订
     */
    public function cancelReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'confirmationNumber' => 'array|nullable',
            'confirmationNumber.*' => 'string',
            'itineraryNumber' => 'string|nullable',
            'hotelId' => 'required|integer',
            'hotelCode' => 'string',
            'channel' => 'string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        // 检查必须提供确认号或行程号中的一个
        $confirmationNumbers = $request->input('confirmationNumber');
        $itineraryNumber = $request->input('itineraryNumber');

        if (empty($confirmationNumbers) && empty($itineraryNumber)) {
            return errorResponse('必须提供确认号或行程号', 400);
        }

        if (! empty($confirmationNumbers) && ! empty($itineraryNumber)) {
            return errorResponse('不能同时提供确认号和行程号', 400);
        }

        try {
            // 优先使用确认号，支持批量取消
            if (! empty($confirmationNumbers)) {
                $result = $this->reservationService->cancelMultipleReservations(
                    $confirmationNumbers,
                    $request->input('hotelId'),
                    $request->input('hotelCode')
                );
            } else {
                // 使用行程号取消逻辑
                $result = $this->reservationService->cancelReservationByItinerary(
                    $itineraryNumber,
                    $request->input('hotelId'),
                    $request->input('hotelCode'),
                    $request->input('channel', 'DSCVRYLYLTY')
                );
            }

            // 订单取消成功后触发事件（只有用户登录时）
            if (isset($result['success']) && $result['success'] && AuthCheck()) {
                $this->dispatchCancelReservationEvent($result, $request, $itineraryNumber, $confirmationNumbers);
            }

            // 返回经过数据转换器处理的标准格式
            if (Arr::get($result, 'success') !== false) {
                return successResponse($result, '取消订单成功');
            }

            return errorResponse('取消订单失败');
        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 修改预订
     */
    public function modifyReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'CRS_confirmationNumber' => 'required|string',
            // 基本预订信息
            'hotelId' => 'required|integer',
            'checkInDate' => 'required|date|after:today',
            'checkOutDate' => 'required|date|after:checkInDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAges' => 'array',
            'childrenAges.*' => 'integer|min:0|max:17',

            // 房型和价格信息
            'roomCode' => 'required|string',
            'rateCode' => 'required|string',

            // 主要客人信息（简化）
            'primaryGuest' => 'required|array',
            'primaryGuest.firstName' => 'required|string|max:50',
            'primaryGuest.lastName' => 'required|string|max:50',
            'primaryGuest.email' => 'required|email',
            'primaryGuest.phone' => 'required|string',
            'primaryGuest.address' => 'array',
            'primaryGuest.address.line1' => 'string|max:100',
            'primaryGuest.address.city' => 'string|max:50',
            'primaryGuest.address.state' => 'string|max:50',
            'primaryGuest.address.country' => 'string|size:2',
            'primaryGuest.address.postalCode' => 'string|max:20',

            // 支付信息（简化）
            'payment' => 'required|array',
            //            'payment.cardType' => 'required|string',
            'payment.cardNumber' => 'required|string',
            'payment.cardHolder' => 'required|string|max:50',
            'payment.expiryMonth' => 'required',
            'payment.expiryYear' => 'required',

            // 可选参数
            //            'loyaltyNumber' => 'string',
            //            'promoCode' => 'nullable|string',
            //            'specialRequests' => 'nullable|string|max:500',
            //            'sendConfirmationEmail' => 'boolean',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }
        $loyaltyNumber = '';
        $loyaltyLevel = '';
        if (AuthCheck()) {
            $loyaltyNumber = AuthUser()->membership_card_no ?? '';
            $loyaltyLevel = AuthUser()->membership_level ?? '';
        }
        $request->merge([
            'sendConfirmationEmail' => false,
            'loyaltyNumber' => $loyaltyNumber,
            'loyaltyLevel' => $loyaltyLevel,
        ]);
        $data = $request->all();
        $data['payment']['cardType'] = getCardType($data['payment']['cardNumber']);
        try {
            // 创建简化预订请求DTO
            $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($data);
            // 使用转换服务将简化参数转换为Sabre格式
            $hasLoyalty = $simplifiedRequest->hasLoyalty();
            $hasPromo = $simplifiedRequest->hasPromo();
            $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                $simplifiedRequest,
                $hasLoyalty,
                $hasPromo
            );
            // 添加修改预订特有的确认号
            $sabreParams['CRS_confirmationNumber'] = $request->input('CRS_confirmationNumber');
            $result = $this->reservationService->modifyReservation($sabreParams);

            // 订单修改成功后触发事件（只有用户登录时）
            if (isset($result['success']) && $result['success'] && AuthCheck()) {
                $this->dispatchReservationStatusEvent($result, 'MODIFY', $request);
            }

            // 成功返回
            if (Arr::get($result, 'confirmation_number') && Arr::get($result, 'success')) {
                return successResponse($result, '预订成功');
            }

            return errorResponse('预订失败');
        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 触发订单状态变更事件
     */
    protected function dispatchReservationStatusEvent(array $reservationData, string $status, Request $request): void
    {
        try {
            // 从返回的数据中提取必要信息 - 兼容多种字段名格式
            $itineraryNumber = $reservationData['itinerary_number'] ?? $reservationData['itineraryNumber'] ?? null;
            $chainId = $reservationData['chainId'] ?? $request->input('chainId', 'GHA');
            $hotelId = $request->input('hotelId');

            // 构建预订详情 - 兼容多种字段名格式
            $reservationDetails = [];
            $confirmationNumber = $reservationData['confirmation_number'] ?? $reservationData['confirmationNumber'] ?? null;

            if (isset($reservationData['confirmationNumbers']) && is_array($reservationData['confirmationNumbers'])) {
                foreach ($reservationData['confirmationNumbers'] as $confirmNum) {
                    $reservationDetails[] = [
                        'confirmation_number' => $confirmNum,
                        'member_rate_yn' => $request->has('loyaltyNumber') && ! empty($request->input('loyaltyNumber')),
                    ];
                }
            } elseif ($confirmationNumber) {
                $reservationDetails[] = [
                    'confirmation_number' => $confirmationNumber,
                    'member_rate_yn' => $request->has('loyaltyNumber') && ! empty($request->input('loyaltyNumber')),
                ];
            }

            // 只有当用户登录时才触发状态上报事件
            if ($itineraryNumber && $hotelId && ! empty($reservationDetails) && AuthCheck()) {
                $profileId = AuthUser()->id ?? null;
                ReservationStatusChangedEvent::dispatch(
                    $itineraryNumber,
                    $status,
                    (string) $chainId,
                    (string) $hotelId,
                    $reservationDetails,
                    $profileId
                );

                Log::info('订单状态事件已触发', [
                    'itinerary_number' => $itineraryNumber,
                    'status' => $status,
                    'hotel_id' => $hotelId,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('触发订单状态事件失败', [
                'error' => $e->getMessage(),
                'status' => $status,
                'data' => $reservationData,
            ]);
        }
    }

    /**
     * 触发取消订单事件
     */
    protected function dispatchCancelReservationEvent(array $result, Request $request, ?string $itineraryNumber, ?array $confirmationNumbers): void
    {
        try {
            $hotelId = $request->input('hotelId');
            $chainId = $request->input('chainId', 'GHA');
            $profileId = AuthUser()->id ?? null;

            // 如果有行程号，直接使用
            if ($itineraryNumber) {
                $reservationDetails = [];
                if (isset($result['data']['cancelledReservations']) && is_array($result['data']['cancelledReservations'])) {
                    foreach ($result['data']['cancelledReservations'] as $cancelled) {
                        $reservationDetails[] = [
                            'confirmation_number' => $cancelled['confirmationNumber'] ?? '',
                            'member_rate_yn' => false, // 取消时默认为false
                        ];
                    }
                }

                if (! empty($reservationDetails)) {
                    ReservationStatusChangedEvent::dispatch(
                        $itineraryNumber,
                        'CANCEL',
                        (string) $chainId,
                        (string) $hotelId,
                        $reservationDetails,
                        $profileId
                    );
                }
            } // 如果有确认号数组，需要为每个确认号触发事件
            elseif ($confirmationNumbers && is_array($confirmationNumbers)) {
                foreach ($confirmationNumbers as $confirmationNumber) {
                    $reservationDetails = [
                        [
                            'confirmation_number' => $confirmationNumber,
                            'member_rate_yn' => false,
                        ],
                    ];

                    // 对于确认号取消，可能没有行程号，使用确认号作为标识
                    ReservationStatusChangedEvent::dispatch(
                        $confirmationNumber, // 临时使用确认号作为行程号
                        'CANCEL',
                        (string) $chainId,
                        (string) $hotelId,
                        $reservationDetails,
                        $profileId
                    );
                }
            }

            Log::info('取消订单事件已触发', [
                'itinerary_number' => $itineraryNumber,
                'confirmation_numbers' => $confirmationNumbers,
                'hotel_id' => $hotelId,
            ]);
        } catch (\Exception $e) {
            Log::error('触发取消订单事件失败', [
                'error' => $e->getMessage(),
                'itinerary_number' => $itineraryNumber,
                'confirmation_numbers' => $confirmationNumbers,
            ]);
        }
    }

    /**
     * 获取个人订阅
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function reservations(Request $request)
    {
        try {
            $this->validate($request->all(), [
                'page' => 'sometimes|integer|min:0',
                'size' => 'sometimes|integer|min:1|max:2000',
                //                'status' => 'sometimes|array',
                'status.*' => 'string|in:CURRENT,UPCOMING,PAST,CANCELED',
                'sort_by' => 'sometimes|string|in:ArrivalDate',
                'sort_direction' => 'sometimes|string|in:asc,desc',
                //                'profile_id' => 'sometimes|string', // PARTNER类型用户需要
            ]);
            // 验证用户是否已登录到我们的系统
            $user = AuthUser();
            if (! $user) {
                return errorResponse('未授权访问', 401);
            }
            $filters = $request->only([
                'page', 'size', 'status', 'sort_by', 'sort_direction',
            ]);
            $filters['profile_id'] = $user->profile_id;
            $filters['membership_card_no'] = $user->membership_card_no;
            $result = ResvBaseController::getBase($filters);

            // 直接调用服务，凭据从配置文件自动读取
            //            $result = $this->gxpService->getUserBookings($filters);

            return successResponse($result, '获取预订列表成功');

        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    // 获取一组酒店的价格（member 或 non_member）
    public static function getPrices(array $hotelIds, string $type = 'non_member'): array
    {
        $key = $type === 'member' ? 'hotel_price_member' : 'hotel_price_non_member';
        $values = self::hmgetArray($key, $hotelIds);
        // 转成 [hotelId => price]
        $result = [];
        foreach ($hotelIds as $i => $id) {
            $val = $values[$i] ?? null;
            $result[$id] = $val !== null && $val !== false && $val !== '' ? (float) $val : null;
        }

        return $result;
    }

    /**
     * HMGET 封装，兼容 Predis 和 PhpRedis
     */
    public static function hmgetArray(string $key, array $fields): array
    {
        if (empty($fields)) {
            return [];
        }
        $driver = config('database.redis.client');
        if ($driver === 'phpredis') {
            return Redis::command('HMGET', array_merge([$key], $fields));
        }

        return Redis::hmget($key, $fields);
    }

    public static function bulkUpdate(array $data)
    {
        // HMGET 按顺序返回值（与 $hotelIds 顺序对应）
        Redis::pipeline(function ($pipe) use ($data) {
            foreach ($data as $hotelId => $prices) {
                if (isset($prices['member'])) {
                    $pipe->hSet('hotel_price_member', $hotelId, $prices['member']);
                    // phpredis: zAdd(key, score, member)
                    $pipe->zAdd('hotel_price_member_z', (float) $prices['member'], (string) $hotelId);
                }
                if (isset($prices['non_member'])) {
                    $pipe->hSet('hotel_price_non_member', $hotelId, $prices['non_member']);
                    $pipe->zAdd('hotel_price_non_member_z', (float) $prices['non_member'], (string) $hotelId);
                }
            }
        });
    }

    // 单条更新（也用 pipeline）
    public static function updateOne($hotelId, $data)
    {
        $memberPrice = null;
        $nonMemberPrice = null;
        if (isset($data['priceInfo']['nonMemberPrice'])) {
            $nonMemberPrice = $data['priceInfo']['nonMemberPrice']['cnyTotalPrice'];
        }
        if (isset($data['priceInfo']['memberPrice'])) {
            $memberPrice = $data['priceInfo']['memberPrice']['cnyTotalPrice'];
        }

        Redis::pipeline(function ($pipe) use ($hotelId, $memberPrice, $nonMemberPrice) {
            if ($memberPrice !== null) {
                $pipe->hSet('hotel_price_member', $hotelId, $memberPrice);
                $pipe->zAdd('hotel_price_member_z', (float) $memberPrice, (string) $hotelId);
            }
            if ($nonMemberPrice !== null) {
                $pipe->hSet('hotel_price_non_member', $hotelId, $nonMemberPrice);
                $pipe->zAdd('hotel_price_non_member_z', (float) $nonMemberPrice, (string) $hotelId);
            }
        });
    }

    // （可选）仅按价格全局分页：直接查询 ZSET
    // $order: 'asc' or 'desc'
    public static function getTopByPrice(int $offset, int $limit, string $type = 'non_member', string $order = 'asc'): array
    {
        $zkey = $type === 'member' ? 'hotel_price_member_z' : 'hotel_price_non_member_z';
        if ($order === 'asc') {
            $ids = Redis::zRange($zkey, $offset, $offset + $limit - 1);
        } else {
            $ids = Redis::zRevRange($zkey, $offset, $offset + $limit - 1);
        }

        return $ids; // 仅 id 列表，若需要 scores 可用 withscores 选项（client dependent）
    }

    /**
     * 批量创建预订
     */
    public function createBatchReservations(Request $request): JsonResponse
    {
        $validator = Validator::make(['data' => $request->all()], [
            'data' => 'required|array|min:1|max:10',
            'data.*' => 'array',

            // 每个订单的基本预订信息
            'data.*.hotelId' => 'required|integer',
            'data.*.checkInDate' => 'required|date|after:today',
            'data.*.checkOutDate' => 'required|date|after:data.*.checkInDate',
            'data.*.numRooms' => 'integer|min:1|max:10',
            'data.*.adults' => 'integer|min:1|max:20',
            'data.*.children' => 'integer|min:0|max:10',
            'data.*.childrenAges' => 'array',
            'data.*.childrenAges.*' => 'integer|min:0|max:17',

            // 房型和价格信息
            'data.*.roomCode' => 'required|string',
            'data.*.rateCode' => 'required|string',

            // 主要客人信息
            'data.*.primaryGuest' => 'required|array',
            'data.*.primaryGuest.firstName' => 'required|string|max:50',
            'data.*.primaryGuest.lastName' => 'required|string|max:50',
            'data.*.primaryGuest.email' => 'required|email',
            'data.*.primaryGuest.phone' => 'required|string',
            'data.*.primaryGuest.address' => 'array',
            'data.*.primaryGuest.address.line1' => 'string|max:100',
            'data.*.primaryGuest.address.city' => 'string|max:50',
            'data.*.primaryGuest.address.state' => 'string|max:50',
            'data.*.primaryGuest.address.country' => 'string|size:2',
            'data.*.primaryGuest.address.postalCode' => 'string|max:20',

            // 支付信息
            'data.*.payment' => 'required|array',
            'data.*.payment.cardNumber' => 'required|string',
            'data.*.payment.cardHolder' => 'required|string|max:50',
            'data.*.payment.expiryMonth' => 'required|integer|min:1|max:12',
            'data.*.payment.expiryYear' => 'required|integer|min:'.date('Y'),
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        try {
            $reservations = $request->all();
            $results = [];
            $itineraryNumber = null;
            $successCount = 0;
            $failedCount = 0;
            $createdReservations = []; // 用于回滚

            // 获取用户会员信息
            $loyaltyNumber = '';
            $loyaltyLevel = '';
            if (AuthCheck()) {
                $loyaltyNumber = AuthUser()->membership_card_no ?? '';
                $loyaltyLevel = AuthUser()->membership_level ?? '';
            }

            // 遍历每个订单进行下单
            foreach ($reservations as $index => $reservationData) {
                try {
                    // 如果不是第一个订单，添加行程号
                    if ($itineraryNumber) {
                        $reservationData['itineraryNumber'] = $itineraryNumber;
                    }

                    // 添加会员信息和必要字段
                    $reservationData['sendConfirmationEmail'] = false;
                    $reservationData['loyaltyNumber'] = $loyaltyNumber;
                    $reservationData['loyaltyLevel'] = $loyaltyLevel;
                    $reservationData['payment']['cardType'] = getCardType($reservationData['payment']['cardNumber']);

                    // 创建简化预订请求DTO
                    $simplifiedRequest = SimplifiedReservationRequestDTO::fromArray($reservationData);

                    // 使用转换服务将简化参数转换为Sabre格式
                    $hasLoyalty = $simplifiedRequest->hasLoyalty();
                    $hasPromo = $simplifiedRequest->hasPromo();

                    $sabreParams = $this->reservationTransformer->transformToSabreFormat(
                        $simplifiedRequest,
                        $hasLoyalty,
                        $hasPromo
                    );

                    // 根据参数选择不同的创建方法
                    if ($hasLoyalty && $hasPromo) {
                        $result = $this->reservationService->createSingleRoomBookingWithMembershipAndPromo($sabreParams);
                    } elseif ($hasLoyalty) {
                        $result = $this->reservationService->createSingleRoomBookingWithMembership($sabreParams);
                    } elseif ($hasPromo) {
                        $result = $this->reservationService->createSingleRoomBookingNonMemberWithPromo($sabreParams);
                    } else {
                        $result = $this->reservationService->createSingleRoomBookingNonMember($sabreParams);
                    }

                    // 记录结果 - 简化版本
                    $results[] = [
                        'index' => $index,
                        'success' => $result['success'] ?? false,
                        'reservation_id' => $result['reservation_id'] ?? null,
                        'confirmation_number' => $result['confirmation_number'] ?? null,
                        'itinerary_number' => $result['itinerary_number'] ?? null,
                        'status' => $result['status'] ?? null,
                    ];

                    if ($result['success'] ?? false) {
                        $successCount++;
                        $createdReservations[] = $result;

                        // 从第一个成功的订单获取行程号
                        if ($itineraryNumber === null) {
                            $itineraryNumber = $result['itinerary_number'] ?? null;
                            Log::info('获取到行程号：'.$itineraryNumber);
                        }

                        // 订单创建成功后触发事件
                        $this->dispatchReservationStatusEvent($result, 'CREATE',
                            new Request($reservationData));

                        // RoomController特有的处理：写入本地数据
                        if (Arr::get($result, 'confirmation_number') && Arr::get($result, 'success')) {
                            $result['StatusCn'] = getReservationStatus(Arr::get($result, 'status'));
                            ResvBase::createResv($reservationData, $result);
                        }
                    } else {
                        $failedCount++;
                        Log::error('批量下单中第'.($index + 1).'个订单失败', $result);
                    }

                } catch (SabreApiException $e) {
                    $failedCount++;
                    $errorResult = [
                        'index' => $index,
                        'success' => false,
                        'error' => $e->getUserMessage(),
                        'error_code' => $e->getCode(),
                        'reservation_id' => null,
                        'confirmation_number' => null,
                        'itinerary_number' => null,
                        'status' => null,
                    ];
                    $results[] = $errorResult;
                    Log::error('批量下单中第'.($index + 1).'个订单异常', [
                        'error' => $e->getMessage(),
                        'reservation_data' => $reservationData,
                    ]);
                } catch (\Exception $e) {
                    $failedCount++;
                    $errorResult = [
                        'index' => $index,
                        'success' => false,
                        'error' => '系统错误，请稍后再试',
                        'error_code' => 500,
                        'reservation_id' => null,
                        'confirmation_number' => null,
                        'itinerary_number' => null,
                        'status' => null,
                    ];
                    $results[] = $errorResult;
                    Log::error('批量下单中第'.($index + 1).'个订单系统异常', [
                        'error' => $e->getMessage(),
                        'reservation_data' => $reservationData,
                    ]);
                }
            }

            // 构建清晰的响应数据
            $responseData = [
                'itinerary_number' => $itineraryNumber,
                'total_reservations' => count($reservations),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'reservations' => $results,
            ];

            $message = $successCount > 0 ?
                ($failedCount > 0 ? "部分下单成功：{$successCount}个成功，{$failedCount}个失败" : '全部下单成功') :
                '全部下单失败';

            Log::info('批量下单完成', [
                'itinerary_number' => $itineraryNumber,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
            ]);

            return successResponse($responseData, $message);

        } catch (\Exception $e) {
            Log::error('批量下单系统错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return errorResponse('系统错误，请稍后再试', 500);
        }
    }
}
