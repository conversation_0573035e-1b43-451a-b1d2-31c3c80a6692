<?php

namespace App\Http\Controllers;

use App\Models\Continent;
use App\Models\Hotel;
use Illuminate\Http\Request;

class ContinentController extends BaseController
{
    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|object
     *
     * @throws \Exception
     */
    public function destination(Request $request)
    {
        // 探索新酒店
        $hotel = Hotel::where('new_hotel', 1)->orderBy('created_at', 'desc')
            ->select(['id', 'code','country_code','city_code', 'hotel_name','categories', 'address', 'hotel_desc', 'remark', 'website_url', 'images', 'brand_code', 'synxis_hotel_id','url'])
            ->limit(10)->get();
        $arr['hotels'] = $hotel;
        // 目的地
        $arr['continent'] = Continent::getContinentList();

        return view('destination', $arr);
    }
}
