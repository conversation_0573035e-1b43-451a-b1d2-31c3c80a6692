<?php

namespace App\Services;

use App\DTOs\SimplifiedReservationRequestDTO;
use Carbon\Carbon;

/**
 * Sabre预订参数转换服务
 * 负责将简化的预订请求转换为完整的Sabre API格式
 */
class SabreReservationTransformer
{
    /**
     * 将简化的预订请求转换为Sabre格式
     */
    public function transformToSabreFormat(
        SimplifiedReservationRequestDTO $request,
        bool $withMembership = false,
        bool $withPromo = false
    ): array {
        $checkInDate = Carbon::parse($request->checkInDate)->format('Y-m-d\\TH:i:s');
        $checkOutDate = Carbon::parse($request->checkOutDate)->format('Y-m-d\\TH:i:s');

        // 构建基本参数结构
        $sabreParams = [
            'ItineraryNumber' => $request->itineraryNumber,
            'hotelId' => $request->hotelId,
            'chainId' => config('sabre.defaults.chain_id'),
            'guests' => $this->buildGuestData($request, $checkInDate, $checkOutDate),
            'roomStay' => $this->buildRoomStayData($request, $checkInDate, $checkOutDate),
            'sendBookerEmail' => $request->sendConfirmationEmail,
            'sendGuestEmail' => $request->sendConfirmationEmail,
            'status' => 'Confirmed',
            'MarketSource' => [
                'Code' => 'GHA',
            ],
        ];

        // 添加忠诚度会员信息
        if ($withMembership && $request->hasLoyalty()) {
            $sabreParams['loyaltyMemberships'] = $this->buildLoyaltyMembershipData(
                $request
            );
        }

        // 添加促销信息
        if ($withPromo && $request->hasPromo()) {
            $sabreParams['promotion'] = $this->buildPromotionData($request);
        }

        return $sabreParams;
    }

    /**
     * 构建客人信息数据
     */
    private function buildGuestData(
        SimplifiedReservationRequestDTO $request,
        string $checkInDate,
        string $checkOutDate
    ): array {
        $guest = $request->primaryGuest;
        $address = $guest->address;

        return [
            [
                'PersonName' => [
                    'GivenName' => $guest->firstName,
                    'Surname' => $guest->lastName,
                ],
                'EmailAddress' => [
                    [
                        'Type' => 'Primary',
                        'Value' => $guest->email,
                    ],
                ],
                'ContactNumbers' => [
                    [
                        'Number' => $guest->phone,
                        'Role' => 'Home',
                        'Type' => 'Mobile',
                        'Use' => 'DayTimeContact',
                        'Default' => true,
                    ],
                ],
                'Locations' => [
                    [
                        'Address' => [
                            'AddressLine' => [$address?->line1 ?? ''],
                            'City' => $address?->city ?? '',
                            'Country' => [
                                'Code' => $address?->country ?? 'CN',
                                'Value' => $address?->getCountryName() ?? '中国',
                            ],
                            'PostalCode' => $address?->postalCode ?? '',
                            'StateProv' => [
                                'Code' => $address?->state ?? '',
                                'Value' => $address?->state ?? '',
                            ],
                            'Type' => 'Home',
                            'Default' => true,
                        ],
                        'Name' => 'Home',
                    ],
                ],
                'Payments' => [
                    [
                        'Amount' => 0,
                        'PaymentCard' => [
                            'CardCode' => $request->payment->cardType,
                            'CardHolder' => $request->payment->cardHolder,
                            'CardNumber' => $request->payment->cardNumber,
                            'ExpireDate' => $request->payment->getSabreExpireDate(),
                        ],
                        'Role' => 'Primary',
                        'Type' => 'CreditCard',
                    ],
                ],
            ],
        ];
    }

    /**
     * 构建房间住宿信息
     */
    private function buildRoomStayData(
        SimplifiedReservationRequestDTO $request,
        string $checkInDate,
        string $checkOutDate
    ): array {
        // 构建客人数量信息
        $guestCount = [
            [
                'AgeQualifyingCode' => 'Adult',
                'NumGuests' => $request->adults,
            ],
        ];

        if ($request->hasChildren()) {
            $guestCount[] = [
                'AgeQualifyingCode' => 'Child',
                'NumGuests' => $request->children,
                'Ages' => $request->childrenAges,
            ];
        }

        return [
            'StartDate' => $checkInDate,
            'EndDate' => $checkOutDate,
            'NumRooms' => $request->numRooms,
            'GuestCount' => $guestCount,
            'Products' => [
                [
                    'StartDate' => $checkInDate,
                    'EndDate' => $checkOutDate,
                    'Primary' => true,
                    'Product' => [
                        'RateCode' => $request->rateCode,
                        'RoomCode' => $request->roomCode,
                    ],
                ],
            ],
        ];
    }

    /**
     * 构建忠诚度会员信息
     */
    private function buildLoyaltyMembershipData(
        SimplifiedReservationRequestDTO $request
    ): array {
        return [
            [
                'Level' => [
                    'Code' => $request->loyaltyLevel ?? 'SILVER', // 默认级别
                ],
                'MembershipID' => $request->loyaltyNumber,
                'Source' => 'Selected',
                'enrollmentCode' => config('sabre.defaults.loyalty_program', 'GHA'),
                'ProgramID' => config('sabre.defaults.loyalty_program', 'GHA'),
            ],
        ];
    }

    /**
     * 构建促销信息
     */
    private function buildPromotionData(SimplifiedReservationRequestDTO $request): array
    {
        return [
            'AccessKey' => [
                'Code' => $request->promoCode,
            ],
            'Type' => 'Promotion',
        ];
    }
}
