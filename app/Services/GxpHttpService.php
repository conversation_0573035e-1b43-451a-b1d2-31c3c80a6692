<?php

namespace App\Services;

use App\Constants\GxpAppKeyType;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class GxpHttpService
{
    protected $client;

    protected $baseUrl;

    protected $appKeyChina;

    protected $appKeyRotana;

    protected $appKeyReservation;

    public function __construct()
    {
        $this->baseUrl = config('gha.api_v1'); // https://gxp.stage.ghaloyalty.com/api/v1/
        $this->appKeyChina = config('gha.app_key_china', '');
        $this->appKeyRotana = config('gha.app_key_rotana', '');
        $this->appKeyReservation = config('gha.app_key_reservation', '');
        $this->client = new Client([
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'verify' => config('gha.gxp.ssl_verify', false),
            'timeout' => config('gha.gxp.timeout', 30),
            'connect_timeout' => config('gha.gxp.connect_timeout', 10),
        ]);
    }

    /**
     * GET请求
     *
     * @param  string  $url
     * @param  array  $params
     * @param  string|null  $token
     * @param  string  $appKeyType  GxpAppKeyType::CHINA 或 GxpAppKeyType::ROTANA
     * @return array
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function get($url, $params = [], $token = null, $appKeyType = GxpAppKeyType::CHINA)
    {
        try {
            $headers = [
                'app-key' => $this->getAppKey($appKeyType),
            ];

            if ($token) {
                $headers['apiauthorization'] = 'Bearer '.$token;
            }

            $query_string = http_build_query($params);
            $full_url = $this->baseUrl.$url.($query_string ? '?'.$query_string : '');
            $response = $this->client->request('GET', $full_url, [
                'headers' => $headers,
            ]);
            $resp = (string) $response->getBody();
            Log::info($url.':responseData', [
                'data' => $resp,
            ]);

            if ($response->getStatusCode() == 200) {
                Log::info($url.':success', ['message' => 'GET request successful']);
                $responseData = json_decode($resp, true);

                return ['data' => $responseData, 'code' => 200];
            }
        } catch (\Exception $exception) {
            $resp = '';
            if (method_exists($exception, 'getResponse') && $exception->getResponse()) {
                $resp = (string) $exception->getResponse()->getBody();
            }
            Log::error($url.':error', [
                'msg' => $exception->getMessage(),
                'body' => $resp,
            ]);

            return ['message' => $exception->getMessage(), 'code' => 500];
        }
    }

    /**
     * POST请求
     *
     * @param  string  $url
     * @param  array  $params
     * @param  string|null  $token
     * @param  string  $appKeyType  GxpAppKeyType::CHINA 或 GxpAppKeyType::ROTANA
     * @return array
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function post($url, $params, $token = null, string $appKeyType = GxpAppKeyType::CHINA)
    {
        try {
            $headers = [
                'app-key' => $this->getAppKey($appKeyType),
            ];
            if ($token) {
                $headers['apiauthorization'] = 'Bearer '.$token;
            }
            Log::info('request'.$url, [
                'param' => $params,
                'header' => $headers,
            ]);
            $response = $this->client->request('POST', $this->baseUrl.$url, [
                'headers' => $headers,
                'json' => $params,
            ]);

            $resp = (string) $response->getBody();
            Log::info($url.':responseData', [
                'data' => $resp,
            ]);

            if ($response->getStatusCode() == 200 || $response->getStatusCode() == 201) {
                Log::info($url.':success', ['message' => 'POST request successful']);
                $responseData = json_decode($resp, true);

                return ['data' => $responseData, 'code' => 200];
            }
        } catch (\Exception $exception) {
            $resp = '';
            if (method_exists($exception, 'getResponse') && $exception->getResponse()) {
                $resp = (string) $exception->getResponse()->getBody();
            }
            Log::error($url.':error', [
                'msg' => $exception->getMessage(),
                'body' => $resp,
            ]);

            return ['message' => $exception->getMessage(), 'code' => 500];
        }
    }

    /**
     * 根据类型获取对应的app-key
     *
     * @throws \Exception
     */
    protected function getAppKey(string $type): string
    {
        if (! GxpAppKeyType::isValidType($type)) {
            throw new \Exception("不支持的app-key类型: {$type}");
        }

        switch ($type) {
            case GxpAppKeyType::CHINA:
                return $this->appKeyChina;
            case GxpAppKeyType::ROTANA:
                return $this->appKeyRotana;
            case GxpAppKeyType::RESERVATION:
                return $this->appKeyReservation;
            default:
                throw new \Exception("不支持的app-key类型: {$type}");
        }
    }
}
