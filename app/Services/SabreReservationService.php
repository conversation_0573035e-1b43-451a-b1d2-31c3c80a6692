<?php

namespace App\Services;

use App\DTOs\Sabre\BookingInfoDTO;
use App\DTOs\Sabre\ChainDTO;
use App\DTOs\Sabre\ChannelDTO;
use App\DTOs\Sabre\ChannelsDTO;
use App\DTOs\Sabre\GuestDTO;
use App\DTOs\Sabre\HotelDTO;
use App\DTOs\Sabre\LanguageDTO;
use App\DTOs\Sabre\LoyaltyMembershipDTO;
use App\DTOs\Sabre\MarketSourceDTO;
use App\DTOs\Sabre\NotificationDTO;
use App\DTOs\Sabre\OriginalSourceDTO;
use App\DTOs\Sabre\PromotionDTO;
use App\DTOs\Sabre\ReservationRequestDTO;
use App\DTOs\Sabre\RoomStayDTO;
use App\Exceptions\SabreApiException;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SabreReservationService
{
    protected SabreService $sabreService;

    protected SabreReservationDataTransformer $dataTransformer;

    public function __construct(
        SabreService $sabreService,
        SabreReservationDataTransformer $dataTransformer
    ) {
        $this->sabreService = $sabreService;
        $this->dataTransformer = $dataTransformer;
    }

    /**
     * 创建单房间预订（非会员）
     */
    public function createSingleRoomBookingNonMember(array $params): array
    {
        $request = $this->buildReservationRequest($params, false, false);
        try {
            $result = $this->sabreService->createReservation($request);

            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Create Single Room Booking Non-Member Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 创建单房间预订（非会员，含促销）
     */
    public function createSingleRoomBookingNonMemberWithPromo(array $params): array
    {
        $request = $this->buildReservationRequest($params, false, true);

        try {
            $result = $this->sabreService->createReservation($request);

            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Create Single Room Booking Non-Member With Promo Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 创建单房间预订（会员）
     */
    public function createSingleRoomBookingWithMembership(array $params): array
    {
        $request = $this->buildReservationRequest($params, true, false);

        try {
            $result = $this->sabreService->createReservation($request);

            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Create Single Room Booking With Membership Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 创建单房间预订（会员，含促销）
     */
    public function createSingleRoomBookingWithMembershipAndPromo(array $params): array
    {
        $request = $this->buildReservationRequest($params, true, true);

        try {
            $result = $this->sabreService->createReservation($request);

            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Create Single Room Booking With Membership And Promo Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 创建多房间预订
     */
    public function createMultiRoomBooking(array $params): array
    {
        // 多房间预订需要先创建第一个房间，然后使用返回的行程号创建后续房间
        $firstRoomParams = $params;
        $firstRoomParams['numRooms'] = 1;

        // 创建第一个房间
        $firstRoomResult = $this->createSingleRoomBookingWithMembership($firstRoomParams);

        if (! $firstRoomResult['success'] || empty($firstRoomResult['itinerary_number'])) {
            throw new SabreApiException('创建第一个房间失败');
        }

        $itineraryNumber = $firstRoomResult['itinerary_number'];
        $results = [$firstRoomResult];

        // 创建剩余房间
        $remainingRooms = ($params['numRooms'] ?? 2) - 1;
        for ($i = 0; $i < $remainingRooms; $i++) {
            $additionalRoomParams = $params;
            $additionalRoomParams['itineraryNumber'] = $itineraryNumber;
            $additionalRoomParams['numRooms'] = 1;

            try {
                $request = $this->buildReservationRequest($additionalRoomParams, true, isset($params['accessCode']));
                $result = $this->sabreService->createReservation($request);
                $results[] = $this->parseReservationResponse($result);
            } catch (SabreApiException $e) {
                Log::error('Create Additional Room Error', [
                    'message' => $e->getMessage(),
                    'room_index' => $i + 2,
                    'itinerary_number' => $itineraryNumber,
                ]);
                // 继续创建其他房间，但记录错误
                $results[] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'room_index' => $i + 2,
                ];
            }
        }

        return [
            'success' => true,
            'itinerary_number' => $itineraryNumber,
            'rooms' => $results,
            'total_rooms' => count($results),
        ];
    }

    /**
     * 构建预订请求DTO
     */
    protected function buildReservationRequest(array $params, bool $withMembership = false, bool $withPromo = false, bool $isModification = false): ReservationRequestDTO
    {
        $now = Carbon::now('UTC');

        // 构建预订信息 (修改预订时不需要)
        $bookingInfo = $isModification ? null : new BookingInfoDTO(
            bookingDate: $now->format('Y-m-d\\TH:i:s'),
            entryChannelBookingDate: $now->format('Y-m-d\\TH:i:s'),
            entryChannelCode: config('sabre.defaults.entry_channel_code'),
            originalSource: new OriginalSourceDTO(
                primaryChannel: new ChannelDTO(
                    code: config('sabre.defaults.primary_channel'),
                    description: 'Channel Connect'
                ),
                secondaryChannel: new ChannelDTO(
                    code: config('sabre.defaults.secondary_channel'),
                    description: 'DISCOVERY Loyalty'
                ),
                subSourceCode: config('sabre.defaults.sub_source_code', 'GHA')
            )
        );

        // 构建酒店和链信息
        $chain = new ChainDTO($params['chainId'] ?? config('sabre.defaults.chain_id'));
        $hotel = new HotelDTO($params['hotelId'], $params['hotelCode'] ?? null);

        // 构建渠道信息
        $channels = new ChannelsDTO(
            primaryChannel: new ChannelDTO(config('sabre.defaults.primary_channel')),
            secondaryChannel: new ChannelDTO(config('sabre.defaults.secondary_channel')),
            subSourceCode: config('sabre.defaults.sub_source_code')
        );
        $startDate = Carbon::parse($params['roomStay']['StartDate'], 'UTC')->format('Y-m-d\\TH:i:s');
        $endDate = Carbon::parse($params['roomStay']['EndDate'], 'UTC')->format('Y-m-d\\TH:i:s');
        // 构建客人信息（标准化 Guests 的起止时间为 UTC ISO）
        $guests = [];
        foreach ($params['guests'] as $guestData) {
            $guestData['StartDate'] = $startDate;
            $guestData['EndDate'] = $endDate;
            $guests[] = GuestDTO::fromArray($guestData);
        }

        // 构建语言信息 (修改预订时不需要)
        $language = $isModification ? null : new LanguageDTO(
            code: $params['language']['code'] ?? config('sabre.defaults.language', 'zh-CN'),
            name: $params['language']['name'] ?? '中文'
        );

        // 构建市场来源 (修改预订时不需要)
        $marketSource = $isModification ? null : new MarketSourceDTO(config('sabre.defaults.market_source_code'));

        // 构建通知设置 (修改预订时不需要)
        $notification = $isModification ? null : new NotificationDTO(
            sendBookerEmail: $params['sendBookerEmail'] ?? false,
            sendGuestEmail: $params['sendGuestEmail'] ?? false
        );

        // 构建住宿信息（统一为 UTC ISO）
        $roomStayInput = $params['roomStay'];
        $roomStayInput['StartDate'] = $startDate;
        $roomStayInput['EndDate'] = $endDate;

        // 遍历给$product添加时间
        foreach ($roomStayInput['Products'] as &$product) {
            $product['StartDate'] = $startDate;
            $product['EndDate'] = $endDate;
        }
        $roomStay = RoomStayDTO::fromArray($roomStayInput);

        // 构建忠诚度会员信息 (修改预订时不需要)
        $loyaltyMemberships = null;
        if (! $isModification && $withMembership && isset($params['loyaltyMemberships'])) {
            $loyaltyMemberships = [];
            foreach ($params['loyaltyMemberships'] as $membershipData) {
                $membershipData['StartDate'] = $startDate;
                $membershipData['EndDate'] = $endDate;
                $loyaltyMemberships[] = LoyaltyMembershipDTO::fromArray($membershipData);
            }
        }

        // 构建促销信息 (修改预订时不需要)
        $promotion = null;
        if (! $isModification && $withPromo && isset($params['promotion'])) {
            $promotion = PromotionDTO::fromArray($params['promotion']);
        }

        return new ReservationRequestDTO(
            bookingInfo: $bookingInfo,
            chain: $chain,
            hotel: $hotel,
            channels: $channels,
            guests: $guests,
            language: $language,
            marketSource: $marketSource,
            notification: $notification,
            roomStay: $roomStay,
            status: $params['status'] ?? 'Confirmed',
            loyaltyMemberships: $loyaltyMemberships,
            promotion: $promotion,
            itineraryNumber: $params['itineraryNumber'] ?? null,
            crsConfirmationNumber: $isModification ? ($params['CRS_confirmationNumber'] ?? null) : null,
            purposeOfStay: $isModification ? ($params['PurposeOfStay'] ?? null) : null,
            roomStaySplit: $isModification ? ($params['roomStaySplit'] ?? null) : null
        );
    }

    /**
     * 解析预订响应
     */
    protected function parseReservationResponse(array $response): array
    {
        $parsed = [
            'success' => false,
            'reservation_id' => null,
            'confirmation_number' => null,
            'itinerary_number' => null,
            'status' => null,
            'errors' => [],
        ];

        try {
            if (isset($response['reservations']) && is_array($response['reservations']) && count($response['reservations']) > 0) {
                $reservation = $response['reservations'][0];

                $parsed['success'] = true;
                $parsed['reservation_id'] = $reservation['Id'] ?? null;
                $parsed['confirmation_number'] = $reservation['CrsConfirmationNumber'] ?? null;
                $parsed['itinerary_number'] = $reservation['ItineraryNumber'] ?? null;
                $parsed['status'] = $reservation['Status'] ?? null;
                $parsed['raw_response'] = $response;
            } else {
                $parsed['errors'][] = '未找到预订信息';
                if (isset($response['errors'])) {
                    $parsed['errors'] = array_merge($parsed['errors'], $response['errors']);
                }
            }
        } catch (\Exception $e) {
            Log::error('Parse Reservation Response Error', [
                'message' => $e->getMessage(),
                'response' => $response,
            ]);

            $parsed['errors'][] = '解析预订响应失败: '.$e->getMessage();
        }

        return $parsed;
    }

    /**
     * 查询预订
     */
    public function getReservation(array $params): array
    {
        try {
            if (isset($params['itineraryNumber'])) {
                $result = $this->sabreService->getReservationByItinerary(
                    $params['itineraryNumber'],
                    $params['channel'] ?? 'DSCVRYLYLTY'
                );
            } elseif (isset($params['confirmationNumber'])) {
                $result = $this->sabreService->getReservationByConfirmation(
                    $params['confirmationNumber'],
                    $params['chainId'] ?? config('sabre.defaults.chain_id'),
                    $params['hotelId'],
                    $params['channel'] ?? 'DSCVRYLYLTY'
                );
            } else {
                throw new SabreApiException('必须提供行程号或确认号');
            }

            // 使用新的数据转换器处理响应数据
            $response = [
                'confirmationNumber' => $params['confirmationNumber'] ?? null,
                'response' => $result,
            ];

            // 根据 format 参数决定返回格式
            $format = $params['format'] ?? 'simplified';

            if ($format === 'availability') {
                // 返回与 availability 接口一致的格式
                return $this->dataTransformer->transformReservationResponse($response, false, true);
            } elseif ($format === 'standard') {
                // 返回标准格式（非简化）
                return $this->dataTransformer->transformReservationResponse($response, false, false);
            } else {
                // 默认返回简化格式
                return $this->dataTransformer->transformReservationResponse($response, true, false);
            }

        } catch (SabreApiException $e) {
            Log::error('Get Reservation Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 修改预订
     */
    public function modifyReservation(array $params): array
    {
        try {
            // 使用统一的构建方法创建修改预订请求
            $request = $this->buildReservationRequest($params, false, false, true);
            $result = $this->sabreService->modifyReservation($request->toArray());

            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Modify Reservation Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 取消预订
     */
    public function cancelReservation(string $confirmationNumber, int $hotelId, ?string $hotelCode = null): array
    {
        try {
            $result = $this->sabreService->cancelReservation($confirmationNumber, $hotelId, $hotelCode);

            return [
                'success' => true,
                'confirmation_number' => $confirmationNumber,
                'status' => 'Cancelled',
                'raw_response' => $result,
            ];
        } catch (SabreApiException $e) {
            Log::error('Cancel Reservation Error', [
                'message' => $e->getMessage(),
                'confirmation_number' => $confirmationNumber,
                'hotel_id' => $hotelId,
            ]);

            return [
                'success' => false,
                'confirmation_number' => $confirmationNumber,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 根据行程号取消所有相关预订
     */
    public function cancelReservationByItinerary(string $itineraryNumber, int $hotelId, ?string $hotelCode = null, string $channel = 'DSCVRYLYLTY'): array
    {
        try {
            // 首先根据行程号查询预订信息
            $itineraryData = $this->sabreService->getReservationByItinerary($itineraryNumber, $channel);

            if (! $itineraryData || empty($itineraryData)) {
                throw new SabreApiException('未找到指定的行程信息');
            }

            // 提取所有确认号
            $confirmationNumbers = $this->extractConfirmationNumbers($itineraryData);

            if (empty($confirmationNumbers)) {
                throw new SabreApiException('行程中未找到可取消的预订');
            }

            // 批量取消所有确认号
            $results = [];
            $successCount = 0;
            $failedCount = 0;

            foreach ($confirmationNumbers as $confirmationNumber) {
                $cancelResult = $this->cancelReservation($confirmationNumber, $hotelId, $hotelCode);
                $results[] = $cancelResult;

                if ($cancelResult['success']) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }

            return [
                'success' => $successCount > 0,
                'itinerary_number' => $itineraryNumber,
                'total_reservations' => count($confirmationNumbers),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'details' => $results,
                'message' => $successCount > 0 ?
                    ($failedCount > 0 ? "部分取消成功：{$successCount}个成功，{$failedCount}个失败" : '全部取消成功') :
                    '全部取消失败',
            ];

        } catch (SabreApiException $e) {
            Log::error('Cancel Reservation By Itinerary Error', [
                'message' => $e->getMessage(),
                'itinerary_number' => $itineraryNumber,
                'hotel_id' => $hotelId,
            ]);

            return [
                'success' => false,
                'itinerary_number' => $itineraryNumber,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 批量取消多个确认号的预订
     */
    public function cancelMultipleReservations(array $confirmationNumbers, int $hotelId, ?string $hotelCode = null): array
    {
        $results = [];
        $successCount = 0;
        $failedCount = 0;

        foreach ($confirmationNumbers as $confirmationNumber) {
            $cancelResult = $this->cancelReservation($confirmationNumber, $hotelId, $hotelCode);
            $results[] = $cancelResult;

            if ($cancelResult['success']) {
                $successCount++;
            } else {
                $failedCount++;
            }
        }

        return [
            'success' => $successCount > 0,
            'total_reservations' => count($confirmationNumbers),
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'details' => $results,
            'message' => $successCount > 0 ?
                ($failedCount > 0 ? "部分取消成功：{$successCount}个成功，{$failedCount}个失败" : '全部取消成功') :
                '全部取消失败',
        ];
    }

    /**
     * 从行程数据中提取确认号
     */
    private function extractConfirmationNumbers(array $itineraryData): array
    {
        $confirmationNumbers = [];

        // 根据实际的API响应格式提取确认号
        // 处理 Reservations 数组格式
        if (isset($itineraryData['Reservations']) && is_array($itineraryData['Reservations'])) {
            foreach ($itineraryData['Reservations'] as $reservation) {
                // 提取 CRS_confirmationNumber
                if (isset($reservation['CRS_confirmationNumber'])) {
                    $confirmationNumbers[] = $reservation['CRS_confirmationNumber'];
                }
                // 提取 CRSConfirmationNumber (备用字段)
                if (isset($reservation['CRSConfirmationNumber'])) {
                    $confirmationNumbers[] = $reservation['CRSConfirmationNumber'];
                }
            }
        }

        // 处理旧版本的 TravelItinerary 格式
        if (empty($confirmationNumbers) && isset($itineraryData['TravelItinerary']['ItineraryInfo']['ReservationItems'])) {
            $reservationItems = $itineraryData['TravelItinerary']['ItineraryInfo']['ReservationItems'];

            foreach ($reservationItems as $item) {
                if (isset($item['Hotel']['BasicPropertyInfo']['ConfirmationNumber'])) {
                    $confirmationNumbers[] = $item['Hotel']['BasicPropertyInfo']['ConfirmationNumber'];
                }
            }
        }

        // 如果以上结构都不匹配，尝试直接获取确认号
        if (empty($confirmationNumbers) && isset($itineraryData['ConfirmationNumber'])) {
            $confirmationNumbers[] = $itineraryData['ConfirmationNumber'];
        }

        // 去重并过滤空值
        return array_unique(array_filter($confirmationNumbers));
    }

    /**
     * 修改多房间预订
     */
    public function modifyMultiRoomReservation(array $params): array
    {
        try {
            $results = [];
            $itineraryNumber = $params['itineraryNumber'] ?? null;

            if (! $itineraryNumber) {
                throw new SabreApiException('修改多房间预订需要提供行程号');
            }

            // 遍历每个房间进行修改
            foreach ($params['rooms'] as $index => $roomData) {
                try {
                    // 构建单个房间的修改参数
                    $singleRoomParams = array_merge($params, $roomData);
                    $singleRoomParams['CRS_confirmationNumber'] = $roomData['confirmationNumber'] ?? null;

                    if (! $singleRoomParams['CRS_confirmationNumber']) {
                        $results[] = [
                            'success' => false,
                            'room_index' => $index,
                            'error' => '房间修改需要提供确认号',
                        ];

                        continue;
                    }

                    // 调用单房间修改
                    $result = $this->modifyReservation($singleRoomParams);

                    $results[] = [
                        'success' => $result['success'] ?? false,
                        'room_index' => $index,
                        'confirmation_number' => $result['confirmation_number'] ?? null,
                        'raw_response' => $result,
                    ];

                } catch (SabreApiException $e) {
                    Log::error('Modify Multi-Room Reservation - Room Error', [
                        'room_index' => $index,
                        'message' => $e->getMessage(),
                        'itinerary_number' => $itineraryNumber,
                    ]);

                    $results[] = [
                        'success' => false,
                        'room_index' => $index,
                        'error' => $e->getUserMessage(),
                    ];
                }
            }

            // 统计成功和失败数量
            $successCount = count(array_filter($results, fn ($r) => $r['success']));
            $failedCount = count($results) - $successCount;

            return [
                'success' => $successCount > 0,
                'itinerary_number' => $itineraryNumber,
                'total_rooms' => count($results),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'rooms' => $results,
            ];

        } catch (SabreApiException $e) {
            Log::error('Modify Multi-Room Reservation Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 综合修改预订（支持房间数量变化）
     * 该方法可以处理：修改现有房间、取消多余房间、添加新房间
     */
    public function modifyReservationWithRoomChanges(array $params): array
    {
        try {
            $itineraryNumber = $params['itineraryNumber'] ?? null;
            $currentRooms = $params['currentRooms'] ?? []; // 当前房间列表 [confirmationNumber => roomData]
            $targetRooms = $params['targetRooms'] ?? [];   // 目标房间列表

            if (! $itineraryNumber) {
                throw new SabreApiException('修改预订需要提供行程号');
            }

            $results = [
                'itinerary_number' => $itineraryNumber,
                'modifications' => [],
                'cancellations' => [],
                'additions' => [],
                'summary' => [
                    'total_operations' => 0,
                    'successful_operations' => 0,
                    'failed_operations' => 0,
                ],
            ];

            // 1. 处理现有房间的修改
            foreach ($targetRooms as $index => $targetRoom) {
                $confirmationNumber = $targetRoom['confirmationNumber'] ?? null;

                if ($confirmationNumber && isset($currentRooms[$confirmationNumber])) {
                    // 房间存在，执行修改
                    try {
                        $modifyParams = array_merge($params, $targetRoom);
                        $modifyParams['CRS_confirmationNumber'] = $confirmationNumber;

                        $result = $this->modifyReservation($modifyParams);

                        $results['modifications'][] = [
                            'success' => $result['success'] ?? false,
                            'room_index' => $index,
                            'confirmation_number' => $confirmationNumber,
                            'operation' => 'modify',
                            'result' => $result,
                        ];

                        if ($result['success'] ?? false) {
                            $results['summary']['successful_operations']++;
                        } else {
                            $results['summary']['failed_operations']++;
                        }
                        $results['summary']['total_operations']++;

                        // 从当前房间列表中移除已处理的房间
                        unset($currentRooms[$confirmationNumber]);

                    } catch (SabreApiException $e) {
                        $results['modifications'][] = [
                            'success' => false,
                            'room_index' => $index,
                            'confirmation_number' => $confirmationNumber,
                            'operation' => 'modify',
                            'error' => $e->getUserMessage(),
                        ];
                        $results['summary']['failed_operations']++;
                        $results['summary']['total_operations']++;
                    }
                }
            }

            // 2. 处理需要取消的房间（当前房间列表中剩余的房间）
            foreach ($currentRooms as $confirmationNumber => $currentRoom) {
                try {
                    $cancelResult = $this->cancelReservation(
                        $confirmationNumber,
                        $params['hotelId'],
                        $params['hotelCode'] ?? null
                    );

                    $results['cancellations'][] = [
                        'success' => $cancelResult['success'] ?? false,
                        'confirmation_number' => $confirmationNumber,
                        'operation' => 'cancel',
                        'result' => $cancelResult,
                    ];

                    if ($cancelResult['success'] ?? false) {
                        $results['summary']['successful_operations']++;
                    } else {
                        $results['summary']['failed_operations']++;
                    }
                    $results['summary']['total_operations']++;

                } catch (SabreApiException $e) {
                    $results['cancellations'][] = [
                        'success' => false,
                        'confirmation_number' => $confirmationNumber,
                        'operation' => 'cancel',
                        'error' => $e->getUserMessage(),
                    ];
                    $results['summary']['failed_operations']++;
                    $results['summary']['total_operations']++;
                }
            }

            // 3. 处理需要添加的新房间（没有确认号的房间）
            foreach ($targetRooms as $index => $targetRoom) {
                $confirmationNumber = $targetRoom['confirmationNumber'] ?? null;

                if (! $confirmationNumber) {
                    // 这是新房间，需要创建预订
                    try {
                        $addParams = array_merge($params, $targetRoom);
                        $addParams['itineraryNumber'] = $itineraryNumber; // 使用现有行程号

                        // 根据会员和促销状态选择创建方法
                        $hasLoyalty = ! empty($addParams['loyaltyNumber']);
                        $hasPromo = ! empty($addParams['promoCode']);

                        if ($hasLoyalty && $hasPromo) {
                            $result = $this->createSingleRoomBookingWithMembershipAndPromo($addParams);
                        } elseif ($hasLoyalty) {
                            $result = $this->createSingleRoomBookingWithMembership($addParams);
                        } elseif ($hasPromo) {
                            $result = $this->createSingleRoomBookingNonMemberWithPromo($addParams);
                        } else {
                            $result = $this->createSingleRoomBookingNonMember($addParams);
                        }

                        $results['additions'][] = [
                            'success' => $result['success'] ?? false,
                            'room_index' => $index,
                            'operation' => 'add',
                            'confirmation_number' => $result['confirmation_number'] ?? null,
                            'result' => $result,
                        ];

                        if ($result['success'] ?? false) {
                            $results['summary']['successful_operations']++;
                        } else {
                            $results['summary']['failed_operations']++;
                        }
                        $results['summary']['total_operations']++;

                    } catch (SabreApiException $e) {
                        $results['additions'][] = [
                            'success' => false,
                            'room_index' => $index,
                            'operation' => 'add',
                            'error' => $e->getUserMessage(),
                        ];
                        $results['summary']['failed_operations']++;
                        $results['summary']['total_operations']++;
                    }
                }
            }

            // 4. 计算总体成功状态
            $results['success'] = $results['summary']['successful_operations'] > 0;

            Log::info('Comprehensive Reservation Modification Completed', [
                'itinerary_number' => $itineraryNumber,
                'summary' => $results['summary'],
            ]);

            return $results;

        } catch (SabreApiException $e) {
            Log::error('Comprehensive Reservation Modification Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }
}
