<?php

namespace App\Services;

use App\Models\City;
use App\Models\Continent;
use App\Models\Hotel;
use App\Models\HotelEco;
use App\Models\HotelImage;
use App\Models\HotelInterest;
use App\Models\HotelLoop;
use App\Models\HotelNeighborhood;
use App\Models\HotelRom;
use App\Models\HotelType;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class HotelService
{
    public function handleHotel($data, $type)
    {
        switch ($type) {
            case "App\Message\ChinaHotelUpdateMessage":
                $hotel = Hotel::where('hotelcode', $data['id'])->first();
                if (empty($hotel)) {
                    $hotel = new Hotel;
                }

                // 同步酒店类型
                if (isset($data['hotelType']) && $data['hotelType']) {
                    foreach ($data['hotelType'] as $value) {
                        $hotel_type = HotelType::where('name_en', $value['name'])->first();
                        if (empty($hotel_type)) {
                            $hotel_type = new HotelType;
                        }
                        $hotel_type->original_id = $value['id'];
                        $hotel_type->name_en = $value['name'];
                        $hotel_type->save();
                    }
                }
                // 同步酒店系列
                if (isset($data['categories']) && $data['categories']) {
                    foreach ($data['categories'] as $value) {
                        $hotel_loop = HotelLoop::where('name_en', $value['name'])->first();
                        if (empty($hotel_loop)) {
                            $hotel_loop = new HotelLoop;
                        }
                        $hotel_loop->original_id = $value['id'];
                        $hotel_loop->name_en = $value['name'];
                        $hotel_loop->save();
                    }
                }
                // 酒店设施
                if (isset($data['facilities']) && $data['facilities']) {
                    foreach ($data['facilities'] as $value) {
                        $hotel_roms = HotelRom::where('name_en', $value['name'])->first();
                        if (empty($hotel_roms)) {
                            $hotel_roms = new HotelRom;
                        }
                        $hotel_roms->original_id = $value['id'];
                        $hotel_roms->name_en = $value['name'];
                        $hotel_roms->save();
                    }
                }
                // 兴趣爱好
                if (isset($data['interests']) && $data['interests']) {
                    foreach ($data['interests'] as $value) {
                        $hotelInterest = HotelInterest::where('name_en', $value['name'])->first();
                        if (empty($hotelInterest)) {
                            $hotelInterest = new HotelInterest;
                        }
                        $hotelInterest->original_id = $value['id'];
                        $hotelInterest->name_en = $value['name'];
                        $hotelInterest->save();
                    }
                }
                // 目的地
                if (isset($data['destinations']) && $data['destinations']) {
                    foreach ($data['destinations'] as $value) {
                        $hotelInterest = Continent::where('name_en', $value['name'])->first();
                        if (empty($hotelInterest)) {
                            $hotelInterest = new Continent;
                        }
                        $hotelInterest->original_id = $value['id'];
                        $hotelInterest->name_en = $value['name'];
                        $hotelInterest->save();
                    }
                }
                // 生态证书
                if (isset($data['ecoCertificates']) && $data['ecoCertificates']) {
                    foreach ($data['ecoCertificates'] as $value) {
                        $HotelEco = HotelEco::where('name_en', $value['name'])->first();
                        if (empty($HotelEco)) {
                            $HotelEco = new HotelEco;
                        }
                        $HotelEco->original_id = $value['id'];
                        $HotelEco->name_en = $value['name'];
                        $HotelEco->save();
                    }
                }
                // 社区标签
                if (isset($data['neighborhoodTag']) && $data['neighborhoodTag']) {
                    foreach ($data['neighborhoodTag'] as $value) {
                        $Neighborhood = HotelNeighborhood::where('name_en', $value['name'])->first();
                        if (empty($Neighborhood)) {
                            $Neighborhood = new HotelNeighborhood;
                        }
                        $Neighborhood->original_id = $value['id'];
                        $Neighborhood->name_en = $value['name'];
                        $Neighborhood->save();
                    }
                }
                // 城市
                if (isset($data['city']) && $data['city']) {
                    foreach ($data['city'] as $value) {
                        $city = City::where('original_id', $value['id'])->first();
                        if (empty($city)) {
                            $city = new City;
                        }
                        $city->original_id = $value['id'];
                        $city->city_name_en = $value['name'];
                        $city->save();
                    }
                }

                // 酒店id
                $hotel->brand_code = $data['brandId'] ?? '';
                // 酒店名称
                $hotel->brand_nameEn = $data['brandName'] ?? '';
                // 酒店标识
                $hotel->code = $data['code'];
                // 酒店名称
                $hotel->hotel_name = $data['name'];
                // 酒店英文名称
                $hotel->hotel_name_en = $data['name'];
                // 酒店类型
                $hotel->hotel_type = ! empty($data['hotelType']) ? encodeForDatabase($data['hotelType'], JSON_UNESCAPED_SLASHES) : null;
                // 新酒店开始时间
                if (! empty($data['newHotelStartDate'])) {
                    $hotel->new_hotel_start_date = $data['newHotelStartDate'];
                }
                // 新酒店开始时间
                if (! empty($data['newHotelEndDate'])) {
                    $hotel->new_hotel_end_date = $data['newHotelEndDate'];
                }
                // 感谢祭酒店id
                $hotel->synxis_hotel_id = $data['synXisHotelId'];
                $hotel->synxis_chain_id = $data['synXisChainId'];
                $hotel->sabre_tax_setup = $data['sabreTaxSetup'];
                // 城市
                $hotel->city_code = ! empty($data['city']) ? encodeForDatabase($data['city'], JSON_UNESCAPED_SLASHES) : null;
                // 区域
                $hotel->region = ! empty($data['region']) ? encodeForDatabase($data['region'], JSON_UNESCAPED_SLASHES) : null;
                // 邮政编码
                $hotel->postal_code = $data['zipCode'];
                // 国家区号
                $hotel->phone_country_code = $data['phoneCountryCode'] ?? '';
                // 电话区号
                $hotel->phone_area_code = $data['phoneAreaCode'];
                // 酒店电话
                $hotel->phone = $data['generalHotelPhoneNumber'];
                // 邮箱
                $hotel->email = $data['generalHotelEmail'];
                // 酒店描述
                $hotel->hotel_desc_en = trim($data['description']);
                // 经度
                $hotel->longitude = $data['location']['longitude'] ?? '';
                // 纬度
                $hotel->latitude = $data['location']['latitude'] ?? '';
                // 设施
                $hotel->feature = ! empty($data['facilities']) ? encodeForDatabase($data['facilities'], JSON_UNESCAPED_SLASHES) : null;
                // 渐变的颜色
                $hotel->gradient_color = $data['gradientColor'];
                // 零率码
                $hotel->zero_rate_code = $data['zeroRateCode'];
                // 层速度
                $hotel->floor_rate = $data['floorRate'];
                // 酒店照片
                $hotel->images = ! empty($data['images']) ? encodeForDatabase($data['images'], JSON_UNESCAPED_SLASHES) : null;
                // 所在州/目的地
                $hotel->destinations = ! empty($data['destinations']) ? encodeForDatabase($data['destinations'], JSON_UNESCAPED_SLASHES) : null;
                // 兴趣
                $hotel->interests = ! empty($data['interests']) ? encodeForDatabase($data['interests'], JSON_UNESCAPED_SLASHES) : null;
                // 生态证书
                $hotel->eco_certificates = ! empty($data['ecoCertificates']) ? encodeForDatabase($data['ecoCertificates'], JSON_UNESCAPED_SLASHES) : null;
                // 酒店系列
                $hotel->categories = ! empty($data['categories']) ? encodeForDatabase($data['categories'], JSON_UNESCAPED_SLASHES) : null;
                // 是否新酒店
                $hotel->new_hotel = $data['newHotel'] ? 1 : 2;
                // 社区标签
                $hotel->neighborhood_tag = ! empty($data['neighborhoodTag']) ? encodeForDatabase($data['neighborhoodTag'], JSON_UNESCAPED_SLASHES) : null;
                $brand_nameEn = Str::slug($hotel->brand_nameEn, '-');
                $hotel_name_en = Str::slug($hotel->hotel_name_en, '-');
                if ($brand_nameEn && $hotel_name_en) {
                    $url = '/'.$brand_nameEn.'/'.$hotel_name_en;
                } else {
                    $url = '/'.$hotel_name_en;
                }
                $hotel->url = $url ?? '';
                $extend = [
                    // 子品牌
                    'subBrand' => $data['subBrand'],
                    // 酒店邮件地址
                    'hotelEmailAddresses' => $data['hotelEmailAddresses'],
                    // 消息启用
                    'noAvailabilityMessageEnable' => $data['noAvailabilityMessageEnable'],
                    // 消息启用手机号
                    'noAvailabilityMessageMobile' => $data['noAvailabilityMessageMobile'],
                    // 消息
                    'noAvailabilityMessage' => $data['noAvailabilityMessage'],
                    // 地址
                    'address' => $data['location']['address'] ?? '',
                    // 房间信息同步
                    'synXisRoomInformationSync' => $data['synXisRoomInformationSync'],
                    // 拨号代码
                    'dialingCode' => $data['dialingCode'],
                    // meta标题
                    'metaTitle' => $data['metaTitle'],
                    // meta关键词
                    'metaKeywords' => $data['metaKeywords'],
                    // meta描述
                    'metaDescription' => $data['metaDescription'],
                    // 邻近城市
                    'nearByCities' => $data['nearByCities'],
                    // 酒店类型
                    'hotelType' => $data['hotelType'],
                    // 发送内部确认
                    'sendInternalConfirmation' => $data['sendInternalConfirmation'],
                    // 新酒店
                    'newHotel' => $data['newHotel'],
                    // 附近
                    'neighbourhood' => ! empty($data['neighbourhood']) ? encodeForDatabase($data['neighbourhood'], JSON_UNESCAPED_SLASHES) : null,
                ];
                $hotel->extend = encodeForDatabase($extend, JSON_UNESCAPED_SLASHES) ?? null;
                $hotel->hotelcode = $data['id'];
                $hotel->version_no = $data['versionNo'];
                $hotel->save();
                break;
            case "App\Message\ChinaHotelRemovedMessage":
                Hotel::where('hotelcode', $data['id'])->delete();
                break;
            default:
                Log::info('收到一个未处理的消息类型');
        }
    }

    /**
     * 保存图片
     *
     * @param  $brand
     */
    private function saveImage($hotel, $images): void
    {
        if (! empty($images)) {
            $img = [];
            foreach ($images as $photo) {
                $img[] = [
                    'brand_code' => $hotel['brand_code'],
                    'hotel_code' => $hotel['hotelcode'],
                    'type' => HotelImage::TYPE_HOTEL,
                    'source_url' => $photo,
                    'url' => $photo,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
                HotelImage::insert($img);
            }
        }
    }
}
