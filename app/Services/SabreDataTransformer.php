<?php

namespace App\Services;

use App\DTOs\DailyPriceDTO;
use App\DTOs\RoomAvailabilityResponseDTO;
use App\DTOs\RoomDTO;
use App\DTOs\RoomRateDTO;
use App\Models\ExchangeRate;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SabreDataTransformer
{
    /**
     * 汇率配置 - 从配置文件获取或使用默认值
     */
    protected array $exchangeRates = [
        // 兜底倍率（到CNY），当数据库未命中时使用
        'USD' => 7.2,
        'EUR' => 7.8,
        'GBP' => 9.1,
        'JPY' => 0.048,
        'CNY' => 1.0,
    ];

    public function __construct()
    {
        // 从配置文件加载汇率（如果config函数存在）
        if (function_exists('config')) {
            $this->exchangeRates = array_merge(
                $this->exchangeRates,
                config('sabre.exchange_rates', [])
            );
        }
    }

    /**
     * 将Sabre可用性响应转换为标准格式
     */
    public function transformAvailabilityResponse(array $sabreResponse): RoomAvailabilityResponseDTO
    {
        $rooms = [];

        try {
            // 检查响应结构
            if (! isset($sabreResponse['productAvailability']['Prices'])) {
                Log::warning('Sabre response missing productAvailability.Prices', ['response' => $sabreResponse]);

                return new RoomAvailabilityResponseDTO([]);
            }
            $prices = $sabreResponse['productAvailability']['Prices'];

            // 从真实Sabre数据结构中获取房型和价格信息
            $roomList = $sabreResponse['contentLists']['RoomList'] ?? [];
            $rateList = $sabreResponse['contentLists']['RateList'] ?? [];

            // 从 contentLists 获取政策详情
            $policyList = $sabreResponse['contentLists']['PolicyList'] ?? [];

            // 按房型分组价格数据
            $groupedRooms = $this->groupByRoom($prices, $roomList, $rateList);

            foreach ($groupedRooms as $roomCode => $roomData) {
                $room = $this->transformRoom($roomCode, $roomData, $roomList, $rateList, $policyList);
                if ($room) {
                    $rooms[] = $room;
                }
            }

        } catch (\Exception $e) {
            Log::error('Error transforming Sabre availability response', [
                'error' => $e->getMessage(),
                'response' => $sabreResponse,
            ]);
        }

        return new RoomAvailabilityResponseDTO($rooms);
    }

    /**
     * 按房型分组价格数据
     */
    protected function groupByRoom(array $prices, array $roomList, array $rateList): array
    {
        $grouped = [];

        foreach ($prices as $priceData) {
            $roomCode = $priceData['Product']['Room']['Code'] ?? 'UNKNOWN';

            if (! isset($grouped[$roomCode])) {
                // 从RoomList中获取房型详细信息
                $roomInfo = $this->getRoomInfoFromRoomList($roomCode, $roomList);

                $grouped[$roomCode] = [
                    'room_info' => $roomInfo,
                    'rates' => [],
                ];
            }

            $grouped[$roomCode]['rates'][] = $priceData;
        }

        return $grouped;
    }

    /**
     * 从RoomList中获取房型信息
     */
    protected function getRoomInfoFromRoomList(string $roomCode, array $roomList): array
    {
        $roomInfo = [
            'Code' => $roomCode,
            'Name' => $roomCode,
            'Description' => $roomCode,
        ];

        // 查找房型信息
        foreach ($roomList as $room) {
            if ($room['Code'] === $roomCode) {
                $roomInfo['Name'] = $room['Name'] ?? $roomCode;
                // 优先使用Details.DetailedDescription，然后是Details.Description，最后是Name
                $roomInfo['Description'] = $room['Details']['DetailedDescription'] ??
                                         $room['Details']['Description'] ??
                                         $room['DetailedDescription'] ??
                                         $room['Description'] ??
                                         $room['Name'] ??
                                         $roomCode;
                $roomInfo['MaxOccupancy'] = $room['MaxOccupancy'] ?? null;
                break;
            }
        }

        return $roomInfo;
    }

    /**
     * 从RateList中获取价格信息
     */
    protected function getRateInfoFromRateList(string $rateCode, array $rateList): array
    {
        $rateInfo = [
            'Code' => $rateCode,
            'Name' => $rateCode,
            'Description' => $rateCode,
        ];

        // 查找价格信息
        foreach ($rateList as $rate) {
            if ($rate['Code'] === $rateCode) {
                $rateInfo['Name'] = $rate['Name'] ?? $rateCode;
                // 优先使用Details.DetailedDescription，然后是Details.Description，最后是Name
                $rateInfo['Description'] = $rate['Details']['DetailedDescription'] ??
                                         $rate['Details']['Description'] ??
                                         $rate['DetailedDescription'] ??
                                         $rate['Description'] ??
                                         $rate['Name'] ??
                                         $rateCode;
                break;
            }
        }

        return $rateInfo;
    }

    /**
     * 从Sabre数据中提取价格信息
     */
    protected function extractPriceFromSabreData(array $rateData): array
    {
        $priceInfo = [
            'currency' => 'USD',
            'total' => 0.0,
            'base' => 0.0,
            'tax' => 0.0,
            'fee' => 0.0,
        ];

        // 根据真实Sabre数据结构查找价格数据
        $product = $rateData['Product'] ?? [];
        $prices = $product['Prices'] ?? [];
        $rateCode = Arr::get($product, 'Rate.Code');
        // 优先使用Total价格
        if (isset($prices['Total']['Price'])) {
            $price = $prices['Total']['Price'];
            $priceInfo = $this->parsePriceObject($price);
        }
        // 其次使用PerNight价格
        elseif (isset($prices['PerNight']['Price'])) {
            $price = $prices['PerNight']['Price'];
            $priceInfo = $this->parsePriceObject($price);
        }
        // 最后尝试从Daily价格中获取（使用第一天的价格）
        elseif (isset($prices['Daily']) && is_array($prices['Daily']) && ! empty($prices['Daily'])) {
            $firstDayPrice = $prices['Daily'][0]['Price'] ?? [];
            if (! empty($firstDayPrice)) {
                $priceInfo = $this->parsePriceObject($firstDayPrice);
            }
        }

        return $priceInfo;
    }

    /**
     * 解析价格对象
     */
    protected function parsePriceObject(array $price): array
    {
        $priceInfo = [
            'currency' => $price['CurrencyCode'] ?? 'USD',
            'total' => 0.0,
            'base' => 0.0,
            'tax' => 0.0,
            'fee' => 0.0,
        ];

        // 基础价格
        $priceInfo['base'] = (float) ($price['Amount'] ?? 0);

        // 税费
        if (isset($price['Tax']['Amount'])) {
            $priceInfo['tax'] = (float) $price['Tax']['Amount'];
        }

        // 服务费
        if (isset($price['Fees']['Amount'])) {
            $priceInfo['fee'] = (float) $price['Fees']['Amount'];
        }

        // 总价 - 优先使用AmountWithTaxesFees，其次使用计算值
        if (isset($price['Total']['AmountWithTaxesFees'])) {
            $priceInfo['total'] = (float) $price['Total']['AmountWithTaxesFees'];
        } elseif (isset($price['Total']['Amount'])) {
            $priceInfo['total'] = (float) $price['Total']['Amount'];
        } else {
            // 如果没有总价，计算总价
            $priceInfo['total'] = $priceInfo['base'] + $priceInfo['tax'] + $priceInfo['fee'];
        }

        return $priceInfo;
    }

    /**
     * 转换单个房型数据
     */
    protected function transformRoom(string $roomCode, array $roomData, array $roomList, array $rateList, array $policyList = []): ?RoomDTO
    {
        try {
            $roomInfo = $roomData['room_info'];
            $rates = $roomData['rates'];

            $roomName = $roomInfo['Name'] ?? $roomCode;
            $roomDescription = $roomInfo['Description'] ?? $roomInfo['Name'] ?? $roomCode;

            $roomRateList = [];
            foreach ($rates as $rateData) {
                $roomRate = $this->transformRoomRate($roomCode, $rateData, $rateList, $policyList);
                if ($roomRate) {
                    $roomRateList[] = $roomRate;
                }
            }

            if (empty($roomRateList)) {
                return null;
            }

            return new RoomDTO(
                roomCode: $roomCode,
                roomName: $roomName,
                roomDescription: $roomDescription,
                roomRateList: $roomRateList
            );

        } catch (\Exception $e) {
            Log::error('Error transforming room data', [
                'room_code' => $roomCode,
                'error' => $e->getMessage(),
                'room_data' => $roomData,
            ]);

            return null;
        }
    }

    /**
     * 转换房价数据
     */
    protected function transformRoomRate(string $roomCode, array $rateData, array $rateList, array $policyList = []): ?RoomRateDTO
    {
        try {
            // 根据真实的Sabre数据结构提取信息
            $product = $rateData['Product'] ?? [];
            $rate = $product['Rate'] ?? [];
            // 从价格数据中获取取消政策的 code
            $cancelPolicyCode = $product['CancelPolicy']['Code'] ?? null;

            // 根据 code 从 contentLists.PolicyList.CancelPolicy 中找到对应的详细信息
            $cancelPolicyDetails = [];
            if ($cancelPolicyCode && isset($policyList['CancelPolicy'])) {
                foreach ($policyList['CancelPolicy'] as $policy) {
                    if (($policy['Code'] ?? null) === $cancelPolicyCode) {
                        $cancelPolicyDetails[] = $policy;
                        break;
                    }
                }
            }

            // 向后兼容：如果没有找到，尝试旧的结构
            if (empty($cancelPolicyDetails)) {
                $cancelPolicyDetails = $rateData['PolicyList']['CancelPolicy'] ?? [];
                if (empty($cancelPolicyDetails)) {
                    $cancelPolicyDetails = $rateData['Policies'] ?? [];
                }
            }

            $policies = $cancelPolicyDetails;

            // 提取基本信息
            $rateCode = $rate['Code'] ?? 'UNKNOWN';

            // 从RateList中获取价格名称和描述
            $rateInfo = $this->getRateInfoFromRateList($rateCode, $rateList);
            $rateName = $rateInfo['Name'] ?? $rate['Name'] ?? $rateCode;
            $rateDescription = $rateInfo['Description'] ?? $rate['Description'] ?? $rateInfo['Name'] ?? $rateName;

            // 根据真实Sabre数据结构提取价格信息
            $priceInfo = $this->extractPriceFromSabreData($rateData);
            $currency = $priceInfo['currency'];
            $totalAmount = toCent($priceInfo['total']);
            $baseAmount = toCent($priceInfo['base']);
            $taxAmount = toCent($priceInfo['tax']);
            $feeAmount = toCent($priceInfo['fee']);

            // 转换为人民币（分）
            // 直接转换总价而不是分别转换后相加，避免精度误差
            $exchangeRate = $this->getToCnyMultiplier($currency);
            $cnyTotalPrice = $totalAmount * $exchangeRate;
            $cnyFee = $feeAmount * $exchangeRate;
            $cnyTax = $taxAmount * $exchangeRate;
            $cnyPrice = $cnyTotalPrice - $cnyTax - $cnyFee;
            // 提取担保政策
            $guaranteePolicy = $this->extractGuaranteePolicy($policies);

            // 提取取消政策
            $cancelRuleString = $this->extractCancellationPolicy($policies);

            // 检测是否可以取消
            $isCancellable = $this->checkIfCancellable($policies);
            // 检测是否是会员价格
            $isMemberRate = $this->checkIfMemberRate($rateData);
            // 提取每日价格数据
            $dailyPrices = $this->extractDailyPrices($rateData, $currency);

            return new RoomRateDTO(
                roomCode: $roomCode,
                rateCode: $rateCode,
                rateName: $rateName,
                rateDescription: $rateDescription,
                guaranteePolicy: $guaranteePolicy,
                currency: $currency,
                price: $baseAmount,
                fee: $feeAmount,
                tax: $taxAmount,
                total_price: $totalAmount,
                cny_price: $cnyPrice,
                cny_fee: $cnyFee,
                cny_tax: $cnyTax,
                cny_total_price: $cnyTotalPrice,
                cancelRuleString: $cancelRuleString,
                isCancellable: $isCancellable,
                isMemberRate: $isMemberRate,
                dailyPrices: $dailyPrices
            );

        } catch (\Exception $e) {
            Log::error('Error transforming room rate data', [
                'room_code' => $roomCode,
                'error' => $e->getMessage(),
                'rate_data' => $rateData,
            ]);

            return null;
        }
    }

    /**
     * 提取担保政策
     */
    protected function extractGuaranteePolicy(array $policies): string
    {
        // 根据真实Sabre数据结构查找担保政策
        if (isset($policies['Guarantee'])) {
            $guarantee = $policies['Guarantee'];
            if (is_array($guarantee) && isset($guarantee[0]['Code'])) {
                return $guarantee[0]['Code'];
            } elseif (is_string($guarantee)) {
                return $guarantee;
            }
        }

        // 查找其他可能的担保政策字段
        foreach ($policies as $policy) {
            if (is_array($policy)) {
                if (isset($policy['Type']) && $policy['Type'] === 'Guarantee') {
                    return $policy['Code'] ?? 'GCC_CRD';
                }
                if (isset($policy['GuaranteeType'])) {
                    return $policy['GuaranteeType'];
                }
            }
        }

        // 默认担保政策
        return 'GCC_CRD';
    }

    /**
     * 提取取消政策 - 针对中国用户习惯优化
     */
    protected function extractCancellationPolicy(array $policies): string
    {
        $cancellationRules = [];

        // 处理 Sabre API 中的 PolicyList 数组结构
        foreach ($policies as $policy) {
            if (! is_array($policy)) {
                continue;
            }

            // 检查是否包含取消政策字段
            if (isset($policy['Code']) || isset($policy['Description']) || isset($policy['ChargeType'])) {
                $description = $policy['Description'] ?? '';
                $code = $policy['Code'] ?? '';
                $chargeType = $policy['ChargeType'] ?? '';
                $cancelTime = $policy['CancelTime'] ?? '';
                $cancelTimeIn = $policy['CancelTimeIn'] ?? 0;

                // 构建符合中国用户习惯的取消政策描述
                if ($description) {
                    // 对原始描述进行中文友好化处理
                    $friendlyDescription = $this->formatCancellationDescription($description);
                    $cancellationRules[] = $friendlyDescription;
                } elseif ($chargeType) {
                    switch ($chargeType) {
                        case 'AlwaysChargeCancelFee':
                            $cancellationRules[] = ' 不可取消：预订后任何时间取消都将产生取消费用';
                            break;
                        case 'ChargeWithinDays':
                            if ($cancelTimeIn > 0) {
                                $timeDesc = $this->formatTimeDescription($cancelTimeIn);
                                $cancellationRules[] = " 限时免费取消：入住前{$timeDesc}可免费取消，之后取消将收取费用";
                            }
                            break;
                        case 'NoCharge':
                            $cancellationRules[] = ' 免费取消：可随时免费取消预订';
                            break;
                        default:
                            if ($code) {
                                $cancellationRules[] = " 取消政策：{$code}（详情请咨询酒店）";
                            }
                    }
                }

                // 添加费用信息（更友好的表述）
                if (isset($policy['CancelFeeAmount']['Value']) && $policy['CancelFeeAmount']['Value'] > 0) {
                    $feeValue = $policy['CancelFeeAmount']['Value'];
                    $feeType = $policy['CancelFeeType'] ?? '';

                    if ($feeType === 'PercentAmountOfStay') {
                        $cancellationRules[] = ' 取消费用：按房费的'.$feeValue.'%收取';
                    } else {
                        $cancellationRules[] = ' 取消费用：'.$feeValue.'元';
                    }
                }

                // 添加迟到取消信息
                if (isset($policy['LateCancellationPermitted']) && $policy['LateCancellationPermitted']) {
                    $cancellationRules[] = ' 特殊政策：允许入住当日取消（可能产生费用）';
                }
            }
        }

        // 兼容旧的数据结构
        if (empty($cancellationRules) && isset($policies['Cancellation'])) {
            $cancellation = $policies['Cancellation'];

            if (is_array($cancellation)) {
                foreach ($cancellation as $rule) {
                    if (is_array($rule)) {
                        $description = $rule['Description'] ?? '';
                        $deadline = $rule['Deadline'] ?? '';
                        $penalty = $rule['Penalty'] ?? '';
                        $amount = $rule['Amount'] ?? '';

                        if ($description) {
                            $friendlyDescription = $this->formatCancellationDescription($description);
                            $cancellationRules[] = $friendlyDescription;
                        } elseif ($deadline) {
                            $friendlyDeadline = $this->formatDeadlineDescription($deadline);
                            $penaltyText = $penalty ?: ($amount ? '需支付'.$amount.'元取消费' : '将收取取消费');
                            $cancellationRules[] = ' '.$friendlyDeadline.'之前可免费取消，逾期'.$penaltyText;
                        }
                    } elseif (is_string($rule)) {
                        $friendlyRule = $this->formatCancellationDescription($rule);
                        $cancellationRules[] = $friendlyRule;
                    }
                }
            } elseif (is_string($cancellation)) {
                $friendlyRule = $this->formatCancellationDescription($cancellation);
                $cancellationRules[] = $friendlyRule;
            }
        }

        if (empty($cancellationRules)) {
            return '📞 取消政策详情请联系酒店咨询';
        }

        return implode('，', array_unique($cancellationRules));
    }

    /**
     * 格式化取消政策描述，使其更符合中国用户阅读习惯
     */
    protected function formatCancellationDescription(string $description): string
    {
        // 常见英文关键词到中文的映射
        $translations = [
            'free cancellation' => '免费取消',
            'non-refundable' => '不可取消',
            'non refundable' => '不可取消',
            'no refund' => '不可退款',
            'cancel before' => '在...之前可取消',
            'cancellation fee' => '取消费用',
            'no charge' => '免费',
            'charge applies' => '将收取费用',
        ];

        $formatted = strtolower($description);
        foreach ($translations as $english => $chinese) {
            $formatted = str_ireplace($english, $chinese, $formatted);
        }

        // 如果还是原文，添加适当的图标前缀
        if ($formatted === strtolower($description)) {
            if (str_contains($formatted, '不可') || str_contains($formatted, 'non')) {
                return ' '.$description;
            } elseif (str_contains($formatted, '免费') || str_contains($formatted, 'free')) {
                return ' '.$description;
            } else {
                return ' '.$description;
            }
        }

        return ucfirst($formatted);
    }

    /**
     * 格式化时间描述，使其更具体和友好
     */
    protected function formatTimeDescription(int $days): string
    {
        if ($days <= 0) {
            return '入住当天';
        } elseif ($days == 1) {
            return '1天（24小时）';
        } elseif ($days <= 7) {
            return "{$days}天";
        } elseif ($days <= 14) {
            return "{$days}天（{$this->getWeekDescription($days)}）";
        } else {
            return "{$days}天（约".ceil($days / 7).'周）';
        }
    }

    /**
     * 获取周数描述
     */
    protected function getWeekDescription(int $days): string
    {
        $weeks = floor($days / 7);
        $remainingDays = $days % 7;

        if ($weeks == 1 && $remainingDays == 0) {
            return '1周';
        } elseif ($weeks == 2 && $remainingDays == 0) {
            return '2周';
        } elseif ($weeks > 0 && $remainingDays > 0) {
            return "{$weeks}周{$remainingDays}天";
        } else {
            return "{$weeks}周";
        }
    }

    /**
     * 格式化截止时间描述
     */
    protected function formatDeadlineDescription(string $deadline): string
    {
        // 尝试解析常见的时间格式
        if (preg_match('/(\d+)\s*days?\s*before/i', $deadline, $matches)) {
            $days = (int) $matches[1];

            return '入住前'.$this->formatTimeDescription($days);
        } elseif (preg_match('/(\d+)\s*hours?\s*before/i', $deadline, $matches)) {
            $hours = (int) $matches[1];
            if ($hours >= 24) {
                $days = floor($hours / 24);
                $remainingHours = $hours % 24;
                if ($remainingHours == 0) {
                    return "入住前{$days}天";
                } else {
                    return "入住前{$days}天{$remainingHours}小时";
                }
            } else {
                return "入住前{$hours}小时";
            }
        } elseif (str_contains($deadline, 'check-in') || str_contains($deadline, '入住')) {
            return '入住当天';
        }

        // 如果无法解析，返回原文
        return $deadline;
    }

    /**
     * 检查是否可以取消
     */
    protected function checkIfCancellable(array $policies): bool
    {
        foreach ($policies as $policy) {
            if (! is_array($policy)) {
                continue;
            }

            // 检查 ChargeType - 如果是 NoCharge 则可以免费取消
            if (isset($policy['ChargeType'])) {
                if ($policy['ChargeType'] === 'NoCharge') {
                    return true;
                }
            }

            // 检查是否明确标记为不可取消
            if (isset($policy['CancellationPermitted'])) {
                return (bool) $policy['CancellationPermitted'];
            }

            // 检查描述中是否包含不可取消的关键词
            $description = strtolower($policy['Description'] ?? '');
            if (strpos($description, 'non-refundable') !== false ||
                strpos($description, 'non refundable') !== false ||
                strpos($description, 'no refund') !== false ||
                strpos($description, '不可退款') !== false ||
                strpos($description, '不退款') !== false) {
                return false;
            }
        }

        // 默认情况下，如果有取消政策但没有明确禁止，认为是可以取消的（可能收费）
        return ! empty($policies);
    }

    /**
     * 设置汇率
     */
    public function setExchangeRate(string $currency, float $rate): void
    {
        $this->exchangeRates[$currency] = $rate;
    }

    /**
     * 获取汇率
     */
    public function getExchangeRate(string $currency): float
    {
        return $this->getToCnyMultiplier($currency);
    }

    /**
     * 批量设置汇率
     */
    public function setExchangeRates(array $rates): void
    {
        $this->exchangeRates = array_merge($this->exchangeRates, $rates);
    }

    /**
     * 计算到CNY的倍率：multiplier = rateUSDPerUnit(currency) / rateUSDPerUnit(CNY)
     * 其中 rate 字段存的是“1 单位该货币 = ? USD”。
     */
    protected function getToCnyMultiplier(string $currency): float
    {
        try {
            $currency = strtoupper($currency);
            $rateCurrency = $this->getUsdPerUnit($currency);
            $rateCny = $this->getUsdPerUnit('CNY');

            if ($rateCurrency !== null && $rateCny !== null && $rateCny > 0) {
                return (float) ($rateCurrency / $rateCny);
            }
        } catch (\Throwable $e) {
            Log::warning('读取数据库汇率失败，使用兜底倍率', [
                'currency' => $currency,
                'error' => $e->getMessage(),
            ]);
        }

        return $this->exchangeRates[$currency] ?? 1.0;
    }

    /**
     * 读取数据库中的“1 单位该货币 = ? USD”。
     */
    protected function getUsdPerUnit(string $currency): ?float
    {
        $currency = strtoupper($currency);
        $cacheKey = 'exchange_rate_usd_per_unit_'.$currency;
        $rate = Cache::remember($cacheKey, now()->addDay(), function () use ($currency) {
            return ExchangeRate::query()
                ->where('currency', $currency)
                ->value('rate');
        });

        return $rate === null ? null : (float) $rate;
    }

    /**
     * 检测是否是会员价格
     * 通过检查ReferenceList中是否包含Loyalty和GHA来判断
     */
    protected function checkIfMemberRate(array $rateData): bool
    {
        // 检查Product级别的ReferenceList
        $product = $rateData['Product'] ?? [];
        $referenceList = $product['ReferenceList'] ?? [];

        // 也检查根级别的ReferenceList
        if (empty($referenceList)) {
            $referenceList = $rateData['ReferenceList'] ?? [];
        }

        if (! is_array($referenceList)) {
            return false;
        }

        // 遍历ReferenceList查找Loyalty标记
        foreach ($referenceList as $reference) {
            if (is_array($reference)) {
                $ref = $reference['Ref'] ?? '';
                $refValue = $reference['RefValue'] ?? '';

                // 检查是否是会员价格标记
                if ($ref === 'Loyalty' && $refValue === 'GHA') {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 提取每日价格数据
     */
    protected function extractDailyPrices(array $rateData, string $currency): array
    {
        $dailyPrices = [];

        // 获取每日价格数据
        $product = $rateData['Product'] ?? [];
        $prices = $product['Prices'] ?? [];
        $dailyPricesData = $prices['Daily'] ?? [];

        if (! is_array($dailyPricesData)) {
            return [];
        }
        $rateCode = Arr::get($product, 'Rate.Code');
        $roomCode = Arr::get($product, 'Room.Code');
        if ($rateCode == 'BAR' && $roomCode == '1STE') {
            // dd($dailyPricesData);
        }
        // 获取到CNY的倍率
        $exchangeRate = $this->getToCnyMultiplier($currency);

        foreach ($dailyPricesData as $dailyPriceData) {

            try {
                $price = $dailyPriceData['Price'] ?? [];
                $date = $dailyPriceData['Date'] ?? '';
                $availableInventory = $dailyPriceData['AvailableInventory'] ?? 0;

                // 解析每日价格
                $dailyPriceInfo = $this->parsePriceObject($price);

                // 转换为人民币（分）
                // 直接转换总价而不是分别转换后相加，避免精度误差
                $cnyTotalPrice = toCent($dailyPriceInfo['total'] * $exchangeRate);
                $cnyFee = toCent($dailyPriceInfo['fee'] * $exchangeRate);
                $cnyTax = toCent($dailyPriceInfo['tax'] * $exchangeRate);
                $cnyPrice = $cnyTotalPrice - $cnyFee - $cnyTax;

                $dailyPrice = new DailyPriceDTO(
                    date: date('Y-m-d', strtotime($date)),
                    currency: $dailyPriceInfo['currency'],
                    price: $dailyPriceInfo['base'] * 100,
                    fee: $dailyPriceInfo['fee'] * 100,
                    tax: $dailyPriceInfo['tax'] * 100,
                    total_price: $dailyPriceInfo['total'] * 100,
                    cny_price: $cnyPrice,
                    cny_fee: $cnyFee,
                    cny_tax: $cnyTax,
                    cny_total_price: $cnyTotalPrice,
                    availableInventory: (int) $availableInventory
                );

                $dailyPrices[] = $dailyPrice;

            } catch (\Exception $e) {
                Log::warning('Error parsing daily price data', [
                    'error' => $e->getMessage(),
                    'daily_price_data' => $dailyPriceData,
                ]);

                continue;
            }
        }

        return $dailyPrices;
    }
}
