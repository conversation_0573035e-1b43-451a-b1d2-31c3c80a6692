<?php

namespace App\Services;

use Elasticsearch\ClientBuilder;

class ElasticsearchService
{
    public static function client()
    {
        $hosts = config('elasticsearch.hosts');

        return ClientBuilder::create()->setHosts($hosts)->build();
    }

    protected $client;

    protected $index = '.hotel';

    protected $type = 'doc';

    public function __construct($index)
    {
        $this->index = $index ? '.'.$index : '.hotel';
        $this->client = ElasticsearchService::client();
    }

    /**
     * 多字段搜索酒店
     *
     * @param  array  $filters
     *                          [
     *                          'keyword'    => '南京 希尔顿',
     *                          'brand_name' => '希尔顿',
     *                          'city_code'  => 'NKG',
     *                          'page'       => 1,
     *                          'size'       => 20,
     *                          ]
     */
    public function searchHotel(array $filters = [])
    {
        $page = $filters['page'] ?? 1;
        $size = $filters['page_size'] ?? 20;

        $must = [];   // AND 条件
        $should = []; // OR 条件（keyword 用）

        // ===== 关键字搜索 (OR 匹配) =====
        if (! empty($filters['keyword'])) {
            $keyword = trim($filters['keyword']);
            $fields = [
                'hotel_name',
                'hotel_name_en',
                'brand_name',
                'brand_nameEn',
                'address',
                'address_en',
            ];

            // 普通字段 wildcard 匹配
            $keywordShould = [];
            foreach ($fields as $field) {
                $keywordShould[] = ['wildcard' => [$field => "*{$keyword}*"]];
            }

            // nested 字段模糊匹配
            $nestedFields = [
                'city_code' => ['name', 'name_en'],
                'destinations' => ['name', 'name_en'],
                'interests' => ['name', 'name_en'],
                'eco_certificates' => ['name', 'name_en'],
                'neighborhood_tag' => ['name', 'name_en'],
                'hotel_type' => ['name', 'name_en'],
                'feature' => ['name', 'name_en'],
                'categories' => ['name', 'name_en'],
            ];

            foreach ($nestedFields as $path => $nestedSubFields) {
                $nestedShould = [];
                foreach ($nestedSubFields as $subField) {
                    $nestedShould[] = ['wildcard' => ["{$path}.{$subField}" => "*{$keyword}*"]];
                }
                $keywordShould[] = [
                    'nested' => [
                        'path' => $path,
                        'query' => [
                            'bool' => [
                                'should' => $nestedShould,
                                'minimum_should_match' => 1,
                            ],
                        ],
                    ],
                ];
            }

            $should[] = [
                'bool' => [
                    'should' => $keywordShould,
                    'minimum_should_match' => 1,
                ],
            ];
        }

        // ===== 其他筛选条件 (保持原逻辑) =====
        $nestedFilters = [
            'hotel_type' => ['id', 'name', 'name_en'],
            'brands' => ['brand_nameEn'],
            'categories' => ['id', 'name', 'name_en'],
            'feature' => ['id', 'name', 'name_en'],
        ];

        foreach ($nestedFilters as $key => $fields) {
            if (empty($filters[$key])) {
                continue;
            }

            $values = is_array($filters[$key]) ? $filters[$key] : explode(',', $filters[$key]);
            $queries = [];

            foreach ($values as $value) {
                $fieldQueries = [];
                if ($key === 'brands') {
                    $fieldQueries[] = ['match_phrase' => ['brand_nameEn' => $value]];
                    $queries[] = ['bool' => ['should' => $fieldQueries, 'minimum_should_match' => 1]];
                } else {
                    $nestedPath = $key;
                    if (is_numeric($value)) {
                        $queries[] = [
                            'nested' => [
                                'path' => $nestedPath,
                                'query' => ['term' => ["{$nestedPath}.id" => (int) $value]],
                            ],
                        ];
                    } else {
                        foreach ($fields as $field) {
                            if ($field === 'id') {
                                continue;
                            }
                            $fieldQueries[] = ['term' => ["{$nestedPath}.{$field}" => $value]];
                        }
                        $queries[] = [
                            'nested' => [
                                'path' => $nestedPath,
                                'query' => [
                                    'bool' => [
                                        'should' => $fieldQueries,
                                        'minimum_should_match' => 1,
                                    ],
                                ],
                            ],
                        ];
                    }
                }
            }

            if (! empty($queries)) {
                $must[] = [
                    'bool' => [
                        'should' => $queries,
                        'minimum_should_match' => 1,
                    ],
                ];
            }
        }

        // ===== 单值普通字段筛选 =====
        $singleFilters = [
            'hotelCode' => 'code',
            'brand_name' => 'brand_name',
            'brand_code' => 'brand_code',
            'hotel_name' => 'hotel_name',
            'country_code' => 'country_code',
            'new_hotel' => 'new_hotel',
            'city_code' => 'city_code',
        ];

        foreach ($singleFilters as $filterKey => $fieldName) {
            if (empty($filters[$filterKey])) {
                continue;
            }

            if ($filterKey === 'new_hotel') {
                $must[] = ['term' => [$fieldName => (bool) $filters[$filterKey]]];
            } elseif ($filterKey === 'city_code') {
                $values = is_array($filters[$filterKey]) ? $filters[$filterKey] : explode(',', $filters[$filterKey]);
                $queries = [];
                foreach ($values as $val) {
                    if (is_numeric($val)) {
                        $queries[] = [
                            'nested' => [
                                'path' => 'city_code',
                                'query' => ['term' => ['city_code.id' => (int) $val]],
                            ],
                        ];
                    } else {
                        $queries[] = [
                            'nested' => [
                                'path' => 'city_code',
                                'query' => [
                                    'bool' => [
                                        'should' => [
                                            ['term' => ['city_code.name' => $val]],
                                            ['term' => ['city_code.name_en' => $val]],
                                        ],
                                        'minimum_should_match' => 1,
                                    ],
                                ],
                            ],
                        ];
                    }
                }
                $must[] = [
                    'bool' => [
                        'should' => $queries,
                        'minimum_should_match' => 1,
                    ],
                ];
            } else {
                $must[] = ['match' => [$fieldName => $filters[$filterKey]]];
            }
        }

        // ===== 构建最终查询 =====
        $query = ['bool' => []];
        if (! empty($must)) {
            $query['bool']['must'] = $must;
        }
        if (! empty($should)) {
            $query['bool']['should'] = $should;
            $query['bool']['minimum_should_match'] = 1;
        }
        if (empty($must) && empty($should)) {
            $query = ['match_all' => (object) []];
        }
        $params = [
            'index' => $this->index,
            'body' => [
                'from' => ($page - 1) * $size,
                'size' => $size,
                'query' => $query,
                'highlight' => [
                    'fields' => [
                        'hotel_name' => new \stdClass,
                        'hotel_name_en' => new \stdClass,
                        'brand_name' => new \stdClass,
                        'brand_nameEn' => new \stdClass,
                        'address' => new \stdClass,
                        'address_en' => new \stdClass,
                    ],
                ],
                'sort' => ! empty($filters['sort'])
                    ? $filters['sort']
                    : ['_score' => ['order' => 'desc']],
            ],
        ];

        try {
            $result = $this->client->search($params);

            return [
                'total' => $result['hits']['total']['value'] ?? $result['hits']['total'],
                'data' => array_map(function ($item) {
                    $source = $item['_source'];
                    if (isset($item['highlight'])) {
                        $source['highlight'] = $item['highlight'];
                    }

                    return $source;
                }, $result['hits']['hits']),
            ];
        } catch (\Exception $e) {
            error_log('Elasticsearch search error: '.$e->getMessage());

            return ['total' => 0, 'data' => []];
        }
    }

    /**
     * 获取品牌总数
     *
     * @return int|mixed
     */
    public function countHotelsByBrand(string $brandName)
    {
        $params = [
            'index' => $this->index,
            'body' => [
                'query' => [
                    'match' => [
                        'brand_name' => $brandName,
                    ],
                ],
            ],
        ];

        $result = $this->client->count($params);

        return $result['count'] ?? 0;
    }

    /**
     * 根据酒店代码、品牌代码和房间代码查询房间信息
     *
     * @param  string|null  $hotelCode  酒店代码
     * @param  string|null  $brandCode  品牌代码
     * @param  string|null  $roomCode  房间代码
     * @param  int  $from  起始位置
     * @param  int  $size  返回数量
     * @return array
     */
    public function searchRooms($hotelCode = null, $brandCode = null, $roomCode = null, $from = 0, $size = 10)
    {
        $params = [
            'index' => '.room',
            'body' => [
                'from' => $from,
                'size' => $size,
                'query' => [
                    'bool' => [
                        'must' => [],
                    ],
                ],
            ],
        ];

        // 添加过滤条件
        if (! empty($hotelCode)) {
            $params['body']['query']['bool']['must'][] = [
                'term' => ['hotelcode' => $hotelCode],
            ];
        }

        if (! empty($brandCode)) {
            $params['body']['query']['bool']['must'][] = [
                'term' => ['brandCode' => $brandCode],
            ];
        }

        if (! empty($roomCode)) {
            $params['body']['query']['bool']['must'][] = [
                'term' => ['roomcode' => $roomCode],
            ];
        }

        // 如果没有查询条件，返回所有文档
        if (empty($params['body']['query']['bool']['must'])) {
            $params['body']['query'] = ['match_all' => new \stdClass];
        }

        try {
            $response = $this->client->search($params);

            return $response['hits']['hits'] ?? [];
        } catch (\Exception $e) {
            throw new \Exception('搜索失败: '.$e->getMessage());
        }
    }

    /**
     * 优惠活动搜索
     *
     * @return array|mixed
     *
     * @throws \Exception
     */
    public function searchOffers(array $filters = [], array $additionalFilters = [])
    {
        $page = $filters['page'] ?? 1;
        $size = $filters['page_size'] ?? 20;

        $params = [
            'index' => '.offers',
            'body' => [
                'from' => ($page - 1) * $size,
                'size' => $size,
                'sort' => [
                    ['updated_at' => 'desc'],
                    ['created_at' => 'desc'],
                ],
                'query' => [
                    'bool' => [
                        'must' => [],
                        'filter' => [],
                    ],
                ],
            ],
        ];

        // ====== 酒店过滤 ======
        if (! empty($filters['hotels'])) {
            if (is_string($filters['hotels'])) {
                $filters['hotels'] = [$filters['hotels']];
            }
            if (is_array($filters['hotels']) && ! empty($filters['hotels'])) {
                $params['body']['query']['bool']['must'][] = [
                    'terms' => [
                        'hotels' => $filters['hotels'],
                    ],
                ];
            }
        }

        // ====== 关键词搜索 (title, title_cn) ======
        if (! empty($filters['keyword'])) {
            $params['body']['query']['bool']['must'][] = [
                'query_string' => [
                    'fields' => ['title', 'title_cn'],
                    'query' => '*'.$filters['keyword'].'*', // 包含搜索
                ],
            ];
        }

        // ====== offer_types 搜索 (数组字段) ======
        if (! empty($filters['offer_type'])) {
            if (is_array($filters['offer_type'])) {
                $params['body']['query']['bool']['must'][] = [
                    'terms' => [
                        'offer_types' => $filters['offer_type'],
                    ],
                ];
            } else {
                $params['body']['query']['bool']['must'][] = [
                    'match' => [
                        'offer_types' => $filters['offer_type'],
                    ],
                ];
            }
        }

        // ====== 日期范围过滤 ======
        $dateRangeFilter = [];
        if (! empty($filters['startDate'])) {
            $dateRangeFilter['gte'] = $filters['startDate'];
        }
        if (! empty($filters['endDate'])) {
            $dateRangeFilter['lte'] = $filters['endDate'];
        }

        if (! empty($dateRangeFilter)) {
            $params['body']['query']['bool']['filter'][] = [
                'range' => [
                    'start_date' => $dateRangeFilter,
                ],
            ];
            $params['body']['query']['bool']['filter'][] = [
                'range' => [
                    'end_date' => $dateRangeFilter,
                ],
            ];
        }

        // ====== 额外过滤条件 ======
        if (! empty($additionalFilters)) {
            foreach ($additionalFilters as $field => $value) {
                if (! empty($value)) {
                    $params['body']['query']['bool']['filter'][] = [
                        'term' => [$field => $value],
                    ];
                }
            }
        }

        // ====== 默认过滤条件（未过期优惠） ======
        if (empty($params['body']['query']['bool']['must']) && empty($params['body']['query']['bool']['filter'])) {
            $params['body']['query'] = [
                'range' => [
                    'end_date' => [
                        'gte' => now()->toDateString(),
                    ],
                ],
            ];
        }
        try {
            $response = $this->client->search($params);

            return [
                'total' => $response['hits']['total']['value'] ?? $response['hits']['total'],
                'data' => array_map(function ($item) {
                    $source = $item['_source'];
                    if (isset($item['highlight'])) {
                        $source['highlight'] = $item['highlight'];
                    }

                    return $source;
                }, $response['hits']['hits']),
            ];
        } catch (\Exception $e) {
            throw new \Exception('搜索优惠信息失败: '.$e->getMessage());
        }
    }
}
