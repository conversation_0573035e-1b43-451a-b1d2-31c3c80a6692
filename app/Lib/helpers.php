<?php

use Illuminate\Support\Facades\Log;

if (! function_exists('successResponse')) {
    /**
     * 返回成功
     */
    function successResponse(mixed $data = null, string $message = 'Success', int $status = 200): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
            'status_code' => $status,
        ], $status);
    }
}

if (! function_exists('errorResponse')) {
    /**
     * 返回失败
     */
    function errorResponse(string $message = 'Error', int $status = 400, mixed $data = null): \Illuminate\Http\JsonResponse
    {
        // 校验状态码是否合法
        if ($status < 100 || $status >= 600) {
            $status = 500;
        }

        return response()->json([
            'success' => false,
            'data' => $data,
            'message' => $message,
            'status_code' => $status,
        ], 200);
    }
}

if (! function_exists('encodeForDatabase')) {
    function encodeForDatabase($data, bool $throwException = false)
    {
        // 处理空值
        if (blank($data)) {
            return null;
        }
        // 如果已经是JSON字符串且有效，直接返回
        if (is_string($data) && isValidJson($data)) {
            return $data;
        }
        try {
            // 编码为JSON，不转义斜杠和Unicode字符
            $json = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            if ($json === false) {
                throw new \RuntimeException('JSON encoding failed: '.json_last_error_msg());
            }

            return $json;
        } catch (\Exception $e) {
            Log::error('JSON编码失败', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            if ($throwException) {
                throw new \InvalidArgumentException('JSON编码失败: '.$e->getMessage());
            }

            return null;
        }
    }
}
if (! function_exists('isValidJson')) {
    function isValidJson(?string $json): bool
    {
        if (! is_string($json) || trim($json) === '') {
            return false;
        }
        json_decode($json);

        return json_last_error() === JSON_ERROR_NONE;
    }
}

if (! function_exists('hexToRgba')) {
    function hexToRgba($hex, $alpha = 1)
    {
        // 移除 # 號
        $hex = str_replace('#', '', $hex);

        // 處理縮寫形式如 #abc
        if (strlen($hex) == 3) {
            $hex = $hex[0].$hex[0].$hex[1].$hex[1].$hex[2].$hex[2];
        }

        // 驗證是否為有效的6位十六進制色值
        if (strlen($hex) != 6) {
            return '';
        }

        // 解析RGB值
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        // 確保alpha值在0-1之間
        $alpha = max(0, min(1, $alpha));

        // 返回RGBA字符串
        return "rgba($r, $g, $b, $alpha)";
    }
}

if (! function_exists('AuthCheck')) {
    function AuthCheck()
    {
        if (auth('sanctum')->check()) {
            return true;
        } elseif (auth('api')->check()) {
            return true;
        } elseif (auth()->check()) {
            return auth()->check();
        }
    }
}

if (! function_exists('AuthUser')) {
    function AuthUser()
    {
        $user = null;
        if (auth('sanctum')->check()) {
            $user = auth('sanctum')->user();
        } elseif (auth('api')->check()) {
            $user = auth('api')->user();
        } elseif (auth()->check()) {
            $user = auth()->user();
        }
        if (! $user) {
            auth()->logout();
        }

        return $user;
    }

}

if (! function_exists('toCent')) {
    /**
     * 将金额（元）转换为整数（分）
     *
     * @param  mixed  $value
     */
    function toCent($value): int
    {
        // 处理 null / string / int / float 等情况
        $clean = str_replace(',', '', (string) $value); // 可处理 "1,234.56"

        return (int) bcmul($clean, '100', 0);
    }
}

if (! function_exists('fromCent')) {
    /**
     * 将整数（分）转换为金额显示字符串
     *
     * @param  string  $symbol  货币符号
     * @param  int  $decimals  小数位数
     * @param  string  $decimalPoint  小数点符号
     * @param  string  $thousandsSep  千分位符号
     */
    function fromCent(int $cents, string $symbol = '¥', int $decimals = 2, string $decimalPoint = '.', string $thousandsSep = ','): string
    {
        // 使用BC数学函数避免浮点精度问题
        $amount = bcdiv((string) $cents, '100', $decimals);

        return $symbol.number_format((float) $amount, $decimals, $decimalPoint, $thousandsSep);
    }
}

if (! function_exists('validateChildrenAge')) {
    function validateChildrenAge(int $childrenCount, string $childrenAge, int $numRooms): array
    {
        // 按房间分割年龄字符串
        $roomAges = explode(',', $childrenAge);

        $totalAgeCount = 0;
        foreach ($roomAges as $roomIndex => $roomAgeStr) {
            if (empty($roomAgeStr)) {
                continue;
            }

            $ages = explode(',', $roomAgeStr);
            foreach ($ages as $age) {
                $age = trim($age);

                // 验证年龄是否为数字
                if (! is_numeric($age)) {
                    return [
                        'valid' => false,
                        'message' => "无效的年龄格式: {$age}",
                    ];
                }

                $ageInt = (int) $age;

                // 验证年龄范围
                if ($ageInt < 0 || $ageInt > 17) {
                    return [
                        'valid' => false,
                        'message' => "儿童年龄必须在0-17岁之间，当前: {$ageInt}岁",
                    ];
                }

                $totalAgeCount++;
            }
        }

        // 验证总年龄数量是否与儿童数量匹配
        if ($totalAgeCount != $childrenCount) {
            return [
                'valid' => false,
                'message' => "儿童数量({$childrenCount})与提供的年龄数量({$totalAgeCount})不匹配",
            ];
        }

        return ['valid' => true, 'message' => ''];
    }
}

if (! function_exists('shouldLogQuery')) {
    function shouldLogQuery(): bool
    {
        return config('sabre.logging.enabled', false) &&
            config('sabre.logging.log_queries', true);
    }
}

// 获取两个日期的住宿天数
if (! function_exists('getDaysBetween')) {
    function getDaysBetween($date1, $date2)
    {
        $date1 = new DateTime($date1);
        $date2 = new DateTime($date2);
        $interval = $date1->diff($date2);

        return $interval->days;
    }
}

if (! function_exists('getCardType')) {
    /**
     * 根据信用卡号获取卡类型
     *
     * @param  array|null  $customPatterns  可选，自定义卡种正则 ['卡种名' => '正则表达式']
     * @return string|null 返回卡类型，如 'Visa', 'MasterCard', 'Amex'，找不到返回 null
     */
    function getCardType(string $cardNumber, ?array $customPatterns = null): ?string
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber); // 去掉非数字字符

        // 默认卡种正则对应简称
        $defaultPatterns = [
            'VI' => '/^4[0-9]{12}(?:[0-9]{3})?$/',                  // Visa
            'MC' => '/^5[1-5][0-9]{14}$/',                          // MasterCard
            'AMEX' => '/^3[47][0-9]{13}$/',                           // American Express
            'DI' => '/^6(?:011|5[0-9]{2})[0-9]{12}$/',              // Discover
            'JCB' => '/^(?:2131|1800|35\d{3})\d{11}$/',              // JCB
            'DC' => '/^3(?:0[0-5]|[68][0-9])[0-9]{11}$/',           // Diners Club
            'UP' => '/^62[0-9]{14,17}$/',                           // UnionPay
            'MA' => '/^(?:5[06789]|6)[0-9]{10,17}$/',               // Maestro
            'MIR' => '/^220[0-4][0-9]{12}$/',                        // Mir
        ];

        $patterns = $customPatterns ?? $defaultPatterns;

        foreach ($patterns as $type => $pattern) {
            if (preg_match($pattern, $cardNumber)) {
                return $type;
            }
        }

        return null; // 未知卡类型
    }
}

if (! function_exists('getMonthsBetween')) {
    /**
     * 获取状态返回
     *
     * @return string|void
     */
    function getReservationStatus($status)
    {
        // 转换大写
        $status = strtoupper($status);
        switch ($status) {
            case 'CONFIRMED':
                return '已确认';
                break;
            case 'NO_SHOW':
                return '不显示';
                break;
            case 'CHECKED_IN':
                return '已入住';
                break;
            case 'CHECKED_OUT':
                return '已退房';
                break;
            case 'CURRENT':
                return '当前';
                break;
            case 'UPCOMING':
                return '即将来临';
                break;
            case 'PAST':
                return '过期';
                break;
            case 'CANCELED':
                return '已取消';
                break;
        }
    }
}

if (! function_exists('getFullSql')) {
    /**
     * 获取 Query Builder 或 Eloquent 的完整 SQL
     *
     * @param  \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder  $query
     * @return string
     */
    function getFullSql($query)
    {
        $sql = $query->toSql(); // 带占位符的 SQL
        $bindings = $query->getBindings(); // 绑定参数

        // 将绑定参数安全地替换到 SQL
        foreach ($bindings as $binding) {
            // 如果是字符串，自动加引号
            $value = is_numeric($binding) ? $binding : "'{$binding}'";
            $sql = preg_replace('/\?/', $value, $sql, 1);
        }

        return $sql;
    }
}
