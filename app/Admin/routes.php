<?php

use App\Admin\Controllers\ActivityController;
use App\Admin\Controllers\ActivityTypeController;
use App\Admin\Controllers\BannerController;
use App\Admin\Controllers\BannerTypeController;
use App\Admin\Controllers\BrandController;
use App\Admin\Controllers\BrandImageController;
use App\Admin\Controllers\CityController;
use App\Admin\Controllers\ConfigController;
use App\Admin\Controllers\ContinentController;
use App\Admin\Controllers\CountryController;
use App\Admin\Controllers\ErrorController;
use App\Admin\Controllers\HotController;
use App\Admin\Controllers\HotelController;
use App\Admin\Controllers\HotelDiningController;
use App\Admin\Controllers\HotelEcoController;
use App\Admin\Controllers\HotelImageController;
use App\Admin\Controllers\HotelInterestController;
use App\Admin\Controllers\HotelLoopController;
use App\Admin\Controllers\HotelNeighborhoodController;
use App\Admin\Controllers\HotelOfferController;
use App\Admin\Controllers\HotelOffersTypeController;
use App\Admin\Controllers\HotelRomController;
use App\Admin\Controllers\HotelTypeController;
use App\Admin\Controllers\LinkController;
use App\Admin\Controllers\LinkTypeController;
use App\Admin\Controllers\NewsController;
use App\Admin\Controllers\ResvBaseController;
use App\Admin\Controllers\RoomController;
use App\Admin\Controllers\RoomImageController;
use App\Admin\Controllers\SearchImageController;
use App\Admin\Controllers\UserCollectController;
use App\Admin\Controllers\UserPreferencesCategoryController;
use App\Admin\Controllers\UserPreferencesConfigController;
use App\Admin\Controllers\WechatHighlightController;
use App\Admin\Controllers\WellnessController;
use Dcat\Admin\Admin;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Admin::routes();

Route::group([
    'prefix' => config('admin.route.prefix'),
    'namespace' => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');
    $router->resource('hotel', HotelController::class);
    $router->resource('room', RoomController::class);
    $router->resource('brand', BrandController::class);
    $router->resource('brand_image', BrandImageController::class);
    $router->resource('hotel_image', HotelImageController::class);
    $router->resource('room_image', RoomImageController::class);
    $router->resource('search_image', SearchImageController::class);
    $router->resource('activity/type', ActivityTypeController::class);
    $router->resource('activity/list', ActivityController::class);
    $router->resource('banner', BannerController::class);
    $router->resource('hotel_dining', HotelDiningController::class);
    $router->resource('hotel_offer', HotelOfferController::class);
    $router->resource('banner_type', BannerTypeController::class);
    $router->resource('banner', BannerController::class);
    $router->resource('continent', ContinentController::class);
    $router->resource('hotel-type', HotelTypeController::class);
    $router->resource('hotel-loop', HotelLoopController::class);
    $router->resource('hotel-rom', HotelRomController::class);
    $router->resource('country', CountryController::class);
    $router->resource('city', CityController::class);
    $router->resource('link-type', LinkTypeController::class);
    $router->resource('link', LinkController::class);
    $router->resource('config', ConfigController::class);
    $router->resource('user-collect', UserCollectController::class);
    $router->resource('news', NewsController::class);
    $router->resource('wellness', WellnessController::class);
    $router->resource('hotel-offers-type', HotelOffersTypeController::class);
    $router->resource('hotel-interest', HotelInterestController::class);
    $router->resource('hotel-eco', HotelEcoController::class);
    $router->resource('hotel-neighborhood', HotelNeighborhoodController::class);
    $router->resource('wechat-highlight', WechatHighlightController::class);
    $router->resource('error', ErrorController::class);
    $router->resource('user-preferences-category', UserPreferencesCategoryController::class);
    $router->resource('user-preferences-config', UserPreferencesConfigController::class);
    $router->resource('resv-base', ResvBaseController::class);
    Route::match(['post', 'put', 'patch'], 'hot/save', [HotController::class, 'save']);
    $router->resource('hot', HotController::class);
    $router->get('activity/miniOptions', [ActivityController::class, 'miniOptions']);
});
