<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Tools\RoomOnLinePost;
use App\Models\Room;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class RoomController extends AdminController
{
    public function __construct()
    {
        $this->title = '房间管理';
    }

    protected function grid()
    {
        return Grid::make(new Room, function (Grid $grid) {
            $hotelId = request()->query('hotel_id');
            $grid->model()->where('hotelcode', $hotelId);
            $grid->column('code', '房型Code');
            $grid->column('room_name', '房型名');
            $grid->column('room_name_en', '房型英文名');
            //            $grid->column('booker_text', 'booker_text');
            $grid->column('room_desc', '中文描述')->display(function ($content) {
                return htmlspecialchars_decode($content, ENT_QUOTES); // 注意：laravel-admin 默认不转义此内容，前提是信任数据源
            });
            $grid->column('room_desc_en', '英文描述')->display(function ($content) {
                return htmlspecialchars_decode($content, ENT_QUOTES); // 注意：laravel-admin 默认不转义此内容，前提是信任数据源
            });
            $grid->column('is_online', '状态')->display(function ($val) {
                if ($val == Room::ON) {
                    return "<span style='color: green'>已上线</span>";
                } elseif ($val == Room::OFF) {
                    return "<span style='color: red'>已下线</span>";
                } else {
                    return '';
                }
            });
            //            $grid->actions(function (Grid\Displayers\Actions $actions)use($hotelId) {
            // 添加查看关联列表的按钮
            //                $actions->append('<a href="' . admin_url('room_image?room_id=' . $actions->row->roomcode."&hotel_id=".$hotelId) . '" class="" title="图片管理"><i class="feather icon-image"></i> 图片管理</a>');
            //            });
            $grid->tools(new RoomOnLinePost('批量上线', Room::ON, 'btn-success'));
            $grid->tools(new RoomOnLinePost('批量下线', Room::OFF, 'btn-danger'));

            // 传入字符串
            $grid->tools('<a class="btn  pull-right" style="margin-left: 10px;" href="'.admin_url('hotel').'">返回</a>');
            $createUrl = admin_url('room/create?hotel_id='.$hotelId);
            $grid->tools('<a class="btn btn-primary grid-refresh btn-mini  pull-right" style="margin-left: 10px;" href="'.$createUrl.'"><i class="feather icon-plus"></i>新增</a>');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableBatchDelete();
            //            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->tableCollapse(false);
            $grid->withBorder();
        });
    }

    protected function detail($id) {}

    protected function form()
    {
        return Form::make(new Room, function (Form $form) {
            $hotelId = request()->query('hotel_id');
            $form->text('room_name', '房型中文名');
            $form->text('room_name_en', '房型英文名');
            $form->editor('booker_text', '预定描述');
            $form->editor('booker_text_en', '预定英文描述');
            $form->editor('room_desc', '房型中文描述');
            $form->editor('room_desc_en', '房型英文描述');
            $form->hidden('hotelcode')->default($hotelId);
            $form->disableResetButton();
            $form->disableViewCheck();
            $form->disableCreatingCheck();
            $form->disableEditingCheck();
            $form->disableViewButton();
            $form->disableDeleteButton();
            Admin::script('
                 $(document).ready(function () {
                    // 列表
                    var url =  $(".btn.btn-sm.btn-primary").attr("href");
                    url = url + "?hotel_id=" +$(".field_hotelcode").val();
                    $(".btn.btn-sm.btn-primary").attr("href",url);
                });
            ');
        });
    }
}
