<?php

namespace App\Admin\Controllers;

use App\Models\Activity;
use App\Models\ActivityType;
use App\Models\Hotel;
use App\Models\HotelBrand;
use App\Models\HotelOffer;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Models\Administrator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ActivityController extends AdminController
{
    public function __construct()
    {
        $this->title = '活动管理';
    }

    protected function grid()
    {
        return Grid::make(new Activity, function (Grid $grid) {
            $grid->column('activity_type', '活动类型')->display(function ($val) {
                return ActivityType::where('id', $val)->value('name');
            });
            $grid->column('name', '活动名称');
            $grid->column('user_id', '操作人')->display(function ($val) {
                return Administrator::where('id', $val)->value('name');
            });
            $grid->column('status', '是否上线')->switch();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableBatchDelete();
            $grid->disableRowSelector();
            $grid->tableCollapse(false);
            $grid->withBorder();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '活动名称');
                $filter->like('name_en', '类型英文名称');
                $filter->equal('user_id', '操作人')->select(ActivityType::pluck('name', 'id')->toArray());
                $filter->equal('status', '是否上线')->select([Activity::STATUS_ON => '上线', Activity::STATUS_OFF => '下线']);
                $filter->equal('activity_type', '活动类型')->select(ActivityType::where('status', Activity::STATUS_ON)->pluck('name', 'id')->toArray());
            });
        });
    }

    protected function detail($id) {}

    protected function form()
    {
        return Form::make(new Activity, function (Form $form) {
            $form->select('activity_type', '活动类型')->options(ActivityType::where('status', Activity::STATUS_ON)->pluck('name', 'id')->toArray())->required();
            $form->text('hotel_name', '酒店/品牌名称');
            $form->text('hotel_url', '酒店/品牌链接');
            $form->text('tag_name', '标签名称');
            $form->text('name', '活动名称')->required();
            $form->text('name_en', '活动英文名称');
            $form->textarea('description', '活动描述');
            $form->select('line_type', '链接类型')->options(Activity::LINE_TYPE_URL);
            $form->text('url', '链接地址');
            $form->text('button_text', '按钮名称');
            $form->radio('mini_switch', '小程序是否使用')->options([
                0 => '不使用',
                1 => '使用',
            ])
                ->when(1, function ($switchField) use ($form) {
                    // 使用 $form 添加其他字段
                    $form->select('mini_type', '小程序跳转类型')
                        ->options(['offers' => '优惠活动', 'hotel' => '酒店', 'brand' => '品牌'])
                        ->load('mini_id', admin_url('activity/miniOptions'));
                    $form->select('mini_id', '小程序跳转内容')
                        ->placeholder('请选择');
                })->default(0);

            $form->image('img_url', '简体图片')->uniqueName()->accept('jpg,png,gif,jpeg,svg')
                ->disk('public')
                ->dir('images')
                ->retainable() // 确保这个存在
                ->removable() // 允许移除
                ->autoUpload()
                ->options([
                    'initialPreviewAsData' => true,
                    'overwriteInitial' => false,
                    'showRemove' => true,
                    'showUpload' => false,
                ]);
            //            $form->image('img_url_f', '繁体图片')->uniqueName()->accept('jpg,png,gif,jpeg,svg')
            //                ->disk('public')
            //                ->dir('images')
            //                ->retainable() // 确保这个存在
            //                ->removable() // 允许移除
            //                ->autoUpload()
            //                ->options([
            //                    'initialPreviewAsData' => true,
            //                    'overwriteInitial' => false,
            //                    'showRemove' => true,
            //                    'showUpload' => false,
            //                ]);
            $form->text('address', '地理位置');
            $form->text('key_words', '网页关键词');
            $form->textarea('web_desc', '网页描述');
            $form->text('page_title', '页面标题');
            $form->url('activity_link', 'activityLink');
            $form->text('rate', '特殊价格码rate');
            $form->text('access_code', 'AccessCode');
            $form->datetime('date', '发布时间');
            $form->editor('content', 'PC版活动内容');
            $form->switch('is_show', '是否在页面展示文字信息')->default(1);
            $form->switch('status', '是否上线')->default(1);
            $form->number('order', '排序');

            $form->disableResetButton();
            $form->disableViewCheck();
            $form->disableCreatingCheck();
            $form->disableEditingCheck();
            $form->disableViewButton();
            $form->disableDeleteButton();
            $form->saved(function (Form $form) {
                Activity::where('id', $form->getKey())->update(['user_id' => Admin::user()->id]);
            });
        });
    }

    public function miniOptions(Request $request)
    {
        $type = $request->get('q'); // mini_type 的值 (offers/hotel/brand)
        $options = [];
        switch ($type) {
            case 'offers':
                $options = HotelOffer::query()->get(['id', DB::raw('title as text')]);
                break;
            case 'hotel':
                $options = Hotel::query()->get(['id', DB::raw('hotel_name as text')]);
                break;
            case 'brand':
                $options = HotelBrand::query()->select(['id', 'name'])->get();
                foreach ($options as &$option) {
                    $option['text'] = $option['name'];
                }
                break;
        }

        return response()->json($options);
    }
}
