<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Tools\OnLinePost;
use App\Models\City;
use App\Models\Hotel;
use App\Models\HotelBrand;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class HotelController extends AdminController
{
    public function __construct()
    {
        $this->title = '酒店管理';
    }

    protected function grid()
    {
        return Grid::make(new Hotel, function (Grid $grid) {
            $grid->column('hotel_name', '酒店中文名');
            $grid->column('hotel_name_en', '酒店英文名');
            $grid->column('brand_code', '品牌')->display(function ($val) {
                return HotelBrand::where('brand_code', $val)->value('name');
            });
            $grid->column('city_name', '城市中文名');
            $grid->column('city_code_en', '城市英文名');
            $grid->column('country_name', '国家中文名');
            $grid->column('country_code_en', '国家英文名');
            $grid->column('online_status', '状态')->display(function ($val) {
                if ($val == Hotel::ON) {
                    return "<span style='color: green'>已上线</span>";
                } elseif ($val == Hotel::ADD) {
                    return "<span style='color>新增</span>";
                } elseif ($val == Hotel::OFF) {
                    return "<span style='color: red'>已下线</span>";
                } else {
                    return '';
                }
            });
            $grid->column('updated_at', '更新时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('hotelcode', '酒店代码');
                $filter->like('hotel_name', '酒店中文名');
                $filter->like('hotel_name_en', '酒店英文名');
                $filter->equal('online_status', '状态')->select([Hotel::ON => '已上线', Hotel::OFF => '已下线', Hotel::ADD => '新增']);
                $filter->equal('brand_code', '品牌')->select(HotelBrand::pluck('name', 'brand_code')->toArray());
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 添加查看关联列表的按钮
                $actions->append('<a href="'.admin_url('room?hotel_id='.$actions->row->code).'"  title="房型列表"><i class="feather icon-list"></i> 房型管理</a>');
                //                $actions->append('<a href="' . admin_url('hotel_image?hotel_id=' . $actions->row->hotelcode) . '"  title="图片管理"><i class="feather icon-image"></i> 图片管理</a>');
            });
            $grid->tools(new OnLinePost('批量上线', Hotel::ON, 'btn-success'));
            $grid->tools(new OnLinePost('批量下线', Hotel::OFF, 'btn-danger'));
            //            $grid->tools(function (Grid\Tools $tools) {
            //                $tools->append('<a href="'.admin_url('hotel/update-es').'" class="btn btn-primary">
            //                            <i class="feather icon-refresh-cw"></i> 同步到ES
            //                        </a>');
            //            });
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableBatchDelete();
            //            $grid->disableRowSelector();
            //            $grid->disableCreateButton();
            $grid->tableCollapse(false);
            $grid->withBorder();
        });
    }

    protected function detail($id) {}

    public function syncEs()
    {
        try {
            \Artisan::call('es:import-hotal');

            return \Dcat\Admin\Admin::json()->success('酒店数据已成功同步到 Elasticsearch');
        } catch (\Exception $e) {
            return \Dcat\Admin\Admin::json()->error('同步失败: '.$e->getMessage());
        }
    }

    protected function form()
    {
        return Form::make(new Hotel, function (Form $form) {
            $form->select('brand_code', '酒店品牌')->options(HotelBrand::pluck('name', 'brand_code')->toArray());
            $form->text('hotel_name', '酒店中文名');
            $form->text('hotel_name_en', '酒店英文名');
            $form->text('hotel_name_pinyin', '酒店拼音');
            $form->text('hotel_link', '酒店链接');
            $form->text('country_code', '国家代码');
            $form->multipleSelect('city_code', '城市代码')
                ->options(City::pluck('city_name', 'original_id'))
                ->saving(function ($value) {
                    // 这里 $value 是选择的 ID 数组，例如 [720, 721]
                    // 转换成你要存的 JSON
                    return collect($value)->map(function ($id) {
                        $city = City::where('original_id', $id)->first();

                        return [
                            'id' => $id,
                            'name' => $city ? $city->city_name_en : null,
                        ];
                    })->toArray();
                })
                ->customFormat(function ($value) {
                    if (! is_array($value)) {
                        return [];
                    }
                    if (isset($value['id'])) {
                        return [$value['id']];
                    }

                    return collect($value)->pluck('id')->toArray();
                });

            $form->textarea('brief_en', '简介英文');
            $form->textarea('brief', '简介');
            $form->textarea('detail_en', '详情英文');
            $form->textarea('detail', '详情');
            $form->editor('hotel_desc', '酒店描述');
            $form->editor('hotel_desc_en', '酒店描述英文');
            $form->url('website_url', '英文站酒店详情页Url');
            //            $form->radio('is_up_pay', '接收银联')->options([Hotel::RECEIVE => '接收', Hotel::NO_RECEIVE => '不接受']);
            $form->textarea('remark', '说明');
            $form->text('address', '地址');
            $form->text('address_en', '英文地址');
            $form->text('postal_code', '邮编');
            $form->text('phone', '电话');
            $form->text('fax', '传真');
            $form->text('email', '邮件地址');
            //            $form->radio('hasPools', '泳池')->options([1 => '有', 0 => '无']);
            //            $form->radio('michelinStarredRestaurants', '米其林餐厅')->options([1 => '有', 0 => '无']);
            $form->radio('online_status', '状态')->options([Hotel::ON => '上线', Hotel::OFF => '下线', Hotel::ADD => '新增']);

            $form->disableResetButton();
            $form->disableViewCheck();
            $form->disableCreatingCheck();
            $form->disableEditingCheck();
            $form->disableViewButton();
            $form->disableDeleteButton();
        });
    }
}
