<?php

namespace App\Admin\Controllers;

use App\Models\UserPreferencesCategory;
use App\Models\UserPreferencesConfig;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Support\Facades\Cache;

class UserPreferencesConfigController extends AdminController
{
    public function __construct()
    {
        $this->title = '用户偏好列表';
    }

    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserPreferencesConfig, function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('category_id')->display(function ($val) {
                return UserPreferencesCategory::where('id', $val)->value('name');
            });
            $grid->column('name');
            $grid->column('original_id');
            $grid->column('name_en');
            $grid->column('group');
            $grid->column('ows_group');
            $grid->column('yes_code');
            $grid->column('status');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('name');
                $filter->equal('name_en');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserPreferencesConfig, function (Show $show) {
            $show->field('id');
            $show->field('category_id');
            $show->field('name');
            $show->field('original_id');
            $show->field('name_en');
            $show->field('group');
            $show->field('ows_group');
            $show->field('yes_code');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserPreferencesConfig, function (Form $form) {
            $form->display('id');
            $form->select('category_id')->options(UserPreferencesCategory::all()->pluck('name', 'id'));
            $form->text('name');
            //            $form->text('original_id');
            $form->display('name_en');
            $form->display('group');
            $form->display('ows_group');
            $form->display('yes_code');
            $form->switch('status');
        });
    }

    public static function getPreferences($user_id, $preferences)
    {
        if (Cache::has('getPreferences_'.$user_id)) {
            return Cache::get('getPreferences_'.$user_id);
        }
        $category = UserPreferencesCategory::where('status', 1)->select(['id', 'name', 'name_en'])->get()->each(function ($v) use ($preferences) {
            $v['child'] = UserPreferencesConfig::where('category_id', $v['id'])->select(['id', 'name', 'original_id', 'name_en', 'yes_code'])->get()
                ->each(function ($vv) use ($preferences) {
                    $vv['checked'] = 0;
                    foreach ($preferences as $key => $value) {
                        if ($value['preference_type'] == $vv['ows_group'] && $value['preference_value'] == $vv['yes_code']) {
                            $vv['checked'] = 1;
                        }
                    }
                })->toArray();
        })->toArray();
        Cache::put('getPreferences_'.$user_id, $category, 60 * 60);

        return $category;
    }
}
