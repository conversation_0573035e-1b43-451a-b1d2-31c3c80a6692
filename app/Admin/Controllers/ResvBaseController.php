<?php

namespace App\Admin\Controllers;

use App\Models\Hotel;
use App\Models\ResvBase;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;

class ResvBaseController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ResvBase, function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('membership_card_no');
            $grid->column('membership_level');
            $grid->column('reservation_id');
            $grid->column('confirmation_number');
            $grid->column('itinerary_number');
            $grid->column('status');
            $grid->column('hotel_id');
            $grid->column('chain_id');
            $grid->column('check_in_date');
            $grid->column('check_out_date');
            $grid->column('num_rooms');
            $grid->column('adults');
            $grid->column('children');
            $grid->column('children_ages');
            $grid->column('room_code');
            $grid->column('rate_code');
            $grid->column('first_name');
            $grid->column('last_name');
            $grid->column('email');
            $grid->column('phone');
            $grid->column('address_line1');
            $grid->column('address_city');
            $grid->column('address_state');
            $grid->column('address_country');
            $grid->column('address_postal_code');
            $grid->column('card_number');
            $grid->column('card_holder');
            $grid->column('expiry_month');
            $grid->column('expiry_year');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('membership_card_no');
                $filter->equal('confirmation_number');
                $filter->equal('itinerary_number');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ResvBase, function (Show $show) {
            $show->field('id');
            $show->field('membership_card_no');
            $show->field('membership_level');
            $show->field('reservation_id');
            $show->field('confirmation_number');
            $show->field('itinerary_number');
            $show->field('status');
            $show->field('hotel_id');
            $show->field('chain_id');
            $show->field('check_in_date');
            $show->field('check_out_date');
            $show->field('num_rooms');
            $show->field('adults');
            $show->field('children');
            $show->field('children_ages');
            $show->field('room_code');
            $show->field('rate_code');
            $show->field('first_name');
            $show->field('last_name');
            $show->field('email');
            $show->field('phone');
            $show->field('address_line1');
            $show->field('address_city');
            $show->field('address_state');
            $show->field('address_country');
            $show->field('address_postal_code');
            $show->field('card_number');
            $show->field('card_holder');
            $show->field('expiry_month');
            $show->field('expiry_year');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ResvBase, function (Form $form) {
            $form->display('id');
            $form->text('membership_card_no');
            $form->text('membership_level');
            $form->text('reservation_id');
            $form->text('confirmation_number');
            $form->text('itinerary_number');
            $form->text('status');
            $form->text('hotel_id');
            $form->text('chain_id');
            $form->text('check_in_date');
            $form->text('check_out_date');
            $form->text('num_rooms');
            $form->text('adults');
            $form->text('children');
            $form->text('children_ages');
            $form->text('room_code');
            $form->text('rate_code');
            $form->text('first_name');
            $form->text('last_name');
            $form->text('email');
            $form->text('phone');
            $form->text('address_line1');
            $form->text('address_city');
            $form->text('address_state');
            $form->text('address_country');
            $form->text('address_postal_code');
            $form->text('card_number');
            $form->text('card_holder');
            $form->text('expiry_month');
            $form->text('expiry_year');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    public static function getBase($data)
    {
        $sql = ResvBase::where('membership_card_no', $data['membership_card_no']);
        $total = (clone $sql)->count();
        $list = $sql->offset(($data['page'] - 1) * $data['size'])
            ->limit($data['size'])
            ->get()->each(function ($item) {
                $item['hotel'] = Hotel::getCode('', $item['hotel_id']);
                $item['cnyTotalPrice'] = 0;
                $item['roomName'] = 'M2尊贵PLUS客房（双床）';
                $item['offers'] = '会员特惠价';
                $item['night'] = getDaysBetween($item['check_in_date'], $item['check_out_date']);
            });
        $arr['total'] = $total;
        $arr['data'] = $list;

        return $arr;

    }
}
