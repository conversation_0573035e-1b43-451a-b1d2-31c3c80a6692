<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Banner;
use App\Models\BannerType;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;

class BannerController extends AdminController
{
    public function __construct()
    {
        $this->title = '轮播管理';
    }

    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Banner, function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('type_cn', '轮播类型');
            $grid->column('title');
            $grid->column('url');
            $grid->column('button_text');
            $grid->column('img_url')->image();
            $grid->column('status', '状态')->switch();
            $grid->column('order_num');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('title');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Banner, function (Show $show) {
            $show->field('id');
            $show->field('type');
            $show->field('title');
            $show->field('url');
            $show->field('status');
            $show->field('img_url');
            $show->field('order_num');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Banner, function (Form $form) {
            $form->hidden('id');
            $form->select('type')->options(BannerType::all()->pluck('title', 'id'))->required();
            $form->text('title')->required();
            $form->text('sub_title', '小标题');
            $form->text('url');
            $form->text('button_text', '按钮文字');
            $form->radio('mini_switch', '是否小程序使用')->options([
                0 => '不使用',
                1 => '使用',
            ])->when(1, function ($switchField) use ($form) {
                // 使用 $form 添加其他字段
                $form->select('mini_type', '小程序跳转类型')
                    ->options(['offers' => '优惠活动', 'hotel' => '酒店', 'brand' => '品牌'])
                    ->load('mini_id', admin_url('activity/miniOptions'));
                $form->select('mini_id', '小程序跳转内容')
                    ->placeholder('请选择');
            })->default(0);

            $form->image('img_url')->uniqueName()
                ->accept('jpg,png,gif,jpeg,svg')
                ->disk('public')
                ->dir('images')
                ->retainable() // 确保这个存在
                ->removable() // 允许移除
                ->autoUpload()
                ->options([
                    'initialPreviewAsData' => true,
                    'overwriteInitial' => false,
                    'showRemove' => true,
                    'showUpload' => false,
                ]);
            $form->switch('status', '状态')->default(1);
            $form->number('order_num');
        });
    }
}
