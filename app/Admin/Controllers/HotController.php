<?php

namespace App\Admin\Controllers;

use App\Models\City;
use App\Models\Hot;
use App\Models\Hotel;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Http\Request;

class HotController extends AdminController
{
    public function __construct()
    {
        $this->title = '热门管理';
    }

    /**
     * page index
     */
    public function index(Content $content)
    {
        $hot = Hot::find(1);
        $form = $this->form();
        if ($hot) {
            $form->edit(1);
        }
        $form->action(admin_url('hot/save'));

        return $content
            ->header('热门管理')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($form);
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Hot, function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('keyword');
            $grid->column('hotel');
            $grid->column('city');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Hot, function (Show $show) {
            $show->field('id');
            $show->field('keyword');
            $show->field('hotel');
            $show->field('city');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Hot, function (Form $form) {
            $form->hidden('id');
            $form->tags('keyword')->saving(function ($value) {
                return array_filter($value, fn ($v) => ! empty($v));
            });
            $form->multipleSelect('hotel')->options(Hotel::all()->pluck('hotel_name', 'id'))
                ->saving(function ($value) {
                    return array_filter($value, fn ($v) => ! empty($v));
                });
            $form->multipleSelect('city')->options(city::all()->pluck('city_name', 'id'))
                ->saving(function ($value) {
                    return array_filter($value, fn ($v) => ! empty($v));
                });

            $form->hidden('created_at');
            $form->hidden('updated_at');
        });
    }

    public function save(Request $request)
    {
        $data = $request->all();
        $data['keyword'] = array_filter($data['keyword'], fn ($v) => ! empty($v));
        $data['hotel'] = array_filter($data['hotel'], fn ($v) => ! empty($v));
        $data['city'] = array_filter($data['city'], fn ($v) => ! empty($v));
        if ($id = $request->id) {
            $model = Hot::find($id);
        } else {
            $model = new Hot;
        }
        $model->fill($data);
        $model->save();

        return Admin::json()->success('保存成功');
    }
}
