<?php

namespace App\Imports;

use App\Models\Hotel;
use App\Models\HotelImage;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class HotelImports implements ToCollection, WithHeadingRow
{
    public function collection(Collection $rows)
    {
        //        try {
        foreach ($rows as $row) {
            $res = $this->getData($row->toArray());
            $info = Hotel::where('hotelcode', $res['hotel']['hotelcode'])->first();
            if ($info) {
                $info->synxis_hotel_id = $res['hotel']['synxis_hotel_id'];
                $info->save();
            }

            //                Hotel::insert($res['hotel']);
            //                if (! empty($res['hotel_image'])) {
            //                    HotelImage::insert($res['hotel_image']);
            //                }
        }
        //        } catch (\Exception $e) {
        //            throw new $e->getMessage();
        //        }

    }

    private function getData($data)
    {
        $extend = [
            'subBrand' => $data['subbrand'],
            'hotelEmailAddresses' => $data['hotelemailaddresses'],
            'metaTitle' => $data['metatitle'],
            'metaKeywords' => $data['metakeywords'],
            'metaDescription' => $data['metadescription'],
            'noAvailabilityMessageMobile' => $data['noavailabilitymessagemobile'],
            'noAvailabilityMessage' => $data['noavailabilitymessage'],
            'nearByCities' => $data['nearbycities'],
            'hotelType' => $data['hoteltype'],
            'interests' => $data['interests'],
            'destinations' => $data['destinations'],
            'categories' => $data['categories'],
            'ecoCertificates' => $data['ecocertificates'],
            'neighborhoodTag' => $data['neighborhoodtag'],
            'newHotel' => $data['newhotel'],
            'sendInternalConfirmCancelEmails' => $data['sendinternalconfirmcancelemails'],
            'synxisRoomSync' => $data['synxisroomsync'],
            'isCustomNoAvailErrorMessage' => $data['iscustomnoavailerrormessage'],
        ];
        $location = ! empty($data['location']) ? json_decode($data['location'], true) : '';
        $images = explode(';', $data['images']);
        $res = [
            'version_no' => $data['versionno'],
            'hotelcode' => $data['id'],
            'code' => $data['code'],
            'city_code' => $data['city'],
            'hotel_name' => $data['name'],
            'hotel_name_en' => $data['name'],
            'country_code' => $data['locationid'],
            'postal_code' => $data['zipcode'],
            'phone' => $data['phonenumber'],
            'email' => $data['email'],
            'longitude' => $location['longitude'] ?? '',
            'latitude' => $location['latitude'] ?? '',
            'gradient_color' => $data['gradientcolor'],
            'floor_rate' => $data['floorrate'],
            'new_hotel_start_date' => $data['newhotelstartdate'],
            'new_hotel_end_date' => $data['newhotelenddate'],
            'synxis_hotel_id' => $data['synxishotelid'],
            'synxis_chain_id' => $data['synxischainid'],
            'sabre_tax_setup' => $data['sabretaxsetup'],
            'zero_rate_code' => $data['zeroratecode'],
            'phone_country_code' => $data['phonecountrycode'],
            'phone_area_code' => $data['phoneareacode'],
            'hotel_desc_en' => $data['description'],
            'brand_code' => $data['brandid'],
            'brand_nameEn' => $data['brandname'],
            'brand_name' => $data['brandname'],
            'feature' => ! empty($data['facilities']) ? $data['facilities'] : null,
            'extend' => json_encode($extend, JSON_UNESCAPED_SLASHES),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'destinations' => ! empty($data['destinations']) ? $data['destinations'] : null,
            'new_hotel' => $data['newhotel'] ? 1 : 2,
            'interests' => ! empty($data['interests']) ? $data['interests'] : null,
            'eco_certificates' => ! empty($data['ecoCertificates']) ? $data['ecoCertificates'] : null,
            'images' => json_encode($images, JSON_UNESCAPED_SLASHES) ?? null,
            'categories' => ! empty($data['categories']) ? $data['categories'] : null,
            'neighborhood_tag' => ! empty($data['neighborhoodTag']) ? $data['neighborhoodTag'] : null,
        ];

        $img = [];

        //        if(!empty($data['images'])){
        //            foreach(explode(";",$data['images']) as $photo){
        //                $img[]=[
        //                    "hotel_code"=>$data['id'],
        //                    "brand_code"=>$data['brandid'],
        //                    "type"      =>HotelImage::TYPE_HOTEL,
        //                    "source_url"=>$photo,
        //                    "url"       =>$photo,
        //                    "created_at"=>date("Y-m-d H:i:s"),
        //                    "updated_at"=>date("Y-m-d H:i:s"),
        //                ];
        //            }
        //        }
        return [
            'hotel' => $res,
            'hotel_image' => $img,
        ];
    }
}
