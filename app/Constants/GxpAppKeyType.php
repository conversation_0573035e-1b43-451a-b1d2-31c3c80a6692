<?php

namespace App\Constants;

class GxpAppKeyType
{
    /**
     * 中国区域app-key
     * 用于以下接口:
     * - Guest Token (POST /auth/token)
     * - Reserve repo posting (POST /reservations-repo)
     * - My Booking (GET /reservations-repo)
     */
    public const CHINA = 'china';

    /**
     * Rotana区域app-key
     * 用于以下接口:
     * - Currency Conversion Rate (GET /ddollars/exchange-rate)
     */
    public const ROTANA = 'rotana';

    /**
     * 获取所有支持的app-key类型
     *
     * @return array
     */
    public static function getAllTypes(): array
    {
        return [
            self::CHINA,
            self::ROTANA,
        ];
    }

    /**
     * 验证app-key类型是否有效
     *
     * @param string $type
     * @return bool
     */
    public static function isValidType(string $type): bool
    {
        return in_array($type, self::getAllTypes());
    }

    /**
     * 根据接口路径获取对应的app-key类型
     *
     * @param string $endpoint
     * @return string
     */
    public static function getTypeByEndpoint(string $endpoint): string
    {
        // 根据接口路径映射到对应的app-key类型
        $endpointMapping = [
            'auth/token' => self::CHINA,
            'ddollars/exchange-rate' => self::ROTANA,
            'reservations-repo' => self::CHINA,
        ];

        foreach ($endpointMapping as $pattern => $type) {
            if (strpos($endpoint, $pattern) !== false) {
                return $type;
            }
        }

        // 默认返回china类型
        return self::CHINA;
    }
}
