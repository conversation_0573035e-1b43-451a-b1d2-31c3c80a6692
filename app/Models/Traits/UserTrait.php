<?php

namespace App\Models\Traits;

use App\Mail\ResetPwd;
use App\Models\User;
use App\Services\GhaHttpService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

trait UserTrait
{
    /**
     * @return array
     *
     * @throws Exception
     */
    public function userRegister($data)
    {
        if (User::where('email', $data['email'])->first()) {
            throw new Exception('该邮箱已注册过', 500);
        }
        if ($data['password'] != $data['confirm_password']) {
            throw new Exception('两次密码不一致', 500);
        }
        $password = bcrypt($data['password']);

        // 调用平台注册接口
        $gha = new GhaHttpService;
        $userInfo = $gha->post('createMember', [
            'email' => $data['email'],
            'password' => $data['password'],
            'firstName' => $data['first_name'],
            'lastName' => $data['last_name'],
            'ghaMarketingYn' => $data['is_message'],
            'enrollmentCode' => 'AN', // todo字段待定
            'language' => $data['language'],
        ]);
        if ($userInfo['code'] != 200) {
            throw new Exception($userInfo['message'], 500);
        }
        DB::beginTransaction();
        try {
            $user = User::create([
                'name' => $data['first_name'].$data['last_name'],
                'email' => $data['email'],
                'password' => $password,
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'country' => $data['country'],
                'city' => $data['city'],
                'is_message' => $data['is_message'],
                'is_auth' => $data['is_auth'],
                'status' => User::IS_SHOW,
            ]);
            $user->profile_id = $userInfo['data']['profileId'];
            $user->membership_card_no = $userInfo['data']['membershipCardNo'];
            $user->save();
            Auth::login($user);
            $token = $user->createToken('api-token')->plainTextToken;
            // 发送邮件
            //            $html = file_get_contents(resource_path('email/register.html'));
            //            // 定义替换数组
            //            $replacements = [
            //                '{{$email}}' => $user['email'],
            //                '{{$database_id}}' => $userInfo['data']['profileId'],
            //                '{{$language}}'=> $data['language'],
            //                '{{$first_name}}' =>$userInfo['data']['firstName'],
            //                '{{$last_name}}' =>$userInfo['data']['lastName'],
            //                '{{$membership_level}}' => $userInfo['data']['membershipLevel'],
            //                '{{$$membership_card_no}}'=>$userInfo['data']['membershipCardNo'],
            //            ];
            //            // 执行替换
            //            foreach ($replacements as $search => $replace) {
            //                $html = str_replace($search, $replace, $html);
            //            }
            //            dispatch(function () use ($user, $html) {
            //                Mail::to($user->email)->send(new ResetPwd("欢迎来到一个回报丰厚的新世界！", $html));
            //            })->afterResponse();
            DB::commit();

            return [
                'token' => $token,
                'token_type' => 'Bearer',
                'user' => $userInfo['data'],
            ];
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new Exception($exception->getMessage(), 500);
        }
    }

    /**
     * @return array|void
     *
     * @throws Exception
     */
    public function userLogin($data, $request)
    {
        $attributes = [
            'email' => $data['email'],
        ];

        // 调用登录接口
        $gha = new GhaHttpService;
        $userInfo = $gha->post('memberLogin', [
            'login' => $data['email'],
            'password' => $data['password'],
            'primaryResultsOnlyYN' => 'Y',
        ], ['include_datamart_memberships' => 'ACTIVE']);
        if ($userInfo['code'] != 200) {
            throw new Exception($userInfo['message'], 500);
        }
        $user_info = $userInfo['data'];
        $user = User::query()->where($attributes)->first();
        if (! $user) {
            $password = bcrypt($data['password']);
            $user = User::create([
                'name' => $user_info['first_name'].$user_info['last_name'],
                'email' => $data['email'],
                'password' => $password,
                'first_name' => $user_info['first_name'],
                'last_name' => $user_info['last_name'],
                'country' => $user_info['country'],
                'city' => $user_info['city'],
                'is_message' => 1,
                'is_auth' => 1,
                'status' => User::IS_SHOW,
            ]);
        }
        $user->profile_id = $user_info['member_balance']['gha_name_ID'];
        $user->membership_card_no = $user_info['member_balance']['membership_card_no'];
        $user->membership_level = $user_info['member_balance']['membership_level'];
        $user->save();
        try {
            Auth::Login($user);
            $token = $user->createToken('api-token')->plainTextToken;
            // 将用户信息存入Session
            session(['user_info' => $userInfo['data']]);
            session(['user_id' => $user->id]);

            Cache::put('user_'.$user->id, $userInfo['data'], 60 * 60 * 24);

            return [
                'token' => $token,
                'token_type' => 'Bearer',
                'user' => $userInfo['data'],
            ];
        } catch (\Exception $exception) {
            throw new Exception($exception->getMessage(), 500);
        }
    }

    /**
     * 重置密码
     *
     * @return array
     *
     * @throws Exception
     */
    public function userResetPwd($data)
    {
        if ($data['password'] != $data['confirm_password']) {
            throw new Exception('两次密码不一致', 500);
        }
        $gha = new GhaHttpService;
        $updatePwd = $gha->post('setPassword', [
            'resetPasswordToken' => $data['token'],
            'password' => $data['password'],
        ]);
        if ($updatePwd['code'] != 200) {
            throw new Exception($updatePwd['message'], 500);
        }

        $user = User::where('email', $data['email'])->first();
        if (empty($user)) {
            $user = new User;
            $user->email = $data['email'];
        }
        DB::beginTransaction();
        try {
            $user->password = bcrypt($data['password']);
            $user->email_verified_at = time();
            $user->save();
            DB::commit();

            return ['email' => $data['email']];
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new Exception($exception->getMessage(), 500);
        }
    }

    /**
     * 修改密码
     *
     * @return array|\Illuminate\Http\JsonResponse
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    public function userUpPwd($data)
    {
        $gha = new GhaHttpService;
        $info = $gha->post('updatePassword', [
            'login' => $data['email'],
            'new_password' => $data['new_password'],
            'old_password' => $data['old_password'],
            'profileID' => $data['profile_id'],
        ]);
        if ($info['code'] != 200) {
            throw new Exception($info['message'], 500);
        }
        $user = User::where('email', $data['email'])->first();
        if (empty($user)) {
            throw new Exception('该账号不存在', 500);
        }
        DB::beginTransaction();
        try {
            $user->password = bcrypt($data['new_password']);
            $user->save();
            DB::commit();

            return ['email' => $data['email']];
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new Exception($exception->getMessage(), 500);
        }
    }

    /**
     * 激活账号
     *
     * @return array
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    public function activeUser($data)
    {
        if ($data['password'] != $data['confirm_password']) {
            throw new Exception('两次密码不一致');
        }
        $gha = new GhaHttpService;
        $ghaMarketingYn = $data['ghaMarketingYn'] ? 'true' : 'false';
        $userInfo = $gha->post('activateAccount', [
            'email' => $data['email'],
            'password' => $data['password'],
            'membershipCardNo' => $data['membershipCardNo'],
            'ghaMarketingYn' => $ghaMarketingYn,
            'propertyDbid' => $data['propertyDbid'] ?? 'TEST_GHA',
        ]);
        if ($userInfo['code'] != 200) {
            throw new Exception($userInfo['message'], 500);
        }
        DB::beginTransaction();
        try {
            if (isset($userInfo['data'])) {
                $user_info = $userInfo['data'];
                $password = bcrypt($data['password']);
                $user = User::where('profile_id', $user_info['profileId'])->first();
                if (! $user) {
                    User::create([
                        'name' => $user_info['firstName'].$user_info['lastName'],
                        'email' => $user_info['email'],
                        'password' => $password,
                        'first_name' => $user_info['firstName'],
                        'last_name' => $user_info['lastName'],
                        'country' => $user_info['country'] ?? 1,
                        'city' => $user_info['city'] ?? 1,
                        'is_message' => $user_info['is_message'] ?? 'true',
                        'is_auth' => $user_info['is_auth'] ?? '1',
                        'status' => User::IS_SHOW,
                        'membership_card_no' => $user_info['membershipCardNo'],
                        'profile_id' => $user_info['profileId'],
                    ]);
                }
            }
            DB::commit();

            return ['email' => $data['email'], 'code' => 200];
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new Exception($exception->getMessage(), 500);
        }
    }

    /**
     * 找回会籍号
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \Exception
     */
    public function findMember(Request $request)
    {
        $this->validate($request->all(), [
            'email' => 'required|email',
            'last_name' => 'required',
        ], [
            'email' => '请输入有效邮箱',
            'last_name' => '请输入姓名',
        ]);
        $gha = new GhaHttpService('api_v2');
        $userInfo = $gha->post('member/restore/membership/number', [
            'email' => $request->email,
            'lastName' => $request->last_name,
        ]);
        if ($userInfo['code'] != 200) {
            throw new Exception($userInfo['message'], 500);
        }
        //        return successResponse(['email'=>$userInfo['data']],'找回会籍号邮件已发送');

        //        $user= UserModel::where('email',$request->email)->where('last_name',$request->last_name)->first();
        //        if(!$user){
        //            return errorResponse('用户不存在');
        //        }
        //        // 发送邮件
        //            dispatch(function () use ($user, $request) {
        //        Mail::to($request->email)->send(new ResetPwd("找回会籍号", "您的会员号为:".$user->membership_card_no));
        //            })->afterResponse();

        return successResponse(['email' => $request->email], '找回会籍号邮件已发送');
    }

    public function wechat_register($data) {}
}
