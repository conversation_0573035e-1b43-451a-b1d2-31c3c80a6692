<?php

namespace App\Models;

use App\Services\ElasticsearchService;
use Illuminate\Database\Eloquent\SoftDeletes;

class Hotel extends BaseModel
{
    use SoftDeletes;

    protected $table = 'hotel';

    protected $casts = [
        'extend' => 'json',
        'images' => 'array',
        'city_code' => 'json',
        'feature' => 'json',
        'destinations' => 'json',
        'interests' => 'json',
        'eco_certificates' => 'json',
        'categories' => 'json',
        'neighborhood_tag' => 'json',
    ];

    protected $appends = [
        'brand_code_cn',
        'brand_img',
        'image',
        'is_collect',
        'city_name',
        'country_name',
        'head_line',
    ];

    protected $fillable = [
        'version_no',
        'hotelcode',
        'code',
        'country_code',
        'city_code',
        'city_name',
        'hotel_name',
        'hotel_name_en',
        'hotel_name_pinyin',
        'gradient_color',
        'primary_currency',
        'new_hotel_start_date',
        'new_hotel_end_date',
        'synxis_hotel_id',
        'synxis_chain_id',
        'sabre_tax_setup',
        'online_status',
        'address',
        'address_en',
        'postal_code',
        'phone',
        'fax',
        'email',
        'zero_rate_code',
        'phone_area_code',
        'is_up_pay',
        'feature',
        'offered_service',
        'longitude',
        'latitude',
        'hotel_desc',
        'condition_code',
        'remark',
        'highlight',
        'award_winning_dinning',
        'website_url',
        'unique_quelities_en',
        'unique_quelities',
        'detail_en',
        'detail',
        'brief_en',
        'brief',
        'rooms_total_number',
        'floors_number',
        'restaurants_number',
        'bars_number',
        'hasPools',
        'michelinStarredRestaurants',
        'edit_time',
        'trust_online',
        'hotel_link',
        'hotel_alias_en',
        'crs_api',
        'check_in_after',
        'check_out_before',
        'brand_code',
        'brand_nameEn',
        'brand_name',
        'max_childrenage',
        'open_date',
        'extend',
        'categories',
        'created_at',
        'created_at',
    ];

    const ON = 'online';

    const OFF = 'offline';

    const ADD = 'add';

    // 支持银联
    const RECEIVE = 1;

    // 不支持
    const NO_RECEIVE = 0;

    public function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format($this->getDateFormat());
    }

    public function setImagesAttribute($value)
    {
        if (is_string($value)) {
            // 将逗号分隔的字符串转换为数组
            $images = array_filter(array_map('trim', explode(',', $value)));
            $this->attributes['images'] = json_encode($images);
        } elseif (is_array($value)) {
            $this->attributes['images'] = json_encode($value);
        } else {
            $this->attributes['images'] = json_encode([]);
        }
    }

    public function getImagesAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    public function brand()
    {
        return $this->belongsTo(HotelBrand::class, 'brand_code', 'brand_code');
    }

    public function getHeadLineAttribute($value)
    {
        if ($this->categories) {
            $head_line = HotelLoop::where('original_id', $this->categories[0]['id'])->value('name');
        } else {
            $head_line = '';
        }

        return $head_line;
    }

    public function getBrandCodeCnAttribute($value)
    {
        return HotelBrand::where('brand_code', $this->brand_code)->value('name');
    }

    public function getBrandNameAttribute($value)
    {
        return HotelBrand::where('brand_code', $this->brand_code)->value('name');
    }

    public function getCityNameAttribute($value)
    {
        if ($this->city_code && isset($this->city_code[0]['name'])) {
            return City::where('id', $this->city_code[0]['name'])->value('city_name');
        } else {
            return '';
        }
    }

    public function getCountryNameAttribute($value)
    {
        if ($this->country_code) {
            return Country::where('original_id', $this->country_code)->value('country_name');
        } else {
            return '';
        }
    }

    public function getBrandImgAttribute($value)
    {
        return HotelBrand::where('brand_code', $this->brand_code)->value('logo_svg');
    }

    public function getImageAttribute($value)
    {
        return $this->images[0] ?? '';
    }

    public function getIsCollectAttribute($value)
    {
        $isCollect = 0;
        if (AuthCheck()) {
            $collect = UserCollect::where('type', 'hotel')
                ->where('user_id', AuthUser()->id)
                ->where('collect', $this->id)
                ->first();
            if ($collect) {
                $isCollect = 1;
            }
        }

        return $isCollect;
    }

    public static function getBrandHotelList($brand_code)
    {
        $service = new ElasticsearchService('hotel');
        $hotels = $service->searchHotel([
            'brand_code' => $brand_code,
            'page' => 1,
            'page_size' => 10,
        ]);
        $hotels = $hotels['data'] ?? [];
        foreach ($hotels as $key => &$value) {
            $value['image'] = $value['images'][0] ?? '';
            $value['is_collect'] = UserCollect::getIsCollect('hotel', $value['id']);
        }

        return $hotels;
    }

    public static function getBrandHotelCodes($brand_code)
    {
        return self::where('brand_code', $brand_code)->pluck('code')->toArray() ?? [];
    }

    public static function getEsBrandHotelCodes($brand_code)
    {
        $service = new ElasticsearchService('hotel');
        $hotels = $service->searchHotel([
            'brand_code' => $brand_code,
            'page' => 1,
            'page_size' => 1000,
        ]);
        $brands = [];
        foreach ($hotels['data'] as $hit) {
            if (! empty($hit['code'])) {
                $brands = array_merge($brands, [$hit['code']]);
            }
        }

        return $brands;
    }

    public static function getHotelCode($synxis_hotel_id)
    {
        return self::where('synxis_hotel_id', $synxis_hotel_id)->value('code');
    }

    public static function getEsHotelCode($hotelCode)
    {
        $service = new ElasticsearchService('hotel');
        $hotels = $service->searchHotel([
            'hotelCode' => $hotelCode,
            'page' => 0,
            'page_size' => 1,
        ]);
        $hotel = [];
        foreach ($hotels['data'] as $hit) {
            $hotel = $hit;
        }

        return $hotel;
    }

    public static function getCode($hotelCode = '', $synxis_hotel_id = '')
    {
        if ($hotelCode !== '') {
            $hotel = Hotel::where('code', $hotelCode)->first();
        } else {
            $hotel = Hotel::where('synxis_hotel_id', $synxis_hotel_id)->first();
        }
        if (! $hotel) {
            return [];
        }
        $hotel->image = $hotel['images'][0] ?? '';
        // 酒店系列
        $arr['interest'] = [];
        if ($hotel->interests) {
            $interests = array_column($hotel->interests, 'id');
            $Interest = HotelInterest::whereIn('original_id', $interests)->select(['original_id', 'name'])->get()->toArray();
            $arr['interest'] = $Interest;
        }
        $arr['roms'] = [];
        // 酒店设施
        if ($hotel->feature) {
            $rom_id = array_column($hotel->feature, 'id');
            $roms = HotelRom::whereIn('original_id', $rom_id)->select(['original_id', 'name', 'image'])->get()->toArray();
            $arr['roms'] = $roms;
        }
        // 图片集合
        $arr['images'] = [
            ['key' => 'hotel', 'label' => '酒店', 'images' => $hotel['images'] ?? []],
            ['key' => 'room', 'label' => '房型', 'images' => Room::getEsImages($hotel->code)],
            ['key' => 'offers', 'label' => '活动', 'images' => HotelOffer::getESImages($hotel->code)],
            ['key' => 'dining', 'label' => '餐厅', 'images' => HotelDining::getImages($hotel->code)],
        ];
        $arr['hotel'] = $hotel;
        // 房型介绍
        $room = Room::getESRoomList($hotel->code, $hotel->brand_name);
        $arr['room'] = $room;
        // 酒店住宿优惠
        $offer = HotelOffer::getHotelOfferList($hotel->code);
        $arr['offer'] = $offer;
        // 餐饮与健康
        $dining = HotelDining::getHotelDiningList($hotel->code);
        $arr['dining'] = $dining;
        // 其他酒店
        $city = $hotel->city_code;

        $arr['other'] = [];
        $arr['city_name'] = '';
        if ($city) {
            $service = new ElasticsearchService('hotel');
            $result = $service->searchHotel([
                'city_code' => $city[0]['name'],
                'page_size' => 10,
                'page' => 1,
            ]);
            foreach ($result['data'] as $key => &$value) {
                $value['image'] = $value['images'][0] ?? '';
            }
            $arr['other'] = $result['data'];
            $arr['city_name'] = $city[0]['name'] ?? '';
        }

        return $arr;

    }
}
