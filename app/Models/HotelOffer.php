<?php

namespace App\Models;

use App\Services\ElasticsearchService;

class HotelOffer extends BaseModel
{
    protected $appends = [
        'is_collect',
        'head_line',
    ];

    protected $table = 'hotel_offers';

    protected $casts = [
        'images' => 'array',
        'hotels' => 'json',
    ];

    public function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format($this->getDateFormat());
    }

    // 可以被批量赋值的属性 也方便查看表所有字段及注释
    /** protected $fillable = [
     * 'title', // 优惠标题
     * 'title_cn', // 优惠标题en
     * 'url', // 跳转链接
     * 'start_date', // 优惠开始时间
     * 'end_date', // 优惠结束时间
     * 'stay_start_date', // 入住开始日期
     * 'stay_end_date', // 入住结束日期
     * 'hotel_name', // 酒店名称
     * 'hotel_name_en', // 酒店名称en
     * 'rate_code1', // 房价代码1
     * 'rate_code2', // 房价代码2
     * 'rate_code3', // 房价代码3
     * 'rate_code4', // 房价代码4
     * 'rate_checked', // 房价核对状态
     * 'featured', // 是否推荐
     * 'offer_types', // 优惠类型
     * 'promo_code', // 优惠码
     * 'description', // 优惠描述
     * 'description_en', // 优惠描述en
     * 'offerIncludes', // 优惠包含
     * 'offerIncludes_en', // 优惠包含en
     * 'terms_conditions', // 条款与条件
     * 'terms_conditions_en', // 条款与条件en
     * 'taxes', // 税费说明
     * 'taxes_en', // 税费说明en
     * 'images', // 图片
     * ]; */
    protected $guarded = []; // 批量赋值的黑名单
    // protected $fillable = []; // 可以作为批量赋值的白名单
    // protected $appends = []; // 追加属性
    // protected $hidden = []; // 数组中的属性会被隐藏

    // 或者使用 mutator
    public function setImagesAttribute($value)
    {
        if (is_string($value)) {
            // 将逗号分隔的字符串转换为数组
            $images = array_filter(array_map('trim', explode(',', $value)));
            $this->attributes['images'] = json_encode($images);
        } elseif (is_array($value)) {
            $this->attributes['images'] = json_encode($value);
        } else {
            $this->attributes['images'] = json_encode([]);
        }
    }

    public function getImagesAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    // 设置器：将数组转换为分号分隔的字符串
    public function setOfferTypesAttribute($value)
    {
        if (is_array($value)) {
            // 将数组转换为分号分隔的字符串
            $this->attributes['offer_types'] = implode('; ', array_filter($value));
        } elseif (is_string($value)) {
            $this->attributes['offer_types'] = $value;
        } else {
            $this->attributes['offer_types'] = '';
        }
    }

    // 获取器：将分号分隔的字符串转换为数组
    public function getOfferTypesAttribute($value)
    {
        if (empty($value)) {
            return [];
        }

        // 将分号分隔的字符串转换为数组
        return array_filter(array_map('trim', explode(';', $value)));
    }

    // 设置器：将数组转换为分号分隔的字符串
    public function setOfferTypesEnAttribute($value)
    {
        if (is_array($value)) {
            // 将数组转换为分号分隔的字符串
            $this->attributes['offer_types_en'] = implode('; ', array_filter($value));
        } elseif (is_string($value)) {
            $this->attributes['offer_types_en'] = $value;
        } else {
            $this->attributes['offer_types_en'] = '';
        }
    }

    // 获取器：将分号分隔的字符串转换为数组
    public function getOfferTypesEnAttribute($value)
    {
        if (empty($value)) {
            return [];
        }

        // 将分号分隔的字符串转换为数组
        return array_filter(array_map('trim', explode(';', $value)));
    }

    // 在模型中
    public function getStartDateAttribute($value)
    {
        return $value ? date('Y-m-d', $value) : '';
    }

    public function setStartDateAttribute($value)
    {
        return $value ? strtotime($value) : '';
    }

    public function getEndDateAttribute($value)
    {
        return $value ? date('Y-m-d', $value) : '';
    }

    public function setEndDateAttribute($value)
    {
        return $value ? strtotime($value) : '';
    }

    public function getStayStartDateAttribute($value)
    {
        return $value ? date('Y-m-d', $value) : '';
    }

    public function setStayStartDateAttribute($value)
    {
        return $value ? strtotime($value) : '';
    }

    public function getStayEndDateAttribute($value)
    {
        return $value ? date('Y-m-d', $value) : '';
    }

    public function setStayEndDateAttribute($value)
    {
        return $value ? strtotime($value) : '';
    }

    public function getHeadLineAttribute($value)
    {
        if (! empty($this->offer_types)) {
            $head_line = $this->offer_types[0] ?? '';
        } else {
            $head_line = '';
        }

        return $head_line;
    }

    public static function getHotelOfferList($hotelcode)
    {
        if (! is_array($hotelcode)) {
            $hotelcode = explode(',', $hotelcode);
        }
        $hotelOffers = HotelOffer::where(function ($query) use ($hotelcode) {
            foreach ($hotelcode as $code) {
                $query->orWhereJsonContains('hotels', $code);
            }
        })
            ->select(['id', 'external_id', 'offer_types', 'title', 'url', 'end_date', 'stay_start_date', 'stay_end_date', 'hotel_name', 'images', 'image'])
            ->limit(10)
            ->get();
        // 获取酒店信息
        $hotel = Hotel::whereIn('code', $hotelcode)->first();
        if ($hotel) {
            $brand = HotelBrand::where('brand_code', $hotel->brand_code)->first();
        } else {
            $brand = [];
        }
        foreach ($hotelOffers as $key => &$value) {
            $value['brand_name'] = $brand->name ?? '';
            $value['country_name'] = $hotel->country_name ?? '';
            $value['city_name'] = $hotel->city_name ?? '';
            $value['brand_img'] = $brand->logo_svg ?? '';
            $value['brand_id'] = $brand->id ?? '';
        }

        return $hotelOffers;
    }

    public static function getImages($hotelcode)
    {
        $roomList = HotelOffer::whereJsonContains('hotels', $hotelcode)->get()->pluck('images')->toArray();
        $images = [];
        foreach ($roomList as $key => $value) {
            $images = array_merge($images, $value);
        }

        return $images;
    }

    public static function getEsImages($hotelcode)
    {
        $service = new ElasticsearchService('offers');
        $result = $service->searchOffers(['hotels' => $hotelcode]);
        $images = [];
        foreach ($result as $hit) {
            if (! empty($hit['_source']['images'])) {
                $images = array_merge($images, $hit['_source']['images']);
            }
        }

        return $images;
    }

    public function getIsCollectAttribute($value)
    {
        $isCollect = 0;
        if (AuthCheck()) {
            $isCollect = UserCollect::where('type', 'offer')
                ->where('user_id', AuthUser()->id)
                ->where('collect', $this->id)
                ->first();
            if ($isCollect) {
                $isCollect = 1;
            }
        }

        return $isCollect;
    }
}
