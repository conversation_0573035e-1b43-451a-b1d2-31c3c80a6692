<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

class Hot extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'hot';

    protected $casts = [
        'keyword' => 'array',
        'hotel' => 'array',
        'city' => 'array',
    ];

    // 可以被批量赋值的属性 也方便查看表所有字段及注释
    /** protected $fillable = [
        'keyword', // 热门关键词
        'hotel', // 热门酒店
        'city', // 热门城市
    ]; */
    protected $guarded = []; // 批量赋值的黑名单
    // protected $fillable = []; // 可以作为批量赋值的白名单
    // protected $appends = []; // 追加属性
    // protected $hidden = []; // 数组中的属性会被隐藏

}
