<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class Activity extends BaseModel
{
    use SoftDeletes;

    protected $table = 'activity';

    protected $casts = [];

    protected $fillable = [
        'name',
        'name_en',
        'status',
        'created_at',
        'updated_at',
        'activity_type',
        'address',
        'is_show',
        'web_desc',
        'key_words',
        'page_title',
        'description',
        'activity_link',
        'rate',
        'access_code',
        'status',
        'line_type',
        'url',
        'date',
        'img_url',
        'img_url_f',
        'content',
        'order',
        'user_id',
    ];

    public function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format($this->getDateFormat());
    }

    // 上线
    const STATUS_ON = 1;

    // 下线
    const STATUS_OFF = 0;

    // 链接类型
    const LINE_TYPE_URL = [
        1 => '内部链接',
        2 => '外部链接',
    ];

    public function getImgUrlAttribute($value)
    {
        if (empty($value)) {
            return '';
        }

        return Storage::disk('public')->url($value);
    }

    public function setImgUrlAttribute($value)
    {
        if ($value) {
            // 移除 Storage 生成的完整 URL，只保留相对路径
            $baseUrl = Storage::disk('public')->url('');
            $value = str_replace($baseUrl, '', $value);
        }
        $this->attributes['img_url'] = $value;
    }

    /**
     * 获取活动类型
     *
     * @return array
     */
    public static function getActivityType($type, $mini_switch = 0)
    {
        $list = Cache::remember('activity'.$type.$mini_switch, 120, function () use ($type, $mini_switch) {
            $where = [];
            if ($mini_switch == 1) {
                $where['mini_switch'] = $mini_switch;
            }

            return Activity::where('activity_type', $type)
                ->where($where)
                ->where('status', 1)->select(['id', 'name', 'sub_name', 'description', 'img_url', 'url', 'button_text', 'hotel_name', 'hotel_url', 'tag_name', 'address', 'mini_switch', 'mini_type', 'mini_id'])->get();
        });

        return $list;
    }
}
