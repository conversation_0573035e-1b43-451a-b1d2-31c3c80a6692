<?php

namespace App\Models;

class ResvBase extends BaseModel
{
    public function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format($this->getDateFormat());
    }

    protected $table = 'resv_base';

    // 可以被批量赋值的属性 也方便查看表所有字段及注释
    /** protected $fillable = [
        'membership_card_no', // 会员卡号
        'membership_level', // 会员等级
        'reservation_id', // 预定ID
        'confirmation_number', // 确认号
        'itinerary_number', // 行程号
        'status', // 状态
        'hotel_id', // 酒店synxis_hotel_id
        'chain_id', // 酒店synxis_chain_id
        'check_in_date', // 开始时间
        'check_out_date', // 结束时间
        'num_rooms', // 房间数
        'adults', // 成人数
        'children', // 儿童数
        'children_ages', // 儿童年龄
        'room_code', // 房型代码
        'rate_code', // 房价代码
        'first_name', // 名
        'last_name', // 姓
        'email', // 邮件
        'phone', // 手机号
        'address_line1', // 详细地址
        'address_city', // 城市
        'address_state', // 区域
        'address_country', // 国家
        'address_postal_code', // 邮编
        'card_number', // 卡号
        'card_holder', // 姓名
        'expiry_month', // 月份
        'expiry_year', // 年份
    ]; */
    protected $guarded = []; // 批量赋值的黑名单
    // protected $fillable = []; // 可以作为批量赋值的白名单
    // protected $appends = []; // 追加属性
    // protected $hidden = []; // 数组中的属性会被隐藏

    public static function createResv($data, $res)
    {
        $reservation = new ResvBase;
        $reservation->membership_card_no = $data['loyaltyNumber'] ?? '';
        $reservation->membership_level = $data['loyaltyLevel'] ?? '';
        $reservation->reservation_id = $res['reservation_id'] ?? '';
        $reservation->confirmation_number = $res['confirmation_number'] ?? '';
        $reservation->itinerary_number = $res['itinerary_number'] ?? '';
        $reservation->status = $res['status'] ?? '';
        $reservation->hotel_id = $data['hotelId'] ?? '';
        $reservation->chain_id = $data['chainId'] ?? '';
        $reservation->check_in_date = $data['checkInDate'] ?? '';
        $reservation->check_out_date = $data['checkOutDate'] ?? '';
        $reservation->num_rooms = $data['numRooms'] ?? '';
        $reservation->adults = $data['adults'] ?? '';
        $reservation->children = $data['children'] ?? '';
        $reservation->children_ages = json_encode($data['childrenAges']) ?? [];
        $reservation->room_code = $data['roomCode'] ?? '';
        $reservation->rate_code = $data['rateCode'] ?? '';
        $reservation->first_name = $data['primaryGuest']['firstName'] ?? '';
        $reservation->last_name = $data['primaryGuest']['lastName'] ?? '';
        $reservation->email = $data['primaryGuest']['email'] ?? '';
        $reservation->phone = $data['primaryGuest']['phone'] ?? '';
        $reservation->address_line1 = $data['primaryGuest']['address']['line1'] ?? '';
        $reservation->address_city = $data['primaryGuest']['address']['city'] ?? '';
        $reservation->address_state = $data['primaryGuest']['address']['state'] ?? '';
        $reservation->address_country = $data['primaryGuest']['address']['country'] ?? '';
        $reservation->address_postal_code = $data['primaryGuest']['address']['postalCode'] ?? '';
        $reservation->card_type = $data['payment']['cardType'] ?? '';
        $reservation->card_number = $data['payment']['cardNumber'] ?? '';
        $reservation->card_holder = $data['payment']['cardHolder'] ?? '';
        $reservation->expiry_month = $data['payment']['expiryMonth'] ?? '';
        $reservation->expiry_year = $data['payment']['expiryYear'] ?? '';
        $reservation->save();
    }

    public function hotel($value, $data)
    {
        dd($value);
    }
}
