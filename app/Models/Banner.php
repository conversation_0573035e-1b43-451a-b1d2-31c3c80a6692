<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class Banner extends BaseModel
{
    use SoftDeletes;

    protected $table = 'banner';

    protected $appends = [
        'type_cn',
    ];

    // 可以被批量赋值的属性 也方便查看表所有字段及注释
    /** protected $fillable = [
        'type', // banner类型
        'title', // 标题
        'url', // 跳转地址
        'status', // 状态1:上线，0:下线
        'img_url', // 图片地址
        'order_num', // 排序
    ]; */
    protected $guarded = []; // 批量赋值的黑名单
    // protected $fillable = []; // 可以作为批量赋值的白名单
    // protected $appends = []; // 追加属性
    // protected $hidden = []; // 数组中的属性会被隐藏

    // status 类型
    const ON = 1;

    const OFF = 0;

    public function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format($this->getDateFormat());
    }

    public function getTypeCnAttribute()
    {
        return (new BannerType)->where('id', $this->type)->value('title');
    }

    public function getImgUrlAttribute($value)
    {
        if (empty($value)) {
            return '';
        }

        return Storage::disk('public')->url($value);
    }

    public function setImgUrlAttribute($value)
    {
        if ($value) {
            // 移除 Storage 生成的完整 URL，只保留相对路径
            $baseUrl = Storage::disk('public')->url('');
            $value = str_replace($baseUrl, '', $value);
        }
        $this->attributes['img_url'] = $value;
    }

    /**
     * 轮播列表
     *
     * @return mixed
     */
    public static function getBannerList($type, $mini_switch = 0)
    {
        $list = Cache::remember('banner'.$type.$mini_switch, 120, function () use ($type, $mini_switch) {
            $where = [];
            if ($mini_switch == 1) {
                $where['mini_switch'] = $mini_switch;
            }
            $list = Banner::where('type', $type)
                ->where('status', 1)
                ->where($where)
                ->select(['id', 'type', 'title', 'sub_title', 'url', 'img_url', 'url', 'button_text', 'hotel_id', 'mini_switch', 'mini_type', 'mini_id'])
                ->get();

            foreach ($list as $k => &$v) {
                $hotel = Hotel::where('id', $v['hotel_id'])->select(['id', 'hotel_name', 'hotel_name_en', 'brand_code'])->first();
                $v['hotel_name'] = $hotel['hotel_name'] ?? '';
                $v['hotel_name_en'] = $hotel['hotel_name_en'] ?? '';
                $v['brand_code'] = $hotel['brand_code'] ?? '';
                $v['brand_img'] = $hotel['brand_img'] ?? '';
            }

            return $list;
        });

        return $list;
    }
}
