<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReservationStatusChangedEvent
{
    use Dispatchable, SerializesModels;

    public int|string|null $profileId;

    public string $itineraryNumber;

    public string $status;

    public string $chainId;

    public string $hotelId;

    public array $reservationDetails;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $itineraryNumber,
        string $status,
        string $chainId,
        string $hotelId,
        array $reservationDetails,
        int|string|null $profileId = null
    ) {
        $this->itineraryNumber = $itineraryNumber;
        $this->status = $status;
        $this->chainId = $chainId;
        $this->hotelId = $hotelId;
        $this->reservationDetails = $reservationDetails;
        $this->profileId = $profileId;
    }

    /**
     * 获取事件数据数组
     */
    public function toArray(): array
    {
        $data = [
            'itinerary_number' => $this->itineraryNumber,
            'status' => $this->status,
            'chain_id' => $this->chainId,
            'hotel_id' => $this->hotelId,
            'reservation_details' => $this->reservationDetails,
        ];

        if ($this->profileId !== null) {
            $data['profile_id'] = $this->profileId;
        }

        return $data;
    }
}
