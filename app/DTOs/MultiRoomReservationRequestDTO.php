<?php

namespace App\DTOs;

/**
 * 多房间预订请求DTO
 * 支持批量修改多个房间
 */
class MultiRoomReservationRequestDTO
{
    public function __construct(
        public readonly string $itineraryNumber,
        public readonly int $hotelId,
        public readonly string $checkInDate,
        public readonly string $checkOutDate,
        public readonly array $rooms, // SimplifiedRoomRequestDTO[]
        public readonly SimplifiedGuestDTO $primaryGuest,
        public readonly SimplifiedPaymentDTO $payment,
        public readonly ?string $loyaltyNumber = null,
        public readonly ?string $loyaltyLevel = null,
        public readonly ?string $promoCode = null,
        public readonly ?string $specialRequests = null,
        public readonly bool $sendConfirmationEmail = false
    ) {}

    /**
     * 从请求数组创建DTO
     */
    public static function fromArray(array $data): self
    {
        $rooms = [];
        foreach ($data['rooms'] as $roomData) {
            $rooms[] = SimplifiedRoomRequestDTO::fromArray($roomData);
        }

        return new self(
            itineraryNumber: $data['itineraryNumber'],
            hotelId: $data['hotelId'],
            checkInDate: $data['checkInDate'],
            checkOutDate: $data['checkOutDate'],
            rooms: $rooms,
            primaryGuest: SimplifiedGuestDTO::fromArray($data['primaryGuest']),
            payment: SimplifiedPaymentDTO::fromArray($data['payment']),
            loyaltyNumber: $data['loyaltyNumber'] ?? null,
            loyaltyLevel: $data['loyaltyLevel'] ?? null,
            promoCode: $data['promoCode'] ?? null,
            specialRequests: $data['specialRequests'] ?? null,
            sendConfirmationEmail: $data['sendConfirmationEmail'] ?? false
        );
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'itineraryNumber' => $this->itineraryNumber,
            'hotelId' => $this->hotelId,
            'checkInDate' => $this->checkInDate,
            'checkOutDate' => $this->checkOutDate,
            'rooms' => array_map(fn ($room) => $room->toArray(), $this->rooms),
            'primaryGuest' => $this->primaryGuest->toArray(),
            'payment' => $this->payment->toArray(),
            'loyaltyNumber' => $this->loyaltyNumber,
            'loyaltyLevel' => $this->loyaltyLevel,
            'promoCode' => $this->promoCode,
            'specialRequests' => $this->specialRequests,
            'sendConfirmationEmail' => $this->sendConfirmationEmail,
        ];
    }

    /**
     * 检查是否有忠诚度会员信息
     */
    public function hasLoyalty(): bool
    {
        return ! empty($this->loyaltyNumber);
    }

    /**
     * 检查是否有促销代码
     */
    public function hasPromo(): bool
    {
        return ! empty($this->promoCode);
    }

    /**
     * 获取房间数量
     */
    public function getRoomCount(): int
    {
        return count($this->rooms);
    }

    /**
     * 获取总成人数
     */
    public function getTotalAdults(): int
    {
        return array_sum(array_map(fn ($room) => $room->adults, $this->rooms));
    }

    /**
     * 获取总儿童数
     */
    public function getTotalChildren(): int
    {
        return array_sum(array_map(fn ($room) => $room->children, $this->rooms));
    }

    /**
     * 检查是否有儿童
     */
    public function hasChildren(): bool
    {
        return $this->getTotalChildren() > 0;
    }
}
