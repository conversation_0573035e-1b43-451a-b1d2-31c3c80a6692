<?php

namespace App\DTOs;

/**
 * 统一的预订请求DTO
 * 支持创建、修改和取消操作，数据格式统一
 */
class UnifiedReservationRequestDTO
{
    public function __construct(
        public readonly string $status, // create, modify, cancel
        public readonly int $hotelId,
        public readonly string $checkInDate,
        public readonly string $checkOutDate,
        public readonly SimplifiedGuestDTO $primaryGuest,
        public readonly SimplifiedPaymentDTO $payment,
        public readonly array $rooms = [], // SimplifiedRoomRequestDTO[]
        public readonly ?string $itineraryNumber = null, // 修改/取消时需要
        public readonly ?string $confirmationNumber = null, // 单房间修改时需要
        public readonly array $confirmationNumbers = [], // 批量取消时需要
        public readonly ?string $loyaltyNumber = null,
        public readonly ?string $loyaltyLevel = null,
        public readonly ?string $promoCode = null,
        public readonly ?string $specialRequests = null,
        public readonly bool $sendConfirmationEmail = false,
        // 单房间操作的兼容字段
        public readonly ?string $roomCode = null,
        public readonly ?string $rateCode = null,
        public readonly int $adults = 1,
        public readonly int $children = 0,
        public readonly array $childrenAges = []
    ) {}

    /**
     * 从请求数组创建DTO
     */
    public static function fromArray(array $data): self
    {
        $rooms = [];
        if (isset($data['rooms']) && is_array($data['rooms'])) {
            foreach ($data['rooms'] as $roomData) {
                $rooms[] = SimplifiedRoomRequestDTO::fromArray($roomData);
            }
        }

        return new self(
            status: $data['status'],
            hotelId: $data['hotelId'],
            checkInDate: $data['checkInDate'],
            checkOutDate: $data['checkOutDate'],
            primaryGuest: SimplifiedGuestDTO::fromArray($data['primaryGuest']),
            payment: SimplifiedPaymentDTO::fromArray($data['payment']),
            rooms: $rooms,
            itineraryNumber: $data['itineraryNumber'] ?? null,
            confirmationNumber: $data['confirmationNumber'] ?? $data['CRS_confirmationNumber'] ?? null,
            confirmationNumbers: $data['confirmationNumbers'] ?? [],
            loyaltyNumber: $data['loyaltyNumber'] ?? null,
            loyaltyLevel: $data['loyaltyLevel'] ?? null,
            promoCode: $data['promoCode'] ?? null,
            specialRequests: $data['specialRequests'] ?? null,
            sendConfirmationEmail: $data['sendConfirmationEmail'] ?? false,
            roomCode: $data['roomCode'] ?? null,
            rateCode: $data['rateCode'] ?? null,
            adults: $data['adults'] ?? 1,
            children: $data['children'] ?? 0,
            childrenAges: $data['childrenAges'] ?? []
        );
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'status' => $this->status,
            'hotelId' => $this->hotelId,
            'checkInDate' => $this->checkInDate,
            'checkOutDate' => $this->checkOutDate,
            'primaryGuest' => $this->primaryGuest->toArray(),
            'payment' => $this->payment->toArray(),
            'rooms' => array_map(fn ($room) => $room->toArray(), $this->rooms),
            'itineraryNumber' => $this->itineraryNumber,
            'confirmationNumber' => $this->confirmationNumber,
            'confirmationNumbers' => $this->confirmationNumbers,
            'loyaltyNumber' => $this->loyaltyNumber,
            'loyaltyLevel' => $this->loyaltyLevel,
            'promoCode' => $this->promoCode,
            'specialRequests' => $this->specialRequests,
            'sendConfirmationEmail' => $this->sendConfirmationEmail,
            'roomCode' => $this->roomCode,
            'rateCode' => $this->rateCode,
            'adults' => $this->adults,
            'children' => $this->children,
            'childrenAges' => $this->childrenAges,
        ];
    }

    /**
     * 检查是否是多房间操作
     */
    public function isMultiRoom(): bool
    {
        return ! empty($this->rooms);
    }

    /**
     * 检查是否是单房间操作
     */
    public function isSingleRoom(): bool
    {
        return ! $this->isMultiRoom() && ! empty($this->roomCode) && ! empty($this->rateCode);
    }

    /**
     * 检查是否有忠诚度会员信息
     */
    public function hasLoyalty(): bool
    {
        return ! empty($this->loyaltyNumber);
    }

    /**
     * 检查是否有促销代码
     */
    public function hasPromo(): bool
    {
        return ! empty($this->promoCode);
    }

    /**
     * 检查操作类型
     */
    public function isCreate(): bool
    {
        return $this->status === 'create';
    }

    public function isModify(): bool
    {
        return $this->status === 'modify';
    }

    public function isCancel(): bool
    {
        return $this->status === 'cancel';
    }

    /**
     * 获取房间数量
     */
    public function getRoomCount(): int
    {
        return $this->isMultiRoom() ? count($this->rooms) : 1;
    }

    /**
     * 获取总成人数
     */
    public function getTotalAdults(): int
    {
        if ($this->isMultiRoom()) {
            return array_sum(array_map(fn ($room) => $room->adults, $this->rooms));
        }

        return $this->adults;
    }

    /**
     * 获取总儿童数
     */
    public function getTotalChildren(): int
    {
        if ($this->isMultiRoom()) {
            return array_sum(array_map(fn ($room) => $room->children, $this->rooms));
        }

        return $this->children;
    }

    /**
     * 检查是否有儿童
     */
    public function hasChildren(): bool
    {
        return $this->getTotalChildren() > 0;
    }

    /**
     * 转换为单房间请求格式（兼容现有接口）
     */
    public function toSingleRoomFormat(): array
    {
        $data = [
            'hotelId' => $this->hotelId,
            'checkInDate' => $this->checkInDate,
            'checkOutDate' => $this->checkOutDate,
            'primaryGuest' => $this->primaryGuest->toArray(),
            'payment' => $this->payment->toArray(),
            'loyaltyNumber' => $this->loyaltyNumber,
            'promoCode' => $this->promoCode,
            'specialRequests' => $this->specialRequests,
            'sendConfirmationEmail' => $this->sendConfirmationEmail,
            'numRooms' => 1,
        ];

        if ($this->isSingleRoom()) {
            $data['roomCode'] = $this->roomCode;
            $data['rateCode'] = $this->rateCode;
            $data['adults'] = $this->adults;
            $data['children'] = $this->children;
            $data['childrenAges'] = $this->childrenAges;
        } elseif ($this->isMultiRoom() && ! empty($this->rooms)) {
            $firstRoom = $this->rooms[0];
            $data['roomCode'] = $firstRoom->roomCode;
            $data['rateCode'] = $firstRoom->rateCode;
            $data['adults'] = $firstRoom->adults;
            $data['children'] = $firstRoom->children;
            $data['childrenAges'] = $firstRoom->childrenAges;
        }

        // 添加修改相关字段
        if ($this->isModify()) {
            if ($this->confirmationNumber) {
                $data['CRS_confirmationNumber'] = $this->confirmationNumber;
            }
            if ($this->itineraryNumber) {
                $data['itineraryNumber'] = $this->itineraryNumber;
            }
        }

        // 添加取消相关字段
        if ($this->isCancel()) {
            if (! empty($this->confirmationNumbers)) {
                $data['confirmationNumber'] = $this->confirmationNumbers;
            }
            if ($this->itineraryNumber) {
                $data['itineraryNumber'] = $this->itineraryNumber;
            }
        }

        return $data;
    }

    /**
     * 验证请求数据
     */
    public function validate(): array
    {
        $errors = [];

        // 验证操作类型
        if (! in_array($this->status, ['create', 'modify', 'cancel'])) {
            $errors[] = '无效的操作类型，支持的类型：create, modify, cancel';
        }

        // 验证修改操作
        if ($this->isModify()) {
            if ($this->isMultiRoom() && ! $this->itineraryNumber) {
                $errors[] = '多房间修改需要提供行程号';
            }
            if ($this->isSingleRoom() && ! $this->confirmationNumber) {
                $errors[] = '单房间修改需要提供确认号';
            }
            if ($this->isMultiRoom()) {
                foreach ($this->rooms as $index => $room) {
                    // 验证房间状态
                    if (! $room->hasValidStatus()) {
                        $errors[] = '房间 '.($index + 1).' 的状态无效，支持的状态：modify, create, cancel';
                    }

                    // 验证不同状态下的必需字段
                    $roomStatus = $room->getStatus();
                    if (in_array($roomStatus, ['modify', 'cancel']) && ! $room->confirmationNumber) {
                        $errors[] = '房间 '.($index + 1).' 的操作类型为 '.$roomStatus.' 时需要提供确认号';
                    }
                }
            }
        }

        // 验证取消操作
        if ($this->isCancel()) {
            if (! $this->itineraryNumber && empty($this->confirmationNumbers)) {
                $errors[] = '取消预订需要提供行程号或确认号';
            }
        }

        // 验证儿童年龄
        if ($this->isMultiRoom()) {
            foreach ($this->rooms as $index => $room) {
                if (! $room->validateChildrenAges()) {
                    $errors[] = '房间 '.($index + 1).' 的儿童数量与年龄数量不匹配';
                }

                // 只对create和modify状态的房间验证儿童年龄范围
                $roomStatus = $room->getStatus();
                if (in_array($roomStatus, ['create', 'modify'])) {
                    foreach ($room->childrenAges as $age) {
                        if ($age < 0 || $age > 17) {
                            $errors[] = '房间 '.($index + 1).' 的儿童年龄必须在0-17岁之间，当前: '.$age.'岁';
                        }
                    }
                }
            }
        } elseif ($this->children > 0 && count($this->childrenAges) !== $this->children) {
            $errors[] = '儿童数量与提供的年龄数量不匹配';
        }

        return $errors;
    }
}
