<?php

namespace App\DTOs\Sabre;

class ReservationRequestDTO
{
    public function __construct(
        public readonly ?BookingInfoDTO $bookingInfo,
        public readonly ChainDTO $chain,
        public readonly HotelDTO $hotel,
        public readonly ChannelsDTO $channels,
        public readonly array $guests,
        public readonly ?LanguageDTO $language,
        public readonly ?MarketSourceDTO $marketSource,
        public readonly ?NotificationDTO $notification,
        public readonly RoomStayDTO $roomStay,
        public readonly string $status,
        public readonly ?array $loyaltyMemberships = null,
        public readonly ?PromotionDTO $promotion = null,
        public readonly ?string $itineraryNumber = null,
        public readonly ?string $crsConfirmationNumber = null,
        public readonly ?string $purposeOfStay = null,
        public readonly ?string $roomStaySplit = null
    ) {}

    public function toArray(): array
    {
        $data = [
            'Chain' => $this->chain->toArray(),
            'Hotel' => $this->hotel->toArray(),
            'Channels' => $this->channels->toArray(),
            'Guests' => array_map(fn ($guest) => $guest->toArray(), $this->guests),
            'RoomStay' => $this->roomStay->toArray(),
            'status' => $this->status,
        ];

        // 创建预订时的必需字段
        if ($this->bookingInfo !== null) {
            $data['BookingInfo'] = $this->bookingInfo->toArray();
        }

        if ($this->language !== null) {
            $data['Language'] = $this->language->toArray();
        }

        if ($this->marketSource !== null) {
            $data['MarketSource'] = $this->marketSource->toArray();
        }

        if ($this->notification !== null) {
            $data['Notification'] = $this->notification->toArray();
        }

        // 可选字段
        if ($this->loyaltyMemberships !== null) {
            $data['LoyaltyMemberships'] = array_map(
                fn ($membership) => $membership->toArray(),
                $this->loyaltyMemberships
            );
        }

        if ($this->promotion !== null) {
            $data['Promotion'] = $this->promotion->toArray();
        }

        if ($this->itineraryNumber !== null) {
            $data['ItineraryNumber'] = $this->itineraryNumber;
        }

        // 修改预订时的特殊字段
        if ($this->crsConfirmationNumber !== null) {
            $data['CRS_confirmationNumber'] = $this->crsConfirmationNumber;
        }

        if ($this->purposeOfStay !== null) {
            $data['PurposeOfStay'] = $this->purposeOfStay;
        }

        if ($this->roomStaySplit !== null) {
            $data['roomStaySplit'] = $this->roomStaySplit;
        }

        return $data;
    }

    public static function fromArray(array $data): self
    {
        $guests = array_map(
            fn ($guestData) => GuestDTO::fromArray($guestData),
            $data['Guests']
        );

        $loyaltyMemberships = null;
        if (isset($data['LoyaltyMemberships'])) {
            $loyaltyMemberships = array_map(
                fn ($membershipData) => LoyaltyMembershipDTO::fromArray($membershipData),
                $data['LoyaltyMemberships']
            );
        }

        return new self(
            bookingInfo: isset($data['BookingInfo']) ? BookingInfoDTO::fromArray($data['BookingInfo']) : null,
            chain: ChainDTO::fromArray($data['Chain']),
            hotel: HotelDTO::fromArray($data['Hotel']),
            channels: ChannelsDTO::fromArray($data['Channels']),
            guests: $guests,
            language: isset($data['Language']) ? LanguageDTO::fromArray($data['Language']) : null,
            marketSource: isset($data['MarketSource']) ? MarketSourceDTO::fromArray($data['MarketSource']) : null,
            notification: isset($data['Notification']) ? NotificationDTO::fromArray($data['Notification']) : null,
            roomStay: RoomStayDTO::fromArray($data['RoomStay']),
            status: $data['status'],
            loyaltyMemberships: $loyaltyMemberships,
            promotion: isset($data['Promotion']) ? PromotionDTO::fromArray($data['Promotion']) : null,
            itineraryNumber: $data['ItineraryNumber'] ?? null,
            crsConfirmationNumber: $data['CRS_confirmationNumber'] ?? null,
            purposeOfStay: $data['PurposeOfStay'] ?? null,
            roomStaySplit: $data['roomStaySplit'] ?? null
        );
    }
}
