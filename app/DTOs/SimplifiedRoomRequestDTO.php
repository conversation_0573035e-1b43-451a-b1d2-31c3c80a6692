<?php

namespace App\DTOs;

/**
 * 简化的单个房间请求DTO
 */
class SimplifiedRoomRequestDTO
{
    public function __construct(
        public readonly string $roomCode,
        public readonly string $rateCode,
        public readonly int $adults = 1,
        public readonly int $children = 0,
        public readonly array $childrenAges = [],
        public readonly ?string $confirmationNumber = null, // 用于修改预订时指定具体房间
        public readonly ?array $guests = null, // 房间特定的客人信息
        public readonly ?string $specialRequests = null,
        public readonly ?string $status = null // 房间操作类型：modify, create, cancel
    ) {}

    /**
     * 从请求数组创建DTO
     */
    public static function fromArray(array $data): self
    {
        return new self(
            roomCode: $data['roomCode'],
            rateCode: $data['rateCode'],
            adults: $data['adults'] ?? 1,
            children: $data['children'] ?? 0,
            childrenAges: $data['childrenAges'] ?? [],
            confirmationNumber: $data['confirmationNumber'] ?? null,
            guests: $data['guests'] ?? null,
            specialRequests: $data['specialRequests'] ?? null,
            status: $data['status'] ?? null
        );
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'roomCode' => $this->roomCode,
            'rateCode' => $this->rateCode,
            'adults' => $this->adults,
            'children' => $this->children,
            'childrenAges' => $this->childrenAges,
            'confirmationNumber' => $this->confirmationNumber,
            'guests' => $this->guests,
            'specialRequests' => $this->specialRequests,
            'status' => $this->status,
        ];
    }

    /**
     * 检查是否有儿童
     */
    public function hasChildren(): bool
    {
        return $this->children > 0;
    }

    /**
     * 检查是否有客人信息
     */
    public function hasGuests(): bool
    {
        return ! empty($this->guests);
    }

    /**
     * 验证儿童年龄数量是否与儿童数匹配
     */
    public function validateChildrenAges(): bool
    {
        if ($this->children === 0) {
            return empty($this->childrenAges);
        }

        return count($this->childrenAges) === $this->children;
    }

    /**
     * 检查房间操作类型
     */
    public function isModify(): bool
    {
        return $this->status === 'modify';
    }

    public function isCreate(): bool
    {
        return $this->status === 'create';
    }

    public function isCancel(): bool
    {
        return $this->status === 'cancel';
    }

    /**
     * 获取房间状态，如果未指定则默认为modify
     */
    public function getStatus(): string
    {
        return $this->status ?? 'modify';
    }

    /**
     * 验证房间状态是否有效
     */
    public function hasValidStatus(): bool
    {
        if ($this->status === null) {
            return true; // null状态是允许的，会默认为modify
        }

        return in_array($this->status, ['modify', 'create', 'cancel']);
    }
}
