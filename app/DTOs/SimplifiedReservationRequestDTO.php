<?php

namespace App\DTOs;

/**
 * 简化的预订请求DTO
 * 用于接收客户端的简化参数
 */
class SimplifiedReservationRequestDTO
{
    public function __construct(
        public readonly int $hotelId,
        public readonly string $checkInDate,
        public readonly string $checkOutDate,
        public readonly string $roomCode,
        public readonly string $rateCode,
        public readonly SimplifiedGuestDTO $primaryGuest,
        public readonly SimplifiedPaymentDTO $payment,
        public readonly int $numRooms = 1,
        public readonly int $adults = 1,
        public readonly int $children = 0,
        public readonly array $childrenAges = [],
        public readonly ?string $loyaltyNumber = null,
        public readonly ?string $loyaltyLevel = null,
        public readonly ?string $promoCode = null,
        public readonly ?string $specialRequests = null,
        public readonly bool $sendConfirmationEmail = false
    ) {}

    /**
     * 从请求数组创建DTO
     */
    public static function fromArray(array $data): self
    {
        return new self(
            hotelId: $data['hotelId'],
            checkInDate: $data['checkInDate'],
            checkOutDate: $data['checkOutDate'],
            roomCode: $data['roomCode'],
            rateCode: $data['rateCode'],
            primaryGuest: SimplifiedGuestDTO::fromArray($data['primaryGuest']),
            payment: SimplifiedPaymentDTO::fromArray($data['payment']),
            numRooms: $data['numRooms'] ?? 1,
            adults: $data['adults'] ?? 1,
            children: $data['children'] ?? 0,
            childrenAges: $data['childrenAges'] ?? [],
            loyaltyNumber: $data['loyaltyNumber'] ?? null,
            promoCode: $data['promoCode'] ?? null,
            specialRequests: $data['specialRequests'] ?? null,
            sendConfirmationEmail: $data['sendConfirmationEmail'] ?? false
        );
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'hotelId' => $this->hotelId,
            'checkInDate' => $this->checkInDate,
            'checkOutDate' => $this->checkOutDate,
            'roomCode' => $this->roomCode,
            'rateCode' => $this->rateCode,
            'primaryGuest' => $this->primaryGuest->toArray(),
            'payment' => $this->payment->toArray(),
            'numRooms' => $this->numRooms,
            'adults' => $this->adults,
            'children' => $this->children,
            'childrenAges' => $this->childrenAges,
            'loyaltyNumber' => $this->loyaltyNumber,
            'promoCode' => $this->promoCode,
            'specialRequests' => $this->specialRequests,
            'sendConfirmationEmail' => $this->sendConfirmationEmail,
        ];
    }

    /**
     * 检查是否有忠诚度会员信息
     */
    public function hasLoyalty(): bool
    {
        return ! empty($this->loyaltyNumber);
    }

    /**
     * 检查是否有促销代码
     */
    public function hasPromo(): bool
    {
        return ! empty($this->promoCode);
    }

    /**
     * 检查是否有儿童
     */
    public function hasChildren(): bool
    {
        return $this->children > 0;
    }

    /**
     * 转换为多房间格式（用于统一处理）
     * 将单房间请求转换为多房间请求格式
     */
    public function toMultiRoomFormat(?string $itineraryNumber = null): MultiRoomReservationRequestDTO
    {
        $roomData = [
            'roomCode' => $this->roomCode,
            'rateCode' => $this->rateCode,
            'adults' => $this->adults,
            'children' => $this->children,
            'childrenAges' => $this->childrenAges,
        ];

        $room = SimplifiedRoomRequestDTO::fromArray($roomData);

        return new MultiRoomReservationRequestDTO(
            itineraryNumber: $itineraryNumber ?? '',
            hotelId: $this->hotelId,
            checkInDate: $this->checkInDate,
            checkOutDate: $this->checkOutDate,
            rooms: [$room],
            primaryGuest: $this->primaryGuest,
            payment: $this->payment,
            loyaltyNumber: $this->loyaltyNumber,
            loyaltyLevel: $this->loyaltyLevel,
            promoCode: $this->promoCode,
            specialRequests: $this->specialRequests,
            sendConfirmationEmail: $this->sendConfirmationEmail
        );
    }
}
