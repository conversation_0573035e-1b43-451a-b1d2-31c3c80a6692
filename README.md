# GHA酒店预订系统


## 项目概述

GHA酒店预订系统是一个基于Laravel框架开发的酒店预订和管理平台，为Global Hotel Alliance (GHA)提供全面的酒店预订服务。该系统集成了Sabre Channel Connect C1服务，支持酒店查询、预订和管理功能。

## 核心功能

- **酒店搜索与预订**：支持按日期、人数、儿童数量等条件搜索酒店
- **会员忠诚度计划**：集成GHA忠诚度会员系统，支持会员等级和特权
- **促销和特别优惠**：支持促销代码和特别套餐
- **预订管理**：查询和管理现有预订
- **酒店详情**：提供酒店信息、设施、政策和支付方式

## 技术栈

- **前端**：Laravel Blade, Tailwind CSS, JavaScript
- **后端**：Laravel Framework
- **API**：Sabre Channel Connect C1, OSCP认证服务

## 安装与配置

### 系统要求

- PHP >= 8.2
- Composer
- MySQL 或其他Laravel支持的数据库
- Node.js 和 NPM（用于前端资源编译）

### 安装步骤

1. 克隆仓库
```bash
git clone [仓库URL]
cd [项目目录]
```

2. 安装依赖
```bash
composer install
npm install
```

3. 环境配置
```bash
cp .env.example .env
php artisan key:generate
```

4. 配置数据库连接（在.env文件中）

5. 运行数据库迁移
```bash
php artisan migrate
```

6. 编译前端资源
```bash
npm run dev
```

7. 启动服务器
```bash
php artisan serve
```

## API文档

系统集成了Sabre Channel Connect C1 API，主要端点包括：

- 认证：`/api/v3/auth/token`
- 酒店可用性查询：`/v1/api/hotel/availability`
- 预订创建：`/v1/api/reservation`
- 预订查询：`/v1/api/reservation?view=Full&channel=DSCVRYLYLTY&ItineraryNumber={itineraryNumber}`

详细API文档请参考Postman集合：`.docs/China Project Collection.postman_collection.json`

## 数据结构

项目包含以下主要数据集：
- 酒店优惠信息（`.docs/stayOffers.csv`）
- 品牌信息（`.docs/brands.csv`）

