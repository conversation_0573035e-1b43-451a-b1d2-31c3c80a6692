{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.47", "sass-embedded": "^1.89.2", "tailwindcss": "^3.4.13", "vite": "^6.0.11"}, "dependencies": {"antd": "^5.26.3", "mockjs": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "swiper": "^11.2.10", "tippy.js": "^6.3.7", "zustand": "^5.0.7"}}