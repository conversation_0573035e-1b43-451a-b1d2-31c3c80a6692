 <!DOCTYPE html>
<html lang="zh-CN" prefix="og: http://ogp.me/ns#">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width">
<link rel="profile" href="http://gmpg.org/xfn/11">
<link rel="pingback" href="https://www.english-heritage.cn/xmlrpc.php">
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5WJ7SZD');</script>
<!-- End Google Tag Manager -->
  <title> 英国遗产 </title>  
 <meta name="description" content="英国遗产作为非政府性质的行政公共机构，主要负责保护英国本地的部分历史遗迹，文化古迹，庄园建筑等。从著名的巨石阵、埃尔特姆宫、多佛城堡、哈德良长城等等为代表的400多项受保护的历史名胜遍布整个英格兰地区，为游客们提供了了解英国历史，领略英格兰风貌的原汁体验。 ">
<meta name="keywords" content="English Heritage, English Heritage China,  English Cultural attractions, English Castles, English Palaces, England tourism, UK tourism, Britain tourism, Great Britain tourism, English tourist attractions, Tourist attractions in England, Tourist attractions in the UK, British Tourism, Tourism UK, England Tourism, Tourist Attractions, Great Britain tourism, Tourist attractions in the UK,  Castles, Palaces, Historical Gardens, Roman Sites, Hadrian's Wall, Stonehenge, Dover Castle, Eltham Palace, Tintagle Castle, British Heritage, Visit England, Holiday in England, Holiday in the UK, Holiday in Britain, Destination England, Destination UK, English cultural attractions, English Culture, English History,  "> 





<title>欢迎来到ENGLISH HERITAGE中文网站</title>

<!-- This site is optimized with the Yoast SEO plugin v10.0.1 - https://yoast.com/wordpress/plugins/seo/ -->
<meta name="description" content="诚挚欢迎您的到来，在此您可以游览400余处名胜古迹，尽情欣赏英伦 风貌。您可以探索悠久历史，感悟历史事件，开启寻觅之旅。您还可 以欣赏 巨石阵 、中世 纪古堡 古罗马帝国时期的兵营等世界知名遗址 。漫步于缤纷多姿的 花园，流连于曾经的精美皇家宅邸。英格兰历史 建筑和古迹委员会愿您可以享受到原汁原味的不列颠之旅，我们的咖 啡美食，商铺礼物，将令您难以忘怀此次英伦之行。"/>
<link rel="canonical" href="https://www.english-heritage.cn/" />
<meta property="og:locale" content="zh_CN" />
<meta property="og:type" content="website" />
<meta property="og:title" content="欢迎来到ENGLISH HERITAGE中文网站" />
<meta property="og:description" content="诚挚欢迎您的到来，在此您可以游览400余处名胜古迹，尽情欣赏英伦 风貌。您可以探索悠久历史，感悟历史事件，开启寻觅之旅。您还可 以欣赏 巨石阵 、中世 纪古堡 古罗马帝国时期的兵营等世界知名遗址 。漫步于缤纷多姿的 花园，流连于曾经的精美皇家宅邸。英格兰历史 建筑和古迹委员会愿您可以享受到原汁原味的不列颠之旅，我们的咖 啡美食，商铺礼物，将令您难以忘怀此次英伦之行。" />
<meta property="og:url" content="https://www.english-heritage.cn/" />
<meta property="og:site_name" content="English Heritage China Website" />
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:description" content="诚挚欢迎您的到来，在此您可以游览400余处名胜古迹，尽情欣赏英伦 风貌。您可以探索悠久历史，感悟历史事件，开启寻觅之旅。您还可 以欣赏 巨石阵 、中世 纪古堡 古罗马帝国时期的兵营等世界知名遗址 。漫步于缤纷多姿的 花园，流连于曾经的精美皇家宅邸。英格兰历史 建筑和古迹委员会愿您可以享受到原汁原味的不列颠之旅，我们的咖 啡美食，商铺礼物，将令您难以忘怀此次英伦之行。" />
<meta name="twitter:title" content="欢迎来到ENGLISH HERITAGE中文网站" />
<meta name="twitter:site" content="@EnglishHeritage" />
<meta name="twitter:creator" content="@EnglishHeritage" />
<script type='application/ld+json'>{"@context":"https://schema.org","@type":"WebSite","@id":"https://www.english-heritage.cn/#website","url":"https://www.english-heritage.cn/","name":"English Heritage China Website","potentialAction":{"@type":"SearchAction","target":"https://www.english-heritage.cn/?s={search_term_string}","query-input":"required name=search_term_string"}}</script>
<script type='application/ld+json'>{"@context":"https://schema.org","@type":"Organization","url":"https://www.english-heritage.cn/","sameAs":["https://web.facebook.com/englishheritage/?_rdc=1&_rdr","https://www.instagram.com/englishheritage/?hl=en","https://www.youtube.com/user/EnglishHeritageFilm","https://twitter.com/EnglishHeritage"],"@id":"https://www.english-heritage.cn/#organization","name":"English Heritage","logo":"https://www.english-heritage.cn/wp-content/uploads/2020/06/English-Heritage-Logo.png"}</script>
<meta name="google-site-verification" content="WUnlftiQ5j-QfZFqT1HSPsqWWByclXFo4YsnRBTuRAU" />
<!-- / Yoast SEO plugin. -->

<link rel='dns-prefetch' href='//www.english-heritage.cn' />
<link rel='dns-prefetch' href='//fonts.googleapis.com' />
<link rel='dns-prefetch' href='//s.w.org' />
<link rel="alternate" type="application/rss+xml" title="English Heritage China Website &raquo; Feed" href="https://www.english-heritage.cn/feed/" />
<link rel="alternate" type="application/rss+xml" title="English Heritage China Website &raquo; 评论Feed" href="https://www.english-heritage.cn/comments/feed/" />
		<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/2.3\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/2.3\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/www.english-heritage.cn\/wp-includes\/js\/wp-emoji-release.min.js?ver=4.8.26"}};
			!function(t,a,e){var r,i,n,o=a.createElement("canvas"),l=o.getContext&&o.getContext("2d");function c(t){var e=a.createElement("script");e.src=t,e.defer=e.type="text/javascript",a.getElementsByTagName("head")[0].appendChild(e)}for(n=Array("flag","emoji4"),e.supports={everything:!0,everythingExceptFlag:!0},i=0;i<n.length;i++)e.supports[n[i]]=function(t){var e,a=String.fromCharCode;if(!l||!l.fillText)return!1;switch(l.clearRect(0,0,o.width,o.height),l.textBaseline="top",l.font="600 32px Arial",t){case"flag":return(l.fillText(a(55356,56826,55356,56819),0,0),e=o.toDataURL(),l.clearRect(0,0,o.width,o.height),l.fillText(a(55356,56826,8203,55356,56819),0,0),e===o.toDataURL())?!1:(l.clearRect(0,0,o.width,o.height),l.fillText(a(55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447),0,0),e=o.toDataURL(),l.clearRect(0,0,o.width,o.height),l.fillText(a(55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447),0,0),e!==o.toDataURL());case"emoji4":return l.fillText(a(55358,56794,8205,9794,65039),0,0),e=o.toDataURL(),l.clearRect(0,0,o.width,o.height),l.fillText(a(55358,56794,8203,9794,65039),0,0),e!==o.toDataURL()}return!1}(n[i]),e.supports.everything=e.supports.everything&&e.supports[n[i]],"flag"!==n[i]&&(e.supports.everythingExceptFlag=e.supports.everythingExceptFlag&&e.supports[n[i]]);e.supports.everythingExceptFlag=e.supports.everythingExceptFlag&&!e.supports.flag,e.DOMReady=!1,e.readyCallback=function(){e.DOMReady=!0},e.supports.everything||(r=function(){e.readyCallback()},a.addEventListener?(a.addEventListener("DOMContentLoaded",r,!1),t.addEventListener("load",r,!1)):(t.attachEvent("onload",r),a.attachEvent("onreadystatechange",function(){"complete"===a.readyState&&e.readyCallback()})),(r=e.source||{}).concatemoji?c(r.concatemoji):r.wpemoji&&r.twemoji&&(c(r.twemoji),c(r.wpemoji)))}(window,document,window._wpemojiSettings);
		</script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
<link rel='stylesheet' id='bingmaps-css'  href='https://www.english-heritage.cn/wp-content/plugins/bingmaps/js/bingmaps.js?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='vw-tour-lite-font-css'  href='//fonts.googleapis.com/css?family=PT+Sans%3A300%2C400%2C600%2C700%2C800%2C900%7CRoboto%3A400%2C700%7CRoboto+Condensed%3A400%2C700%7COxygen%3A400%2C700%7COpen+Sans%3A300%2C400%2C600%2C700%2C800%2C900&#038;ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='vw-tour-lite-style-css'  href='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/style.css?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='bootstrap-style-css'  href='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/css/bootstrap.css?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='effect-css'  href='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/css/effect.css?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='font-awesome-css'  href='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/css/font-awesome.css?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='gill-sans-light-css'  href='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/fonts/copyfonts.com_gill-sans-light.ttf?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='vw-tour-lite-customcss-css'  href='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/css/custom.css?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='vw-tour-lite-plugins-css'  href='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/css/style-plugins.css?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='nivo-style-css'  href='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/css/nivo-slider.css?ver=4.8.26' type='text/css' media='all' />
<link rel='stylesheet' id='msl-main-css'  href='https://www.english-heritage.cn/wp-content/plugins/master-slider/public/assets/css/masterslider.main.css?ver=3.5.8' type='text/css' media='all' />
<link rel='stylesheet' id='msl-custom-css'  href='https://www.english-heritage.cn/wp-content/uploads/master-slider/custom.css?ver=33.2' type='text/css' media='all' />
<script type='text/javascript' src='https://www.english-heritage.cn/wp-includes/js/jquery/jquery.js?ver=1.12.4'></script>
<script type='text/javascript' src='https://www.english-heritage.cn/wp-includes/js/jquery/jquery-migrate.min.js?ver=1.4.1'></script>
<script type='text/javascript' src='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/js/jquery.nivo.slider.js?ver=4.8.26'></script>
<script type='text/javascript' src='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/js/custom.js?ver=4.8.26'></script>
<link rel='https://api.w.org/' href='https://www.english-heritage.cn/wp-json/' />
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://www.english-heritage.cn/xmlrpc.php?rsd" />
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="https://www.english-heritage.cn/wp-includes/wlwmanifest.xml" /> 
<meta name="generator" content="WordPress 4.8.26" />
<link rel='shortlink' href='https://www.english-heritage.cn/' />
<link rel="alternate" type="application/json+oembed" href="https://www.english-heritage.cn/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fwww.english-heritage.cn%2F" />
<link rel="alternate" type="text/xml+oembed" href="https://www.english-heritage.cn/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fwww.english-heritage.cn%2F&#038;format=xml" />
<script>var ms_grabbing_curosr = 'https://www.english-heritage.cn/wp-content/plugins/master-slider/public/assets/css/common/grabbing.cur', ms_grab_curosr = 'https://www.english-heritage.cn/wp-content/plugins/master-slider/public/assets/css/common/grab.cur';</script>
<meta name="generator" content="MasterSlider 3.5.8 - Responsive Touch Image Slider | avt.li/msf" />
<style type="text/css">
.qtranxs_flag_en {background-image: url(https://www.english-heritage.cn/wp-content/plugins/qtranslate-x/flags/gb.png); background-repeat: no-repeat;}
.qtranxs_flag_zh {background-image: url(https://www.english-heritage.cn/wp-content/plugins/qtranslate-x/flags/cn.png); background-repeat: no-repeat;}
</style>
<link hreflang="en" href="https://www.english-heritage.cn/?lang=en" rel="alternate" />
<link hreflang="zh" href="https://www.english-heritage.cn/?lang=zh" rel="alternate" />
<link hreflang="x-default" href="https://www.english-heritage.cn/" rel="alternate" />
<meta name="generator" content="qTranslate-X *******" />
		<style type="text/css">.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}</style>
			<style type="text/css">
		
	</style>
	<style type="text/css" id="custom-background-css">
body.custom-background { background-color: #ffffff; }
</style>
<link rel="icon" href="https://www.english-heritage.cn/wp-content/uploads/2024/11/cropped-EHLogotype-Vibrant-Red-CMYK-icon-32x32.jpg" sizes="32x32" />
<link rel="icon" href="https://www.english-heritage.cn/wp-content/uploads/2024/11/cropped-EHLogotype-Vibrant-Red-CMYK-icon-192x192.jpg" sizes="192x192" />
<link rel="apple-touch-icon-precomposed" href="https://www.english-heritage.cn/wp-content/uploads/2024/11/cropped-EHLogotype-Vibrant-Red-CMYK-icon-180x180.jpg" />
<meta name="msapplication-TileImage" content="https://www.english-heritage.cn/wp-content/uploads/2024/11/cropped-EHLogotype-Vibrant-Red-CMYK-icon-270x270.jpg" />
		<style type="text/css" id="wp-custom-css">
			/*
You can add your own CSS here.

Click the help icon above to learn more.
*/

h4.table-txt {
    text-align: center;
}

h2.table-head {
    text-align: center;
    color: #d61130 !important;
    padding-top: 30px !important;
}
section#Price_table {
    background-color: #F9F9F5 !important;
}

.dover-transform1 a img {
    height: 370px;
}
.stone-map a img {
    width: 100%;
}
.Clifford-Tower{
height: auto;
margin-bottom: 100px;
}
.Clifford-Tower .post-image {
    float: none;
    padding: 0px 0px !important;
}
.dover-attraction {
    height: 650px !important;
}
#content-vw h5{
    text-transform: uppercase;
}
.packgbtn.btn-cs {
    margin-left: -15px !important;
}
.plan-cs {
    margin: 15px 0px;
}
		</style>
	</head>

<body class="home page-template-default page page-id-8 custom-background wp-custom-logo _masterslider _ms_version_3.5.8">



<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5WJ7SZD"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
  <div class="toggle"><a class="toggleMenu" href="#">Menu</a></div>
  
  <div id="header">
    <div class="container top-contact">
    <div class="top-bar">
<div class = "col-md-12">
      <div class= "col-md-4 envelope">
          <i class="fa fa-envelope" aria-hidden="true"></i> <EMAIL></div>
      <div class= "col-md-4 switch-icon">
      <div class="socialmedia">

<div class = "language-switcher">
<div class = "CountryFlagBackground"> 
 <style type="text/css">
.qtranxs_widget ul { margin: 0; }
.qtranxs_widget ul li
{
display: inline; /* horizontal list, use "list-item" or other appropriate value for vertical list */
list-style-type: none; /* use "initial" or other to enable bullets */
margin: 0 5px 0 0; /* adjust spacing between items */
opacity: 0.5;
-o-transition: 1s ease opacity;
-moz-transition: 1s ease opacity;
-webkit-transition: 1s ease opacity;
transition: 1s ease opacity;
}
/* .qtranxs_widget ul li span { margin: 0 5px 0 0; } */ /* other way to control spacing */
.qtranxs_widget ul li.active { opacity: 0.8; }
.qtranxs_widget ul li:hover { opacity: 1; }
.qtranxs_widget img { box-shadow: none; vertical-align: middle; display: initial; }
.qtranxs_flag { height:12px; width:18px; display:block; }
.qtranxs_flag_and_text { padding-left:20px; }
.qtranxs_flag span { display:none; }
</style>
<div class="widget qtranxs_widget">
<ul class="language-chooser language-chooser-custom qtranxs_language_chooser" id="qtranslate--1-chooser">
<li class="language-chooser-item language-chooser-item-en"><a href="https://www.english-heritage.cn/?lang=en" title="En (en)"><img src="https://www.english-heritage.cn/wp-content/uploads/2017/11/gb.png" alt="En (en)" /></a></li>
<li class="language-chooser-item language-chooser-item-zh active"><a href="https://www.english-heritage.cn/?lang=zh" title="中文 (zh)"><img src="https://www.english-heritage.cn/wp-content/uploads/2017/11/cn.png" alt="中文 (zh)" /></a></li>
</ul><div class="qtranxs_widget_end"></div>
</div> 

</div>
</div>


        
          <a href="https://www.youtube.com/user/EnglishHeritageFilm" target="_blank"><i class="fa fa-youtube" aria-hidden="true"></i></a>
        		
          <a href="https://www.instagram.com/englishheritage/?hl=en" target="_blank"><i class="fa fa-instagram" aria-hidden="true"></i></a>
          
                  <a href="https://web.facebook.com/englishheritage/?_rdc=1&#038;_rdr" target="_blank"><i class="fa fa-facebook" aria-hidden="true"></i></a>
                          <a href="https://twitter.com/EnglishHeritage" target="_blank"><img src="https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/images/twitter.png" width="30px" style="margin-bottom: 11px;" /></a>
                          <a href="#" data-toggle="modal" data-target="#myModal"><i class="fa fa-wechat" aria-hidden="true"></i></a>
                  <a href="https://weibo.com/u/7857164723" target="_blank"><i class="fa fa-weibo" aria-hidden="true"></i></a>
        </div>
      </div>
    </div>
    </div>
    </div><!-- top-bar -->
    <div class="container">
      <div class="logo col-md-3">
          <a href="https://www.english-heritage.cn/" class="custom-logo-link" rel="home" itemprop="url"><img width="240" height="86" src="https://www.english-heritage.cn/wp-content/uploads/2024/11/cropped-EHLogotype-Vibrant-Red-CMYK.png" class="custom-logo" alt="English Heritage China Website" itemprop="logo" /></a>      </div>
    
      <div class="nav col-md-9">
          <div class="menu-main-menu-container"><ul id="menu-main-menu" class="menu"><li id="menu-item-56" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-8 current_page_item menu-item-56"><a href="https://www.english-heritage.cn/">主页</a></li>
<li id="menu-item-1208" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-1208"><a href="#">访问</a>
<ul  class="sub-menu">
	<li id="menu-item-59" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-59"><a href="https://www.english-heritage.cn/places-to-visit/">参观景点</a></li>
	<li id="menu-item-60" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-60"><a href="https://www.english-heritage.cn/overseas-visitor-pass/">海外游客通行卡</a></li>
</ul>
</li>
<li id="menu-item-922" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-922"><a href="https://www.english-heritage.cn/our-places/">旗下景点</a>
<ul  class="sub-menu">
	<li id="menu-item-1676" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1676"><a href="https://www.english-heritage.cn/%e8%8b%b1%e5%9b%bd%e5%9b%ad%e6%9e%97/">英国园林</a></li>
	<li id="menu-item-1632" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1632"><a href="https://www.english-heritage.cn/castles/">英国城堡</a></li>
	<li id="menu-item-69" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-69"><a href="https://www.english-heritage.cn/roman-sites/">罗马时期遗址</a></li>
	<li id="menu-item-186" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-186"><a href="https://www.english-heritage.cn/mixed-inspiration/">多样精彩呈现</a></li>
</ul>
</li>
<li id="menu-item-885" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-885"><a href="https://www.english-heritage.cn/about-us/">关于我们</a>
<ul  class="sub-menu">
	<li id="menu-item-81" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-81"><a href="https://www.english-heritage.cn/general-enquiries/">常规问询</a></li>
	<li id="menu-item-82" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-82"><a href="https://www.english-heritage.cn/group-visits/">团队及同业参观</a></li>
</ul>
</li>
<li id="menu-item-83" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-83"><a target="_blank" href="https://www.english-heritageshop.org.uk">商店</a></li>
</ul></div>      </div><!-- nav -->
      <div class="clearfix"></div>
    </div>                           
  </div>




<div id="content-vw" class="container">
    <div class="middle-align">      
              
             
             
                <section>
                <div class="container">
                <div class="col-md-12">
                <div class = "main-desc">
                <div class="col-md-5">
                 <div class = "property-map">


 
     
                     <div class = "find-search">
九月 19, 2025
                
                <h3> 目的地攻略 </h3>
                
                
                    <div class = "search-place">
                        <form role="search" method="get" class="search-form" action="https://www.english-heritage.cn">
<input type="text" placeholder="请写地名" class="search-field" name = "s">

                        
                        <button class="btn read-more btn-outline-secondary search" type="submit"></button>    </form>
</div>




                </div>
</div>
                </div>

                <div class="col-md-7 ">
                <div class="event-img">
                <img src = "wp-content/uploads/2017/11/EH26326-resize.jpg" title= "StoneHenge" alt=""> <div class= "banner-desc-stone"> 
                 <h4><a class="text-white" href="/stonehenge">巨石阵</a></h4>


               </div>
                </div>
              <div class="col-md-12">
                <div class = "col-md-6 dover-img">

               <div class= "banner-desc"> 
                 <h4 style="margin-bottom: 0px;">海外游客通行卡</h4><a class="read-more btn btn-outline-secondary" href="/overseas-visitor-pass/">了解更多</a>


               </div>
                </div>
                <div class="col-md-6 aboutus-img">

               <div class= "banner-desc">

                <h4 style="margin-bottom: 0px;">关于我们</h4><a class="read-more btn btn-outline-secondary" href="https://www.english-heritage.cn/about-us/?lang=zh">了解更多</a>


                </div>
                </div>
                </div>                
                </div>
                </div>
                 </div>
                   </div>
                </section><br/><br/><br/>

<section id="tourtype1" style="background-image:url('wp-content/uploads/2017/10/image-3.jpg');height: 350px;">
        <div class="post innerlightbox">

                <h3 class="borderline-grey"><b>欢迎来到English Heritage中文网站！</b></h3>

          
        <div class="images_border">
            <img src="wp-content/uploads/2017/10/img-border.png" alt="divider">
        </div>     
    </div>      
    <div class="container">
        <div class="carousel slide row" data-ride="carousel" data-type="multi" data-interval="2000" id="fruitscarousel">
          <div class="row"> 

                <h6 class="textwhite" style="color:#fff; font-size:20px;">诚挚欢迎您的到来，在此您可以游览400余处名胜古迹，尽情欣赏英伦 风貌。您可以探索悠久历史，感悟历史事件，开启寻觅之旅。您还可 以欣赏 <a href="https://www.english-heritage.cn/stonehenge/?lang=en">巨石阵 </a>、中世 <a href="https://www.english-heritage.cn/dover-castle/?lang=en"> 纪古堡 </a>古罗马帝国时期的兵营等世界知名遗址 
。漫步于缤纷多姿的 <a href="https://www.english-heritage.cn/eltham-palace/">花园</a>，流连于曾经的精美皇家宅邸。英格兰历史 建筑和古迹委员会愿您可以享受到原汁原味的不列颠之旅，我们的咖 啡美食，<a href="https://www.english-heritageshop.org.uk/">商铺礼物</a>，将令您难以忘怀此次英伦之行。 </h6>



                        </div>
        </div>
    </div>
</section>
 


<section id="feature-tourbox" class="feature-tourbox stonehenge-attraction" >
        <div class="innerdarkbox">                                
        <div class="container">
                                
  
                        <div CLASS="row"> 
                                                <div class="post-box">             
                            <div class="post-image">
                                <a href="https://www.english-heritage.cn/wp-content/uploads/2017/11/stonehenge-1.jpg">
                                    <img width="650" height="460" src="https://www.english-heritage.cn/wp-content/uploads/2017/11/3-1.png" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" srcset="https://www.english-heritage.cn/wp-content/uploads/2017/11/3-1.png 560w, https://www.english-heritage.cn/wp-content/uploads/2017/11/3-1.png 300w" sizes="(max-width: 560px) 100vw, 560px">                              </a>
                            </div>
                        </div>
                        <div class="post-box bigpost-img logobox stonehenge">

<h5>体验巨石阵</h5>
<p id= "description">参观世界上最令人印象深刻的奇迹之一--巨石阵。领略巨石阵的故事，让这一世界知名的遗址给你带来最生动的史前文化体验。</p>
                            <div class="packgbtn button">                                
                               
                                    <a class="read-more btn btn-outline-secondary" href="https://www.english-heritage.cn/stonehenge/?lang=zh">了解更多</a>
                                
                            </div>


                                      
  
                            

                        </div>             

                    </div>
                                            <div class="clearfix"></div>
        </div>
    </div>
</section>

<section id="feature-tourbox" class="feature-tourbox hardian-attraction" >
        <div class="innerdarkbox">
        <div class="container">
                                <div CLASS="hardianrow button">

                                                        <div class="post-box">             
                            <div class="post-image">
                                <a href="https://www.english-heritage.cn/wp-content/uploads/2017/11/4.png">
                                    <img width="650" height="460" src="https://www.english-heritage.cn/wp-content/uploads/2017/11/4.png" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" srcset="https://www.english-heritage.cn/wp-content/uploads/2017/11/4.png 560w, https://www.english-heritage.cn/wp-content/uploads/2017/11/4.png 300w" sizes="(max-width: 560px) 100vw, 560px">                              </a>
                            </div>
                        </div> 
                        

                        <div class="post-box bigpost-img logobox hardian">

                           <h5>哈德良长城</h5>
                               <p id= "description">哈德良长城是著名的世界文化遗产，沿着英格兰最天然也最引人注目的乡村地区建成，总长73英里左右。游览哈德良长城，探索罗马帝国时期的生活。</p>
                            <div class="packgbtn button">                                
                                
                                    <a class="read-more btn btn-outline-secondary" href="https://www.english-heritage.cn/%E5%93%88%E5%BE%B7%E8%89%AF%E9%95%BF%E5%9F%8E/">了解更多</a>
                                
                            </div>


                            
   
  

                        </div> 



                    </div>
                                            <div class="clearfix"></div>
        </div>
    </div>
</section> 

<section id="feature-tourbox" class="feature-tourbox dover-attraction">
        <div class="innerdarkbox">
        <div class="container">
                                <div CLASS="doverrow button">

                        <div class="post-box">             
                            <div class="post-image">
                                <a href="https://www.english-heritage.cn/wp-content/uploads/2017/11/kings-chamber.jpg">
                                    <img width="650" height="460" src="https://www.english-heritage.cn/wp-content/uploads/2017/11/2-1.png" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" srcset="https://www.english-heritage.cn/wp-content/uploads/2017/11/2-1.png 560w, https://www.english-heritage.cn/wp-content/uploads/2017/11/2-1.png 300w" sizes="(max-width: 560px) 100vw, 560px">                              </a>
                            </div>
                        </div>

                        <div class="post-box bigpost-img logobox dover">


                                                    <h5>多佛城堡</h5>
                              
                            <p id= "description">多佛城堡是最具标志性的英国城堡，位于多佛白崖之上，被称为“大不列颠之门”。在这里，你可以攀爬多佛巨塔、“见到”英国最强大的国王之一、饱览秀丽风光、探寻中世纪宫殿生动有趣的娱乐方式。</p>
                            <div class="packgbtn button">                                
                                
                                    <a class="read-more btn btn-outline-secondary" href="https://www.english-heritage.cn/dover-castle/?lang=zh">了解更多</a>
                                
                            </div>




                        </div>                

                    </div>
                                            <div class="clearfix"></div>
        </div>
    </div>
</section> 
<section id="feature-tourbox" class="feature-tourbox eltham-attraction" >
        <div class="innerdarkbox">
        <div class="container">
                                <div CLASS="elthamrow button">
                       <div class="post-box">             
                            <div class="post-image">
                                <a href="https://www.english-heritage.cn/wp-content/uploads/2017/11/Eltham-Palace-and-Gardens-Exterior-2.jpg">
                                    <img width="650" height="460" src="https://www.english-heritage.cn/wp-content/uploads/2017/11/1-1.png" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" srcset="https://www.english-heritage.cn/wp-content/uploads/2017/11/1-1.png 560w, https://www.english-heritage.cn/wp-content/uploads/2017/11/1-1.png 300w" sizes="(max-width: 560px) 100vw, 560px">                              </a>
                            </div>
                        </div>

                        <div class="post-box bigpost-img logobox eltham">

                              <h5>埃尔特姆宫</h5>                           
  
                            <p id= "description">参观埃尔特姆宫，体验英国颓废装饰艺术，让你仿佛回到了20世纪30年代（正巧赶上鸡尾酒世纪！）。20世纪30年代，百万富翁Stephen和Virginia Courtauld将埃尔特姆宫进行了彻底翻新，也使得它成了英国装饰艺术中的最佳典范，展示了1930年代的顶级装饰技术。</p>
                            <div class="packgbtn btn-cs button">                                
                                
                                    <a class="read-more btn btn-outline-secondary" href="https://www.english-heritage.cn/eltham-palace/?lang=zh">了解更多</a>
                                
                            </div>



                           
                        </div>                
                        
                    </div>
                                            <div class="clearfix"></div>
        </div>
    </div>
</section> 
<section id="feature-tourbox" class="feature-tourbox dover-attraction tintagel-attraction">
        <div class="innerdarkbox">
        <div class="container">
                                <div CLASS="doverrow button">

                        <div class="post-box">             
                            <div class="post-image tintagel-img">
                                <a href="https://www.english-heritage.cn/wp-content/uploads/2017/11/kings-chamber.jpg">
                                    <img width="650" height="460" src="https://www.english-heritage.cn/wp-content/uploads/2017/11/EH1433.jpg" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" srcset="https://www.english-heritage.cn/wp-content/uploads/2017/11/EH1433.jpg 560w, https://www.english-heritage.cn/wp-content/uploads/2017/11/EH1433.jpg 300w" sizes="(max-width: 560px) 100vw, 560px">                              </a>
                            </div>
                        </div>

                        <div class="post-box bigpost-img logobox tintagel-castle-desc">


                                                    <h5>廷塔杰尔城堡</h5>
                              
                            <p id= "description">廷塔杰尔城堡位于康沃尔郡帕德斯托和比德之间的北部海岸上。来到这儿，你可以尽情沉浸在廷塔杰尔城堡的历史、神话以及迷人的风景里。据传，这里是亚瑟王的出生地，因而多个世纪以来，这座引人注目的城堡以及海岸线也就成了众多作家、艺术家，甚至是国王的亲弟弟的灵感源泉。现在，该轮到您来被启发下了。</p>
                            <div class="packgbtn button">                                
                                
                                    <a class="read-more btn btn-outline-secondary" href="https://www.english-heritage.cn/tintagel-castle/">了解更多</a>
                                
                            </div>




                        </div>                

                    </div>
                                            <div class="clearfix"></div>
        </div>
    </div>
</section>
<section id="feature-tourbox" class="feature-tourbox Clifford-Tower" >
        <div class="innerdarkbox">
        <div class="container">
                                <div CLASS="elthamrow button">
                       <div class="post-box">             
                            <div class="post-image">
                                <a href="https://www.english-heritage.cn/wp-content/uploads/2019/04/the-tower-today.jpg">
                                    <img width="650" height="460" src="https://www.english-heritage.cn/wp-content/uploads/2019/04/the-tower-today.jpg" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" srcset="https://www.english-heritage.cn/wp-content/uploads/2019/04/the-tower-today.jpg 560w, https://www.english-heritage.cn/wp-content/uploads/2019/04/the-tower-today.jpg 300w" sizes="(max-width: 560px) 100vw, 560px">                              </a>
                            </div>
                        </div>

                        <div class="post-box bigpost-img logobox eltham">

                              <h5>约克克利福德塔</h5>                           
  
                            <p id= "description">如今，克利福德塔新建的塔顶观景台可以使游客俯瞰历史悠久的约克，欣赏其中世纪建筑和著名的约克大教堂无与伦比的美景。塔内部加建了全新的步道和台阶，这使得那些在几个世纪中一直限制进入的区域得以对公众开放。沉浸式音景和故事将给你带来前所未有的体验，使你切身体会这座塔动荡的历史。</p>
                            <div class="packgbtn btn-cs button">                                
                                
                                    <a class="read-more btn btn-outline-secondary" href="https://www.english-heritage.cn/cliffords-tower-york">了解更多</a>
                                
                            </div>



                           
                        </div>                
                        
                    </div>
                                            <div class="clearfix"></div>
        </div>
    </div>
    <div style="margin-top:70px; display:none">
        <h2 class="borderline-grey"> Summer Solstice at Stonehenge 2020</h2>
                                                          
  
                            <p id= "description">数千年来，巨石阵一直是夏至时的礼拜和庆祝场所。今年我们可能无法亲自接待客人，但我们会把这个特别的时刻带到您家里。我们将直播日落和日出，确保您不会错过任何一刻。6月20日下午21:26日落，6月21日凌晨04:52日出。</p>
                            <div class="packgbtn btn-cs button">                                
                                
                                    <a href="https://www.youtube.com/user/EnglishHeritageFilm?utm_campaign=1749966_%22Days%20Out%22%20Newsletter%20June&utm_medium=email&utm_source=English%20Heritage%20Non%20Member&utm_content=YouTube">https://www.youtube.com/user/EnglishHeritageFilm?utm_campaign=1749966_%22Days%20Out%22%20Newsletter%20June&utm_medium=email&utm_source=English%20Heritage%20Non%20Member&utm_content=YouTube</a>
                                
                            </div>

    </div>
</section>
 
 <section id="feature-tourbox" class="feature-tourbox dover-attraction tintagel-attraction">
        <div class="innerdarkbox">
        <div class="container">
                                <div CLASS="doverrow button">

                        <div class="post-box">             
                            <div class="post-image tintagel-img">
                                <a href="https://www.english-heritage.cn/wp-content/uploads/2023/07/whitby-Abbey_j850263.jpg">
                                    <img width="650" height="460" src="https://www.english-heritage.cn/wp-content/uploads/2023/07/whitby-Abbey_j850263.jpg" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" srcset="https://www.english-heritage.cn/wp-content/uploads/2023/07/whitby-Abbey_j850263.jpg 560w, https://www.english-heritage.cn/wp-content/uploads/2023/07/whitby-Abbey_j850263.jpg 300w" sizes="(max-width: 560px) 100vw, 560px">                              </a>
                            </div>
                        </div>

                        <div class="post-box bigpost-img logobox tintagel-castle-desc">


                                                    <h5>惠特比修道院</h5>
                              
                            <p id= "description">惠特比修道院在1500多年间一直吸引着大量的游客。你可以在探索高耸残缺的哥特式建筑的同时，追寻那些圣人、诗人还有“吸血鬼”的故事，并尽情欣赏海港的壮丽景色。</p>
                            <div class="packgbtn button">                                
                                
                                    <a class="read-more btn btn-outline-secondary" href="https://www.english-heritage.cn/whitby-abbey/">了解更多</a>
                                
                            </div>




                        </div>                

                    </div>
                                            <div class="clearfix"></div>
        </div>
    </div>
</section>
 
                    

		<!-- MasterSlider -->
		<div id="P_MS68cd356ee5827" class="master-slider-parent msl ms-parent-id-7"  >

			
			<!-- MasterSlider Main -->
			<div id="MS68cd356ee5827" class="master-slider ms-skin-default" >
				 				 
				<div  class="ms-slide" data-delay="3" data-fill-mode="fill"  >
					<img src="https://www.english-heritage.cn/wp-content/plugins/master-slider/public/assets/css/blank.gif" alt="" title="" data-src="https://www.english-heritage.cn/wp-content/uploads/2017/10/soveriegnsgateholiday.jpg" />

					<div class="ms-info"><div class="slider-info"><h2 id="slide-desc"><span style="color: #000000;">唤醒你的门槛</span><br /><span style="color: #000000;">英国旅游</span></h2><a class=" btndesc-1 read-more btn btn-outline-secondary" href="http://www.english-heritage.org.uk/visit/holiday-cottages/"><b>预订下榻地点</b></a></div></div>


	<div class="ms-thumb" ><div class="ms-tab-context"><div class=&quot;ms-tab-context&quot;></div></div>
</div>
				</div>
				<div  class="ms-slide" data-delay="3" data-fill-mode="fill"  >
					<img src="https://www.english-heritage.cn/wp-content/plugins/master-slider/public/assets/css/blank.gif" alt="" title="" data-src="https://www.english-heritage.cn/wp-content/uploads/2017/10/Carousel-Couple-Hall-1.jpg" />

					<div class="ms-info"><div class="slider-info"><h2 id="slide-desc"><span style="color: #000000;">鼓舞人心的场地为您的完美婚礼</span><br /><span style="color: #000000;">英国旅游</span></h2><a class="btndesc-2 read-more btn btn-outline-secondary" href="http://www.english-heritage.org.uk/visit/venue-hire/weddings/"><b>保持联系</b></a></div></div>


	<div class="ms-thumb" ><div class="ms-tab-context"><div class=&quot;ms-tab-context&quot;></div></div>
</div>
				</div>
				<div  class="ms-slide" data-delay="3" data-fill-mode="fill"  >
					<img src="https://www.english-heritage.cn/wp-content/plugins/master-slider/public/assets/css/blank.gif" alt="" title="" data-src="https://www.english-heritage.cn/wp-content/uploads/2017/10/Retail-Banner-Special-Offers.jpg" />

					<div class="ms-info"><div class="slider-info"><h2 id="slide-desc"><span style="color: #000000;">来自各个历史时代的礼物</span><br /><span style="color: #000000;">英国旅游</span></h2><a class="btndesc-3 read-more btn btn-outline-secondary" href="https://www.english-heritageshop.org.uk/"><b> 探索我们的商店</b></a></div></div>


	<div class="ms-thumb" ><div class="ms-tab-context"><div class=&quot;ms-tab-context&quot;></div></div>
</div>
				</div>

			</div>
			<!-- END MasterSlider Main -->

			 
		</div>
		<!-- END MasterSlider -->

		<script>
		(function ( $ ) {
			"use strict";

			$(function () {
				var masterslider_5827 = new MasterSlider();

				// slider controls
				masterslider_5827.control('bullets'    ,{ autohide:true, overVideo:true, dir:'h', align:'bottom' , margin:10  });

				masterslider_5827.control('slideinfo'  ,{ autohide:false, overVideo:true, dir:'h', align:'top',inset:true , margin:75   });
				// slider setup
				masterslider_5827.setup("MS68cd356ee5827", {
						width           : 1000,
						height          : 500,
						minHeight       : 0,
						space           : 0,
						start           : 1,
						grabCursor      : true,
						swipe           : true,
						mouse           : true,
						layout          : "fullwidth",
						wheel           : false,
						autoplay        : true,
						instantStartLayers:false,
						loop            : false,
						shuffle         : false,
						preload         : 0,
						heightLimit     : true,
						autoHeight      : true,
						smoothHeight    : true,
						endPause        : false,
						overPause       : true,
						fillMode        : "fill",
						centerControls  : true,
						startOnAppear   : false,
						layersMode      : "center",
						hideLayers      : false,
						fullscreenMargin: 0,
						speed           : 20,
						dir             : "h",
						parallaxMode    : 'swipe',
						view            : "basic"
				});

				
				window.masterslider_instances = window.masterslider_instances || [];
				window.masterslider_instances.push( masterslider_5827 );
			 });

		})(jQuery);
		</script>





                               
                        
               
        <div class="clear"></div>     
    </div>
</div><!-- container -->


        <div class="strapimg">
        <img src = "http://www.english-heritage.cn/wp-content/uploads/2017/10/StoryofEngland.gif" width= "100%" alt="">
        </div>
        <div class="footersec">

            <div class="container">
                <div class="row">
<div class = "col-md-12">
                    <div class="col-md-4">
                                            </div>
                    <div class="col-md-4">
                                            </div>
                    <div class="col-md-4">
                                            </div>

                    </div>

                </div>
            </div>
        </div>


                    
      
        <div class="copyright-wrapper">
        	<div class="container">
                <!--<div class="footer-menu">
                    <div class="menu-footer-main-menu-container"><ul id="menu-footer-main-menu" class="menu"><li id="menu-item-389" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-389"><a href="https://www.english-heritage.cn/cookies/">Cookie 政策</a></li>
<li id="menu-item-388" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-388"><a href="https://www.english-heritage.cn/privacy-policy/">隐私政策</a></li>
<li id="menu-item-387" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-387"><a href="https://www.english-heritage.cn/terms-conditions/">条款&#038;条件</a></li>
</ul></div>                </div>--><!-- footer-menu -->
                <div class="clear"></div> 
                <div class="copyright">
                <div id="top" class="copyright text-center">
               <a href="#header" ><img class= "footer-divider" src = "http://www.english-heritage.cn/wp-content/uploads/2017/10/footer-icon.png" alt=""></a><br/><br/>
               <div class="menu-footer-main-menu-container"><ul id="menu-footer-main-menu-1" class="menu"><li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-389"><a href="https://www.english-heritage.cn/cookies/">Cookie 政策</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-388"><a href="https://www.english-heritage.cn/privacy-policy/">隐私政策</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-387"><a href="https://www.english-heritage.cn/terms-conditions/">条款&#038;条件</a></li>
</ul></div>                   <p>© English Heritage 2025, All Rights Reserved | <a href= "http://sinosolutions.cn/" > Site Credits</a> | <a href= "http://www.english-heritage.cn/sitemap.xml"> Sitemap</a> </p>
                   </div>
                </div><!-- copyright -->
            </div><!-- inner -->
        </div>
        
<script type='text/javascript' src='https://www.english-heritage.cn/wp-content/themes/vw-tour-lite/js/bootstrap.js?ver=4.8.26'></script>
<script type='text/javascript' src='https://www.english-heritage.cn/wp-includes/js/wp-embed.min.js?ver=4.8.26'></script>
<script type='text/javascript' src='https://www.english-heritage.cn/wp-content/plugins/master-slider/public/assets/js/jquery.easing.min.js?ver=3.5.8'></script>
<script type='text/javascript' src='https://www.english-heritage.cn/wp-content/plugins/master-slider/public/assets/js/masterslider.min.js?ver=3.5.8'></script>
<!-- Modal -->
  <div class="modal" id="myModal" role="dialog">
    <div class="modal-dialog modal-sm">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title"> Scan for We chat QR</h4>
        </div>
        <div class="modal-body">
         <img src="/wp-content/themes/vw-tour-lite/images/wechat.jpg" alt=""/>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
<!-- Modal -->

</body>
</html>