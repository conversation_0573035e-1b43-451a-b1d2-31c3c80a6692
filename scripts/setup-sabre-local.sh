#!/bin/bash

# Sabre API 本地开发环境设置脚本
# 使用方法: bash scripts/setup-sabre-local.sh

echo "🚀 设置 Sabre API 本地开发环境..."
echo ""

# 检查 .env 文件是否存在
if [ ! -f .env ]; then
    echo "❌ .env 文件不存在，请先复制 .env.example 到 .env"
    exit 1
fi

# 检查是否已经配置了 Sabre 相关环境变量
if grep -q "SABRE_USERNAME" .env; then
    echo "⚠️  检测到 .env 文件中已存在 Sabre 配置"
    read -p "是否要覆盖现有配置？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "取消设置"
        exit 0
    fi
fi

echo "📝 请输入 Sabre API 配置信息："
echo ""

# 获取用户输入
read -p "Sabre 用户名: " SABRE_USERNAME
read -s -p "Sabre 密码: " SABRE_PASSWORD
echo ""
read -p "Sabre API Key: " SABRE_API_KEY
read -p "Chain ID (默认: 32446): " SABRE_CHAIN_ID
SABRE_CHAIN_ID=${SABRE_CHAIN_ID:-32446}

echo ""
echo "🔧 正在更新 .env 文件..."

# 移除现有的 Sabre 配置（如果存在）
sed -i.bak '/^SABRE_/d' .env

# 添加新的 Sabre 配置
cat >> .env << EOF

# Sabre API Configuration
SABRE_AUTH_URL=https://oscp.stage.ghaloyalty.com/api/v3/auth/token
SABRE_API_URL=https://services-c1.synxis.com
SABRE_USERNAME=${SABRE_USERNAME}
SABRE_PASSWORD=${SABRE_PASSWORD}
SABRE_API_KEY=${SABRE_API_KEY}

# Sabre Default Settings
SABRE_PRIMARY_CHANNEL=SYDC
SABRE_SECONDARY_CHANNEL=DSCVRYLYLTY
SABRE_CHAIN_ID=${SABRE_CHAIN_ID}
SABRE_CONTEXT=WBSVC
SABRE_LANGUAGE=zh-CN
SABRE_ENTRY_CHANNEL_CODE=GHA
SABRE_SUB_SOURCE_CODE=GHA
SABRE_MARKET_SOURCE_CODE=GHA
SABRE_LOYALTY_PROGRAM=GHA

# Sabre Timeout Settings
SABRE_CONNECT_TIMEOUT=10
SABRE_REQUEST_TIMEOUT=30

# Sabre Retry Settings
SABRE_MAX_RETRY_ATTEMPTS=3
SABRE_RETRY_DELAY=1000

# Sabre Cache Settings (本地开发禁用缓存)
SABRE_CACHE_ENABLED=false
SABRE_TOKEN_CACHE_TTL=3300
SABRE_AVAILABILITY_CACHE_TTL=300
SABRE_HOTEL_DETAILS_CACHE_TTL=3600

# Sabre Logging
SABRE_LOGGING_ENABLED=true
SABRE_LOG_LEVEL=info
SABRE_LOG_CHANNEL=daily

# Sabre Test Mode
SABRE_TEST_MODE=false
SABRE_MOCK_RESPONSES=false
EOF

echo "✅ .env 文件已更新"
echo ""

# 检查是否需要发布配置文件
if [ ! -f config/sabre.php ]; then
    echo "📦 发布 Sabre 配置文件..."
    php artisan vendor:publish --tag=sabre-config --force
    echo "✅ 配置文件已发布"
else
    echo "ℹ️  Sabre 配置文件已存在，跳过发布"
fi

echo ""
echo "🧪 测试 Sabre API 连接..."
php artisan sabre:test --auth

echo ""
echo "🎉 Sabre API 本地开发环境设置完成！"
echo ""
echo "📚 接下来你可以："
echo "   1. 运行完整测试: php artisan sabre:test"
echo "   2. 查看文档: docs/SABRE_API_INTEGRATION.md"
echo "   3. 查看儿童年龄处理: docs/CHILDREN_AGE_HANDLING.md"
echo "   4. 运行单元测试: php artisan test tests/Feature/SabreServiceTest.php"
echo ""
echo "💡 提示："
echo "   - 本地开发环境已禁用缓存 (SABRE_CACHE_ENABLED=false)"
echo "   - 生产环境请设置 SABRE_CACHE_ENABLED=true"
echo "   - 儿童年龄格式: 单个儿童 '8', 多个儿童 '6,10', 多房间 '6,10;8'"
