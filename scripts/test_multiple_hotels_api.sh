#!/bin/bash

# 批量查询多个酒店最佳价格API测试脚本

# 设置API基础URL
BASE_URL="http://localhost:8000/api/v1"
API_ENDPOINT="$BASE_URL/sabre/multipleHotelsBestPrices"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 批量查询多个酒店最佳价格API测试 ===${NC}"
echo ""

# 测试1: 基本查询
echo -e "${YELLOW}测试1: 基本查询${NC}"
echo "查询3个酒店的最佳价格..."

curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "hotelIds": [100823, 100824, 100825],
    "startDate": "2025-07-09",
    "endDate": "2025-07-11",
    "numRooms": 1,
    "adults": 2
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s | jq '.' 2>/dev/null || echo "响应格式不是有效的JSON"

echo ""
echo "---"
echo ""

# 测试2: 带儿童的查询
echo -e "${YELLOW}测试2: 家庭出行查询（带儿童）${NC}"
echo "查询2个酒店，包含儿童信息..."

curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "hotelIds": [100823, 100824],
    "startDate": "2025-08-15",
    "endDate": "2025-08-18",
    "numRooms": 2,
    "adults": 4,
    "children": 2,
    "childrenAge": "8,12"
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s | jq '.' 2>/dev/null || echo "响应格式不是有效的JSON"

echo ""
echo "---"
echo ""

# 测试3: 带促销代码的查询
echo -e "${YELLOW}测试3: 促销价格查询${NC}"
echo "查询5个酒店的促销价格..."

curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "hotelIds": [100823, 100824, 100825, 100826, 100827],
    "startDate": "2025-09-01",
    "endDate": "2025-09-03",
    "numRooms": 1,
    "adults": 2,
    "accessCode": "AUTUMN2025"
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s | jq '.' 2>/dev/null || echo "响应格式不是有效的JSON"

echo ""
echo "---"
echo ""

# 测试4: 参数验证失败测试
echo -e "${YELLOW}测试4: 参数验证失败测试${NC}"
echo "发送无效参数，测试验证机制..."

curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "startDate": "2025-07-09",
    "endDate": "2025-07-11"
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s | jq '.' 2>/dev/null || echo "响应格式不是有效的JSON"

echo ""
echo "---"
echo ""

# 测试5: 酒店数量超限测试
echo -e "${YELLOW}测试5: 酒店数量超限测试${NC}"
echo "发送超过20个酒店ID，测试限制机制..."

curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "hotelIds": [100001, 100002, 100003, 100004, 100005, 100006, 100007, 100008, 100009, 100010, 100011, 100012, 100013, 100014, 100015, 100016, 100017, 100018, 100019, 100020, 100021, 100022, 100023, 100024, 100025],
    "startDate": "2025-07-09",
    "endDate": "2025-07-11",
    "numRooms": 1,
    "adults": 2
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s | jq '.' 2>/dev/null || echo "响应格式不是有效的JSON"

echo ""
echo "---"
echo ""

# 测试6: 日期验证测试
echo -e "${YELLOW}测试6: 日期验证测试${NC}"
echo "发送过去的日期，测试日期验证..."

curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "hotelIds": [100823, 100824],
    "startDate": "2024-01-01",
    "endDate": "2024-01-03",
    "numRooms": 1,
    "adults": 2
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s | jq '.' 2>/dev/null || echo "响应格式不是有效的JSON"

echo ""
echo "---"
echo ""

# 性能测试
echo -e "${YELLOW}性能测试: 查询10个酒店${NC}"
echo "测试批量查询性能..."

start_time=$(date +%s.%N)

curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "hotelIds": [100823, 100824, 100825, 100826, 100827, 100828, 100829, 100830, 100831, 100832],
    "startDate": "2025-10-01",
    "endDate": "2025-10-03",
    "numRooms": 1,
    "adults": 2
  }' \
  -w "\n状态码: %{http_code}\n" \
  -s -o /dev/null

end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)

echo -e "${GREEN}查询10个酒店耗时: ${duration}秒${NC}"

echo ""
echo -e "${BLUE}=== 测试完成 ===${NC}"
echo ""
echo -e "${GREEN}使用说明:${NC}"
echo "1. 确保Laravel应用正在运行 (php artisan serve)"
echo "2. 确保数据库连接正常"
echo "3. 确保Sabre API配置正确"
echo "4. 如需查看详细响应，可以移除 '-s' 参数"
echo ""
echo -e "${YELLOW}注意事项:${NC}"
echo "- 测试使用的酒店ID可能不存在，这是正常的"
echo "- 实际响应取决于Sabre API的可用性"
echo "- 某些测试可能会因为网络或API限制而失败"
