import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react'
import {sync} from "glob";

export default defineConfig({
    plugins: [
        laravel({
            input: [
                ...sync("resources/css/**/*.{css,scss}").filter(e => !e.includes("_")), 
                ...sync("resources/js/**/*.{js,jsx}").filter(e => !e.includes("_")),
            ],
            refresh: true,
        }),
        react(),
    ],
    resolve: {
        alias: {
            '@images': '/resources/images',
        },
    },
    server: {
        port: 5179
    }
});
