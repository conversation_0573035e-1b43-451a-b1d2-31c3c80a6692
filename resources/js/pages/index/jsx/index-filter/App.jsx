import { Input, DatePicker, Config<PERSON><PERSON>ider, Popover, InputNumber } from "antd"
import PeopleSelectPopover from "./parts/PeopleSelectPopover"
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { useEffect, useMemo, useState } from "react";
dayjs.locale('zh-cn');

const { RangePicker } = DatePicker;

export default function IndexFilterApp() {
    const [rooms, setRooms] = useState([{adult: 2, child: 0, childAges: []}])
    const formattedPeople = useMemo(() => {
        const adults = rooms.reduce((acc, cur) => acc + cur.adult, 0);
        const children = rooms.reduce((acc, cur) => acc + cur.child, 0);
        return `${adults}成人 ${children > 0 ? `${children}儿童` : ""} ${rooms.length}房间`
    }, [rooms])
    useEffect(() => {
        const targetElement = document.querySelector('.filter-bar-wrap');
        const resizeObserver = new ResizeObserver((entries) => {
            for (let entry of entries) {
                const height = entry.contentRect.height;
                document.querySelector('.filter-bar-wrap-holder').style.height = `${height}px`;
            }
        });
        resizeObserver.observe(targetElement);
    }, [])
    useEffect(() => {
        const sentinel = document.querySelector('.filter-bar-wrap-holder');
        const target = document.querySelector('.filter-bar-wrap');
        window.addEventListener('scroll', () => {
            const rect = sentinel.getBoundingClientRect();
            if (rect.top < 90) {
                target.classList.add('gha-fixed');
            } else {
                target.classList.remove('gha-fixed');
            }
        })
    }, [])
    function onRoomChange(roomIdx, type, val, ageIdx) {
        const updateRooms = [...rooms]
        if (type === 'adult') {
            updateRooms[roomIdx].adult = val;
        }
        if (type === 'child') {
            if (val > updateRooms[roomIdx].child) {
                updateRooms[roomIdx].childAges.push(0)
            } else {
                updateRooms[roomIdx].childAges.pop()
            }
            updateRooms[roomIdx].child = val;
        }
        if (type === "age") {
            updateRooms[roomIdx].childAges[ageIdx] = val;
        }
        // updateRooms[roomIdx]
        console.error(roomIdx, type, val, ageIdx)
        setRooms(updateRooms)
    }
    function onRoomAdd() {
        setRooms(prev => [...prev, {adult: 1, child: 0, childAges: []}])
    }
    function onRoomRemove() {

    }
    return (
        <>
            <div className="filter-item keyword">
                <i className="iconfont icon-Location"></i>
                <Input variant="borderless" placeholder="品牌/酒店/国家/城市"/>
            </div>
            <div className="filter-item date">
                <i className="iconfont icon-Calendar"></i>
                <ConfigProvider locale={locale}>
                    <RangePicker variant="borderless" placement="bottomLeft" suffixIcon={null}/>
                </ConfigProvider>
            </div>
            <Popover 
                content={(
                    <PeopleSelectPopover
                        rooms={rooms} 
                        onRoomAdd={onRoomAdd} 
                        onRoomRemove={onRoomRemove} 
                        onRoomChange={onRoomChange}
                    />
                )} 
                trigger="click" 
                arrow={false} 
                placement="bottomLeft"
                rootClassName="people-select-popover-root"
                // getPopupContainer={trigger => trigger}
            >
                <div className="filter-item people">
                    <i className="iconfont icon-Family"></i>
                    <a href="javascript:;">{ formattedPeople }</a>
                </div>
            </Popover>
            <div className="filter-item reward">
                <i className="iconfont icon-Award"></i>
                <Input variant="borderless" placeholder="促销码"/>
            </div>
            <div className="submit-wrap">查找酒店</div>
        </>
    )
}