import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider, Tabs } from 'antd';
import GhaConfigProvider from "@/_components/GhaConfigProvider"
import OrderItem from "./order-item";
import mockjs from "mockjs";


export default function UserOrders() {
  const [orders, setOrders] = useState([
    {
      id: Math.random().toString(), 
      title: mockjs.Random.ctitle(5, 10), 
      price: mockjs.Random.float(100, 1000, 2, 2),
    },
    {
      id: Math.random().toString(), 
      title: mockjs.Random.ctitle(5, 10), 
      price: mockjs.Random.float(100, 1000, 2, 2),
    }
  ]);
  return (
    <div className="p-0 lg:p-10">
      {orders.map(item => {
        return <OrderItem key={item.id} order={item} />
      })}
    </div>
  )
}

createRoot(document.querySelector("#orders")).render(<UserOrders />);