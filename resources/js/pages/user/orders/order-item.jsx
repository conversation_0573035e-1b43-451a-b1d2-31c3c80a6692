

export default function OrderItem() {
  return (
    <div className="order-item-wrap">
      <div className="order-header">
        <p className="num">订单号：255612B56454</p>
        <p className="date">预订日期：2025-06-18</p>
      </div>
      <div className="order-content">
        <div className="cover-wrap" style={{backgroundImage: `url(https://storage.ghadiscovery.com/cdn-cgi/image/width=658,height=428,f=auto,g=auto,fit=cover/img/images/3/3/0/4/1444033-1-eng-GB/691b37af7221-Plume.jpg)`}}>
          <div className="logo-wrap">
            <img src="https://cms.ghadiscovery.com/content/download/281/1261?version=31&inline=1" alt="" />
          </div>
          <div className="tag-wrap">
            <p>签名奢侈品</p>
          </div>
        </div>
        <div className="info-wrap">
          <h2 className="font-16 font-medium">凯宾斯基大道酒店</h2>
          <h5 className="font-12 mt-[3px] pb-1">阿联酋，迪拜</h5>
          <div className="w-8 h-0.5 bg-[#919191] my-1.5"></div>
          <h3 className="pt-1.5">M2尊贵PLUS客房（双床）</h3>
          <p className="font-12 mt-[5px] text-[#737373]">会员特惠价，2023-02-26至2023-02-28 3晚，成人 2， 儿童 1</p>
          <p className="font-12 text-right mt-[5px]">实付款：<span className="font-IvyMode-Reg font-18">CNY1,249</span></p>
          <div className="flex flex-row lg:block text-right py-[5px] -mx-[3px] mt-[5px]">
            <a href="" className="gha-btn flex-1 lg:w-32 mx-[3px]">订单详情</a>
            <a href="" className="gha-primary-btn flex-1 lg:w-32 mx-[3px]">再来一单</a>
          </div>
        </div>
      </div>
    </div>
  )
}