import { createRoot } from "react-dom/client";
import { Button, Form, Input, Upload, Select, ConfigProvider, DatePicker } from 'antd';
import GhaConfigProvider from "@/_components/GhaConfigProvider"
import GhaAvatar from "@images/user/gha-avatar.png"

function UserProfileForm() {
  const [form] = Form.useForm();
  function onFinish(values) {
    console.log(values);
  }
  return (
    <div className="p-10">
      <ConfigProvider
        theme={{
          components: {
            Form: {
              itemMarginBottom: 25,
              verticalLabelPadding: "0 0 0px",
            },
          },
        }}
      >
        <Form
          layout="vertical"
          form={form}
          onFinish={onFinish}
          className="gha-form"
        >
          <div className="flex flex-col lg:flex-row">
            <div className="lg:pr-7.5 lg:border-r border-[#919191]/20 text-center">
              <Upload>
                <div className="cursor-pointer flex flex-col items-center">
                  <img src={GhaAvatar} className="w-36 h-36 rounded-full" />
                  <p className="mt-2.5 text-primary font-14 flex flex-row items-center"><i className="iconfont icon-Camera text-2xl"></i>上传照片</p>
                </div>
              </Upload>
            </div>
            <div className="flex-1 mt-5 lg:pl-7.5  lg:mt-0">
              <div className="flex flex-col lg:flex-row">
                <Form.Item label="首选语言" name="language" className='flex-1 lg:mr-5'>
                  <Select variant='underlined' defaultValue="zh-CN">
                    <Select.Option value="zh-CN">简体中文</Select.Option>
                    <Select.Option value="en-US">English</Select.Option>
                    <Select.Option value="ja-JP">日本語</Select.Option>
                  </Select>
                </Form.Item>
                <div className="flex-1"></div>
              </div>
              <div className="flex flex-col lg:flex-row">
                <Form.Item label="姓*" name="first_name" className='flex-1 lg:mr-5'>
                  <Input variant='underlined' placeholder="请输入您的姓" />
                </Form.Item>
                <Form.Item label="名*" name="last_name" className='flex-1'>
                  <Input variant='underlined' placeholder="请输入您的名" />
                </Form.Item>
              </div>
              <Form.Item label={<>邮箱<span className="text-[#919191]/60">（只能修改一次）</span></>} name="last_name" className='flex-1'>
                <Input variant='underlined' placeholder="请输入您的邮箱" />
              </Form.Item>
              <div className="flex flex-col lg:flex-row">
                <Form.Item label="出生日期" name="first_name" className='flex-1 lg:mr-5'>
                  <DatePicker className="w-full" variant='underlined' placeholder="请选择您的出生日期" />
                </Form.Item>
                <Form.Item label="手机号码" name="tel" className='flex-1'>
                  <Input variant='underlined' placeholder="请输入您的手机号码" />
                </Form.Item>
              </div>
              <div className="flex flex-col lg:flex-row">
                <Form.Item label="城市" name="city" className='flex-1 lg:mr-5'>
                  <Input variant='underlined' placeholder="请输入您的城市" />
                </Form.Item>
                <Form.Item label="国家" name="tel" className='flex-1'>
                  <Select variant='underlined' defaultValue="zh-CN">
                    <Select.Option value="zh-CN">简体中文</Select.Option>
                    <Select.Option value="en-US">English</Select.Option>
                    <Select.Option value="ja-JP">日本語</Select.Option>
                  </Select>
                </Form.Item>
              </div>
              <Button className="gha-primary-btn w-full lg:w-48" type="primary" htmlType="submit">保存个人信息</Button>
            </div>
          </div>
        </Form>
      </ConfigProvider>
      
    </div>
  )
}

createRoot(document.querySelector("#user-profile-form")).render(
  <GhaConfigProvider><UserProfileForm /></GhaConfigProvider>
);