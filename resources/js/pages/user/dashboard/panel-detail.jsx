import { Table, ConfigProvider } from "antd"

export default function PanelDetail() {
  const columns = [
    {
      title: "奖励金",
      dataIndex: "content",
    },
    {
      title: "描述",
      dataIndex: "desc",
    },
    {
      title: "类型",
      dataIndex: "type",
    },
    {
      title: "时间",
      dataIndex: "time",
    },
  ]
  const data = [
    {
      key: "1",
      content: "+ D$200",
      desc: "积分奖励",
      type: "奖励",
      time: "10/05/2020",
    },
    {
      key: "2",
      content: "+ D$200",
      desc: "积分奖励",
      type: "奖励",
      time: "10/05/2020",
    },
    {
      key: "3",
      content: "+ D$200",
      desc: "积分奖励",
      type: "奖励",
      time: "10/05/2020",
    },
  ]
  return (
    <div className="p-4 lg:px-10 lg:py-7.5">
      <ConfigProvider
        theme={{
          components: {
            Table: {
              headerBg: "transparent",
              headerSplitColor: "transparent",
              /* 这里是你的组件 token */
            },
          },
        }}>
        <Table columns={columns} dataSource={data} pagination={false}></Table>
      </ConfigProvider>
      
    </div>
  )
}