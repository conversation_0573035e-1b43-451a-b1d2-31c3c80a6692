import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider, Tabs } from 'antd';
import PanelLevel from "./panel-level";
import PanelDonate from "./panel-donate";
import PanelDetail from "./panel-detail";
import GhaConfigProvider from "@/_components/GhaConfigProvider"

function UserDashboard() {
  const [activeKey, setActiveKey] = useState('level')
  const items = [
    {
      key: 'level',
      label: '我的礼遇',
      children: <PanelLevel/>,
    },
    {
      key: 'donate',
      label: '捐赠奖励金',
      children: <PanelDonate/>,
    },
    {
      key: 'detail',
      label: '奖励金明细',
      children: <PanelDetail/>,
    },
  ];
  return (
    <div className="user-dashboard-wrap hide-ant-tabs-nav">
      <div className="lg:hidden px-5 py-4 shadow-[0px_6px_6px_0px_rgba(0,0,0,0.05)] rounded-xl text-center font-14 bg-white">
        <p className="">欢迎你</p>
        <h2 className="font-18 font-Jost-SemiBold font-bold mt-[5px]">{ window.__Server_Data__.gha_user.name }</h2>
        <p className="mt-[5px]"><span className="text-[#737373]">会员卡号：</span>{ window.__Server_Data__.gha_user['member_balance']['membership_card_no'] }</p>
        <p className="mt-[5px]"><span className="text-[#737373]">会员等级：</span>{ window.__Server_Data__.user_card.title}会员</p>
        <div className="text-center mt-3">
          <div className="gha-primary-btn !py-1.5 !px-8 logout-btn-trigger">退出账号</div>
        </div>
        <div className="w-full h-px bg-[#919191]/20 my-3"></div>
        <p>您的奖励金</p>
        <h2 className="font-36 font-Jost-SemiBold font-bold">D${ window.__Server_Data__.gha_user['member_balance']['gravty_discovery_balance'] }</h2>
      </div>
      {Math.random() > 1 && (
        <div className="flex flex-row items-center lg:hidden -mx-1.5 mt-6 min-w-0">
          {items.map(e => {
            return (
              <div className="flex-1 mx-1.5">
                <div onClick={() => setActiveKey(e.key)} className={`${activeKey === e.key ? 'gha-primary-btn' : "gha-btn"} !py-1.5 inline-block w-full`} key={e.key}>{e.label}</div>
              </div>
            )
          })}
        </div>
      )}
      <div className="mt-6 lg:mt-6">
        <Tabs items={items} onChange={setActiveKey} activeKey={activeKey}/>
      </div>
      
    </div>
  )
}

createRoot(document.querySelector("#dashboard")).render(<GhaConfigProvider><UserDashboard /></GhaConfigProvider>);