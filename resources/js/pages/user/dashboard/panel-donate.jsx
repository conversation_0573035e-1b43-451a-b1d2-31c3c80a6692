import { Button } from "antd"
import donateLogo from "@images/user/donate-logo.png"

export default function PanelDonate() {
  return (
    <div className="px-4 lg:px-10">
      <div className="py-4 lg:py-7.5">
        <div className="donate-banner text-white">
          <div className="text-center lg:text-left">
            <img className="w-40 inline-block" src={donateLogo} alt="" />
          </div>
          <div className="pl-0 lg:pl-5 text-center lg:text-left">
            <h3 className="font-18 font-bold">创造大不同</h3>
            <p className="font-14">捐赠DISCOVERY奖励金（D$）</p>
            <a className="mt-5 w-40 gha-primary-btn" href="javascript:;">了解更多</a>
          </div>
        </div>
        <div className="mt-7.5">
          <h3 className="font-bold font-16 font-Jost-SemiBold">捐赠DISCOVERY奖励金（D$）</h3>
          <p className="font-14 mt-1.5">GHA DISCOVERY 奖励会员体验远近世界。 我们还努力让世界变得更美好，并支持我们的酒店和会员最热衷的事业。选择一个主题或特定的慈善机构。 D$1 捐赠 = 捐赠 1 美元； 100% 的价值将用于您选择的主题或事业。</p>
        </div>
        <div className="mt-7.5 border border-[#919191]/10 shadow-[0px_6px_6px_0px_rgba(0,0,0,0.05)] p-7.5 text-center">
          <h3 className="font-bold font-16">余额不足</h3>
          <p className="mt-1.5 font-14">您的帐户中没有足够的 D$ 进行捐赠。 请在您有可用的 D$10 后重试。</p>
          <Button type="primary" shape="round" className="mt-5 w-32 gha-primary-btn">好的</Button>
        </div>
      </div>
    </div>
  )
}