import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider, Tabs } from 'antd';
import GhaConfigProvider, { useAntdMessage } from "@/_components/GhaConfigProvider"
import { $constants, $helper, $http } from "@/_utils/_index"


export default function UserResetPwdFrom() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false)
  const { disabled, setDisabled } = useState(false)
  const message = useAntdMessage()

  function onFinish(values) {
    setLoading(true)
    $http.userUpPassword(values).subscribe(res => {
      setLoading(false)
      console.error("rrrr", res)
      if (res.status_code !== 200) {
        message.error(res.message)
        return;
      }
      message.success("修改成功")
      form.resetFields()
    })
  }

  function onValuesChange() {
    // console.error
    // setDisabled(form.isFieldsTouched())
  }
  
  return (
    <div className="p-10">
      <Form
        layout="vertical"
        form={form}
        requiredMark={false}
        validateTrigger="onBlur"
        onFinish={onFinish}
        className="gha-form"
        onValuesChange={onValuesChange}
        initialValues={$helper.isLaravelLocal() ? {
          new_password: '123456Abc$',
          old_password: '123456Abcd$',
          confirm_password: '123456Abc$'
        } : {}}
      >
        <div className="lg:w-[360px]">
          <Form.Item label="旧密码*" name="old_password" className='flex-1 lg:mr-5'
            rules={[
              {required: true, message: "请输入您的旧密码"},
            ]}
          >
            <Input.Password variant='underlined' placeholder="请输入您的旧密码" />
          </Form.Item>
          <Form.Item label="新密码*" name="new_password" className='flex-1 lg:mr-5'
            rules={[
              {required: true, message: "请输入您的密码"},
              { 
                pattern: $constants.passwordPattern,
                message: "密码至少8位，包含字母、数字和符号，且首尾不能有空格"
              }
            ]}
          >
            <Input.Password variant='underlined' placeholder="请输入新密码" />
          </Form.Item>
          <Form.Item label="确认密码*" name="confirm_password" className='flex-1 lg:mr-5'
            dependencies={['new_password']} 
            rules={[
              {required: true, message: "请输入确认密码"},
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('新密码与确认密码不一致!'));
                },
              }),
            ]}
          >
            <Input.Password variant='underlined' placeholder="请输入确认密码" />
          </Form.Item>
        </div>
        <Button loading={loading} className="gha-primary-btn !py-1 w-full lg:w-48" type="primary" htmlType="submit">保存</Button>
      </Form>
    </div>
  )
}

createRoot(document.querySelector("#user-reset-pwd-form")).render(
  <GhaConfigProvider><UserResetPwdFrom /></GhaConfigProvider>
);