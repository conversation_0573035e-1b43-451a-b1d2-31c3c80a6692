import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider, Tabs } from 'antd';
import PanelTravel from "./panel-travel";
import PanelHotel from "./panel-hotel";
import PanelCommunication from "./panel-communication";
import GhaConfigProvider from "@/_components/GhaConfigProvider"

function UserSettings() {
  const [activeKey, setActiveKey] = useState('travel')
  const items = [
    {
      key: 'travel',
      label: '旅行喜好',
      children: <PanelTravel/>,
    },
    {
      key: 'hotel',
      label: '住宿喜好',
      children: <PanelHotel/>,
    },
    {
      key: 'communication',
      label: '通讯喜好',
      children: <PanelCommunication/>,
    },
  ];
  return (
    <div className="user-dashboard-wrap">
      <div className="flex flex-row items-center lg:hidden -mx-1.5 min-w-0">
        {items.map(e => {
          return (
            <div className="flex-1 mx-1.5">
              <a href="javascript:void;" onClick={() => setActiveKey(e.key)} className={`${activeKey === e.key ? 'gha-primary-btn' : "gha-btn"} !py-1.5 inline-block w-full`} key={e.key}>{e.label}</a>
            </div>
          )
        })}
      </div>
      <div className="mt-4 lg:mt-0">
        <Tabs items={items} onChange={setActiveKey} activeKey={activeKey}/>
      </div>
      
    </div>
  )
}

createRoot(document.querySelector("#settings")).render(
  <GhaConfigProvider><UserSettings /></GhaConfigProvider>
);