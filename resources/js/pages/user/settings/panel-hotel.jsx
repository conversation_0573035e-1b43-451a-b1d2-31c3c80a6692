import { Checkbox, Row, Col } from "antd"

export default function PanelTravel() {
  const options = [
    { label: "亚洲", value: "asia" },
    { label: "欧洲", value: "europe" },
    { label: "非洲", value: "africa" },
    { label: "美洲", value: "america" },
    { label: "大洋洲", value: "oceania" },
  ]
  return (
    <div className="px-10 py-7.5">
      <div className="">
        <h2 className="font-16 font-bold">目的地</h2>
        <div className="mt-2">
          <Checkbox.Group className="w-full">
            <Row gutter={[16, 2]}>
              {options.map((option) => (
                <Col span={6} key={option.value}> {/* 24/6=4列 */}
                  <Checkbox value={option.value}>{option.label}</Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </div>
        <div className="w-full h-px bg-[#919191]/20 my-7.5"></div>
      </div>
      <div className="">
        <h2 className="font-16 font-bold">目的地</h2>
        <div className="mt-2">
          <Checkbox.Group className="w-full">
            <Row gutter={[16, 2]}>
              {options.map((option) => (
                <Col span={6} key={option.value}> {/* 24/6=4列 */}
                  <Checkbox value={option.value}>{option.label}</Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </div>
        <div className="w-full h-px bg-[#919191]/20 my-7.5"></div>
      </div>
      <div className="text-center">
        <a href="" className="gha-primary-btn w-full md:w-44">保存修改</a>
      </div>
    </div>
  )
}