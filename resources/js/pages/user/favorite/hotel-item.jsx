export default function HotelItem() {
  return (
    <div className="px-2 w-full md:w-1/2 xl:w-1/3 mt-4">
      <div className="hotel-item">
        <div className="cover-wrap">
          <div className="logo-wrap">
            <img src="https://cms.ghadiscovery.com/content/download/281/1261?version=31&inline=1" alt="" />
          </div>
          <div className="tag-wrap">
            <p>签名奢侈品</p>
          </div>
        </div>
        <div className="info-wrap">
          <h2 className="title">凯宾斯基大道酒店</h2>
          <h5 className="local">阿联酋，迪拜</h5>
          <div className="spt-line"></div>
          <div className="price-wrap">
            <div className="protrude">
              <p>会员价低至</p>
              <p className="price-num">CNY1,249</p>
            </div>
            <div className="">
              <p>会员价低至</p>
              <p className="price-num">CNY1,249</p>
            </div>
          </div>
          <div className="action-wrap">
            <a href="" className="gha-primary-btn">立即预订</a>
            <a href="" className="gha-btn">酒店详情</a>
          </div>
        </div>
      </div>
      
    </div>
  )
}