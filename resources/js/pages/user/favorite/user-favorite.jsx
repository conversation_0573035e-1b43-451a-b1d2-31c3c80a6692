import { createRoot } from "react-dom/client";
import { useEffect, useState } from "react";
import { Button, Form, Input, ConfigProvider, Tabs, Checkbox } from 'antd';
import GhaConfigProvider, { useAntdMessage } from "@/_components/GhaConfigProvider"
import mockjs from "mockjs";
import HotelItem from "@/_components/HotelItem";
import OfferItem from "@/_components/OfferItem";
import { $http } from "@/_utils/_index";


export default function UserFavorite() {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [showType, setShowType] = useState(['hotel'])
  const [data, setData] = useState({})
  const [types] = useState([{key: "hotel", label: "酒店"}, {key: "offer", label: "住宿优惠"}])
  const message = useAntdMessage()
  useEffect(() => {
    setLoading(true)
    $http.userGetCollectItems(['hotel', 'offer']).subscribe(res => {
      setLoading(false)
      if (res.status_code !== 200) {
        setError(true)
        return
      }
      setData(res.data)
    })
  }, [])
  function onToggleShowType(item) {
    let newTypes = [...showType]
    if (showType.includes(item.key) && showType.length > 1) {
      newTypes = newTypes.filter(t => t !== item.key)
    }
    if (!showType.includes(item.key)) {
      newTypes.push(item.key)
    }
    setShowType(newTypes)
  }
  function onFav(type, item) {
    $http.collectItem({type, id: item.id}).subscribe(res => {
      if (res.status_code !== 200) {
        message.error(res.msg)
        return
      }
      const newList = [...data[type]]
      const curItem = newList.find(e => e.id === item.id)
      curItem.is_collect = !curItem.is_collect
      setData({...data, [type]: newList})
    })
  }
  return (
    <>
      <div className={`px-10 hidden lg:block ${loading ? "pb-5 lg:shadow-[0px_5px_5px_0px_rgba(0,0,0,0.03)] border-b border-[#919191]/20" : ""}`}>
        <h2 className="font-18 font-bold">我的收藏</h2>
      </div>
      {loading ? (
        <div className="py-8">
          <div className="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i className="iconfont icon-loading text-4xl"></i></div>
        </div>
      ) : (
        <>
          <div className="pb-5 lg:px-10 lg:mt-4 lg:shadow-[0px_5px_5px_0px_rgba(0,0,0,0.03)] border-b border-[#919191]/20">
            {/* <Checkbox.Group value={[1,2]}>
              <Checkbox value={1}>酒店</Checkbox>
              <Checkbox value={2}>住宿优惠</Checkbox>
              <Checkbox value={3}>特色体验</Checkbox>
              <Checkbox value={4}>本地生活优惠</Checkbox>
              <Checkbox value={5}>促销活动</Checkbox>
            </Checkbox.Group> */}
            <div className="-mx-2.5 flex md:block">
              {types.map((item) => {
                return (
                  <div onClick={() => onToggleShowType(item)} key={item.key} className={`flex-1 ${showType.includes(item.key) ? 'gha-primary-btn' : 'gha-btn'} w-32 !py-1.5 mx-2.5`}>{item.label}</div>
                )
              })}
            </div>
            
          </div>
          <div className="py-6 lg:p-10">
            <div className={`${showType.includes('hotel') ? "" : "hidden"}`}>
              <h2 className="font-16 font-Jost-SemiBold font-bold text-center md:text-left">酒店</h2>
              <div className="flex flex-row flex-wrap -mx-2 -mt-2">
                {(data.hotel || []).map((_, idx) => {
                  return (
                    <div key={idx} className="px-2 w-full md:w-1/2 xl:w-1/3 mt-4">
                      <HotelItem hotel={_} onFav={() => onFav("hotel", _)}/>
                    </div>
                  )
                })}
              </div>
            </div>
            {showType.length > 1 && (<div className="w-full h-px bg-[#919191]/20 my-7.5"></div>)}
            <div className={`${showType.includes('offer') ? "" : "hidden"}`}>
              <h2 className="font-16 font-Jost-SemiBold font-bold">住宿优惠</h2>
              <div className="flex flex-row flex-wrap -mx-2 -mt-2">
                {(data.offer || []).map((_, idx) => {
                  return (
                    <div key={idx} className="px-2 w-full md:w-1/2 xl:w-1/3 mt-4">
                      <OfferItem offer={_} onFav={() => onFav("offer", _)}/>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </>
      )}
      
    </>
    
  )
  return loading ? (
    <div className="py-8">
      <div className="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i className="iconfont icon-loading text-4xl"></i></div>
    </div>
  ) : (
    <>
      
    </>
  )
}

createRoot(document.querySelector("#favorite")).render(
  <GhaConfigProvider><UserFavorite /></GhaConfigProvider>
);