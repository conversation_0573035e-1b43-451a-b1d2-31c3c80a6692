
$(function() {
  new MembershipController()
})
class MembershipController {
  constructor() {
    this.bootstrap()
  }

  bootstrap() {
    this.initMobileCardTab()
  }

  initMobileCardTab() {
    $(".mobile-card-rights").on("click", ".level-item .tracker", function() {
      const key = $(this).data("key")
      if ($(".level-step-container").hasClass(`active-${key}`)) return
      $(".level-step-container").removeClass(`active-1`).removeClass(`active-2`).removeClass(`active-3`).removeClass(`active-4`)
      $(".level-step-container").addClass(`active-${key}`)
      $(".card-panel").addClass("hidden")
      $(`.card-panel-${key}`).removeClass("hidden")
    })
  }
}