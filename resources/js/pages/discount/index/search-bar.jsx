import { createRoot } from "react-dom/client";
import { GhaDiscountSearchBar } from "@/_components/gha-search-bar/GhaSearchBar"
import { $helper } from "@/_utils/_index"


createRoot(document.querySelector(".discount-search-bar")).render(
  <GhaDiscountSearchBar sticky onSubmit={({keyword, offerType, date}) => {
    const searchParams = {
      keyword,
      offerType,
      date: date.map(e => e.format("YYYY-MM-DD")),
    }
    location.href = `/search/offers#/?${$helper.encodeSearchHash(searchParams)}`
  }}/>
);