
import { $helper, $ghaMapHelper } from "@/_utils/_index"

$(function() {
  new DiscountDetailController()
})
class DiscountDetailController {
  constructor() {
    this.bootstrap()
  }

  bootstrap() {
    this.initFixedBar(".fixed-bar")
    this.initFixedBar(".fixed-bar-m")
    this.bindEvents()
    this.initScrollActive()
    this.initModalMap()
  }

  initModalMap() {
    $helper.getGlobalSubject().on("updateItemFav", function(event) {
      window.$hotel.is_collect = event.data.nextState
      let $favEl = $(`.trigger-fav-el[data-type="${event.data.type}"][data-id="${event.data.id}"]`)
      $favEl.find("i").removeClass("icon-Heart").removeClass("icon-Heart-filled")
      $favEl.find("i").addClass(event.data.nextState ? "icon-Heart-filled" : "icon-Heart")
    })
    const that = this
    $("body").on("click", ".gha-modal-wrap .close", function() {
      $(".gha-modal-wrap").addClass("hidden")
      $("body").removeClass("overflow-hidden")
    })
    $("body").on("click", "#toggleMapBtn", function() {
      $(".gha-modal-wrap.map").removeClass("hidden")
      $("body").addClass("overflow-hidden")
      that.buildMap()
    })
  }
  buildMap() {
    if (this.mapBuilded) return
    this.mapBuilded = true
    let hotelData = window.$hotel
    mapboxgl.accessToken = 'pk.eyJ1IjoiaWlpc2xlZSIsImEiOiJjbHJoN2Z3djMwbjY0MmptampmODRlcWdvIn0.yak7m5pJUycZ58aJUst7ag'
    const map = new mapboxgl.Map({
      container: 'modalMap', // container ID
      style: "mapbox://styles/mapbox/streets-v12",
      zoom: 10,
      center: [hotelData.longitude, hotelData.latitude]
    });
    map.addControl(new MapboxLanguage({
        defaultLanguage: 'zh-Hans'
    }));
    map.on("load", function() {
      $(".gha-mapbox").removeClass("loading")
      $ghaMapHelper.fitBoundsMap(map, [[hotelData.longitude, hotelData.latitude]])
      let item = hotelData
      let markerEl
      markerEl = document.createElement('div')
      markerEl.className = 'gha-mapbox-marker-invalid'
      $(markerEl).append(`<i class="iconfont icon-Union"></i>`)
      
      const marker = new mapboxgl.Marker(markerEl)
        .setLngLat([item.longitude, item.latitude])
        .addTo(map);
      marker.getElement().addEventListener("click", function() {
        $ghaMapHelper.flyMarkerToCenter(map, [item.longitude, item.latitude])
        const popup = $ghaMapHelper.getPopup({hotelData, slug: "offers", autoTriggerFav: true})
        marker.setPopup(popup)
        marker.togglePopup()
        $ghaMapHelper.initPopupSwiper(hotelData)
      })
    })
  }

  bindEvents() {
    $("body").on("click", "#togglePolicy", function() {
      if ($(this).find("i").hasClass("icon-down")) {
        $(this).find("i").removeClass("icon-down").addClass("icon-up")
      } else {
        $(this).find("i").removeClass("icon-up").addClass("icon-down")
      }
      $("#togglePolicyContent").toggleClass("hidden")
    })
    $("body").on("click", ".anchor-item", function() {
      $(".anchor-item").removeClass("active")
      $(this).addClass("active")
    })
  }

  initFixedBar(selector) {
    const sentinelTop = $(selector).find(`.fixed-sentinel-top`)[0];
    const target = $(selector)[0];
    if (!target || !sentinelTop) return;
    function handleScroll() {
      if (sentinelTop.getBoundingClientRect().top < 90) {
        target.classList.add('gha-fixed');
      } else {
        target.classList.remove('gha-fixed');
      }
    }
    window.addEventListener('scroll', handleScroll, true)
    handleScroll()
  }

  initScrollActive() {
    function setTabItemActive(idx) {
      if (!$(".fixed-bar .anchor-item").eq(idx).hasClass("active")) {
        $(".fixed-bar .anchor-item").removeClass("active")
        $(".fixed-bar .anchor-item").eq(idx).addClass("active")
      }
      if (!$(".fixed-bar-m .anchor-item").eq(idx).hasClass("active")) {
        $(".fixed-bar-m .anchor-item").removeClass("active")
        $(".fixed-bar-m .anchor-item").eq(idx).addClass("active")
      }
    }
    function handleScroll() {
      let anchor0Top = document.querySelector("#anchor0").getBoundingClientRect().top
      let anchor1Top = document.querySelector("#anchor1").getBoundingClientRect().top
      let anchor2Top = document.querySelector("#anchor2").getBoundingClientRect().top
      let topIdx = [anchor0Top, anchor1Top, anchor2Top].findIndex(e => e > 10)
      if (topIdx === -1) {
        setTabItemActive(2)
      }
      if (2 === topIdx) {
        setTabItemActive(1)
      }
      if ([0, 1].includes(topIdx)) {
        setTabItemActive(0)
      }
    }
    window.addEventListener('scroll', handleScroll)
  }
}