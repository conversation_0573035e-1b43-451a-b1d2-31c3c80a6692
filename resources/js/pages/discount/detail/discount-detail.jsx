import { createRoot } from "react-dom/client";
import { Modal } from "antd"

import { GhaDiscountDetailBar } from "@/_components/gha-search-bar/GhaSearchBar"
import { useEffect, useState } from "react";
import { $helper } from "@/_utils/_index"

function App() {
  const [opend, setOpend] = useState(false)
  const [ childModalOpend, setChildModalOpend ] = useState(false)
  useEffect(() => {
    $("#orderNowBtn").click(function() {
      setOpend(true)
    })
  }, [])
  return (
    <Modal 
      open={opend} 
      onCancel={() => {
        if ($helper.isAntdGhaPopoverRootVisible()) return
        setOpend(false)
      }} 
      closable={false} 
      maskClosable={!childModalOpend}
      footer={null}
      width={850}
      top={100}
      transitionName="ant-fade"
      rootClassName='gha-antd-modal'
    >
      <div className="rounded-xl overflow-hidden px-4">
        <GhaDiscountDetailBar></GhaDiscountDetailBar>
      </div>
      
    </Modal>
  )
}

createRoot(document.querySelector(".discount-detail-content")).render(<App />);