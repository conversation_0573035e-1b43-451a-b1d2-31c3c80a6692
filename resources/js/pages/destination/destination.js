import { $ghaMapHelper, $helper, $priceService } from "@/_utils/_index"
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"

$(function() {
  new DestinationController()
})
class DestinationController {
  constructor() {
    this.bootstrap()
    this.initPriceService()
  }

  initPriceService() {
    $priceService.setCond(useGhaSearchBarStore.getState())
    $priceService.on($priceService.keys.ON_EVERY_HOTEL_PRICE_UPDATED, function(event) {
      const {hotelID} = event.data
      const $el = $(`.hotel-item[data-synxisid="${hotelID}"] .price-wrap`)
      if ($el) {
        let html = $ghaMapHelper.buildPriceHtml({hotelData: {synxis_hotel_id: hotelID}})
        $el.html(html)
        if ($el.find(".price-num").length > 0) {
          $(`.hotel-item[data-synxisid="${hotelID}"]`).find(".booking-btn").removeClass("disabled")
        }
      }
    }, "DestinationController")

    $(".hotel-item").each((idx, ele) => {
      if ($(ele).data('synxisid')) {
        $priceService.getPrice($(ele).data('synxisid'))
      }
    })
  }

  bootstrap() {
    this.initSwiper()
  }

  initSwiper() {
    new Swiper(".swiper-container", {
      slidesPerView: 1,
      spaceBetween: 16,
      // centeredSlides: true,
      breakpoints: {
        768: {
          slidesPerView: 2,
          spaceBetween: 24
        },
        1024: {
          slidesPerView: 3,
          spaceBetween: 24
        },
        1280: {
          slidesPerView: 2,
          spaceBetween: 24
        }
      },
      // centeredSlides: true,
      // loop: true,
      navigation: {
        nextEl: ".gha-swiper-button-next",
        prevEl: ".gha-swiper-button-prev",
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
    })
  }
}