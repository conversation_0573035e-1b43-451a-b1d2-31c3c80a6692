import { createRoot } from "react-dom/client";
import { useEffect, useState, useRef } from "react";
import TopFilter from "./parts/TopFilter";
import TagFilter from "./parts/TagFilter";
import FilterState, {orderOptions} from "./parts/FilterState";
import HotelList from "./parts/HotelList";
import HotelMap from "./parts/HotelMap";
import MobileFilterBar from "./parts/MobileFilterBar";
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"
import { $http, $helper, $priceService } from "@/_utils/_index"
import { useAntdMessage } from "@/_components/GhaConfigProvider";


function HotelListApp() {
  const mapRef = useRef(null)
  const showType = useSearchStore(state => state.showType)
  const slug = useSearchStore(state => state.slug)
  const list = useSearchStore(state => state.list)
  const message = useAntdMessage()
  useEffect(() => {
    // console.error("HotelListApp", useGhaSearchBarStore.getState())
    requestData()
  }, [])
  function onHotelLike(id, callback) {
    let type = slug === "hotels" ? "hotel" : "offer"
    $http.collectItem({type, id}).subscribe(
      res => {
        if (res.status_code !== 200) {
          message.error(res.msg)
          return
        }
        const newDataSource = [...list]
        const item = newDataSource.find(e => `${e.id}` === `${id}`)
        item.is_collect = !item.is_collect
        useSearchStore.setState({list: newDataSource})
        callback(item.is_collect, newDataSource)
      }
    )
  }
  function onCondChange(cond) {
    // if (cond.type === "search") {
    //   const searchBarStoreState = useGhaSearchBarStore.getState()
    //   const searchParams = {
    //     keyword: searchBarStoreState.keyword,
    //     rooms: searchBarStoreState.rooms,
    //     date: searchBarStoreState.date
    //   }
    //   location.href = `/search/${slug}?${$helper.buildBookingQuery(searchParams)}`
    // }
    requestData()
  }
  function onMobileCondChange(cond) {
    useSearchStore.setState({...cond})
    requestData()
  }
  function buildSearchParams() {
    const searchBarStoreState = useGhaSearchBarStore.getState()
    const searchStoreState = useSearchStore.getState()
    const selectTags = searchStoreState.selectTags.filter(e => e.indexOf("_all") < 0)
    const searchParams = {
      keyword: searchBarStoreState.keyword,
      rooms: JSON.stringify(searchBarStoreState.rooms),
      date: searchBarStoreState.date.map(e => e.format("YYYY-MM-DD")),
      order: searchStoreState.order,
      brands: selectTags.filter(e => e.startsWith("brands_")).map(e => e.split("_")[1]).join("_"),
      categories: selectTags.filter(e => e.startsWith("categories_")).map(e => e.split("_")[1]).join("_"),
      facts: selectTags.filter(e => e.startsWith("facts_")).map(e => e.split("_")[1]).join("_"),
      series: selectTags.filter(e => e.startsWith("series_")).map(e => e.split("_")[1]).join("_"),
    }
    if (searchBarStoreState.offerType && searchBarStoreState.offerType !== "all") {
      searchParams.offer_type = searchBarStoreState.offerType
    }
    return searchParams
  }
  function requestData() {
    useSearchStore.setState({loading: true})
    window.scrollTo({top: 0, behavior: 'smooth'})
    rxjs.of(null).pipe(
      rxjs.concatMap(() => {
        if (slug === "offers") {
          return $http.searchOffers(buildSearchParams())
        }
        return $http.searchHotels(buildSearchParams())
      })
    ).subscribe(res => {
      $priceService.clear()
      $priceService.setCond(useGhaSearchBarStore.getState())
      useSearchStore.setState({loading: false, list: res.data?.data || []})
      mapRef.current?.updateMapData(res.data.data)
      // console.error("xxxxxxx", res)
    })
  }
  return (
    <>
      <TopFilter onCondChange={onCondChange} />
      <div className="hidden lg:block relative">
        <TagFilter onCondChange={onCondChange} />
        <FilterState onCondChange={onCondChange}/>
      </div>
      <div className="lg:hidden relative">
        <MobileFilterBar onSubmit={onMobileCondChange}/>
      </div>
      {showType === "list" && <HotelList></HotelList>}
      {showType === "map" && <HotelMap ref={mapRef} dataSource={list} onHotelLike={onHotelLike}></HotelMap>}
    </>
  )
}

createRoot(document.querySelector(".hotel-list-page-wrap")).render(<HotelListApp />);