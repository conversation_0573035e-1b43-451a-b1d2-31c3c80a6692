import { useState } from "react"
import { Popover } from "antd"
import { $helper } from "@/_utils/_index"
import { GhaHotelListSearchBar } from "@/_components/gha-search-bar/GhaSearchBar"
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";

export default function HotelMapBar() {
  const [mobilePopoverOpend, setMobilePopoverOpend] = useState(false)
  return (
    <div className="absolute top-20 left-0 right-0 z-[999] lg:hidden">
      <div className="px-4 flex flex-row items-center">
        <div onClick={() => useSearchStore.setState({showType: "list"})} className="flex flex-row items-center justify-center w-12 h-12 bg-white shadow-md rounded-full cursor-pointer">
          <i className="iconfont icon-a-Arrow-Left text-black text-3xl"></i>
        </div>
        <Popover 
          placement="bottom" 
          // trigger={["click"]}
          trigger={[""]}
          rootClassName="gha-popover-root-no-padding gha-popover-root-no-bg gha-popover-root-no-shadow" 
          getPopupContainer={e => e.parentElement}
          open={mobilePopoverOpend}
          arrow={false}
          onOpenChange={(open) => {
            if ($helper.isAntdGhaPopoverRootVisible()) return
            setMobilePopoverOpend(open)
          }}
          content={(
            <>
              <div className="w-screen">
                <div className="g-main-content">
                  <GhaHotelListSearchBar className={"rounded-xl overflow-hidden"} sticky={false}/>
                </div>
              </div>
            </>
          )}
        >
          <div className="bg-white h-12 flex items-center px-4 font-14 ml-4 shadow-md cursor-pointer">
            <p>北京，9/6-9/8，2名成人1间房</p>
          </div>
        </Popover>
        
      </div>
    </div>
  )
}