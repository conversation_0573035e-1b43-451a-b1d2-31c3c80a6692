import { GhaHotelListSearchBar, GhaDiscountSearchBar } from "@/_components/gha-search-bar/GhaSearchBar"
import useSearchStore from "../store/useSearchStore"

export default function TopFilter({ onCondChange }) {
  const slug = useSearchStore((state) => state.slug)
  return (
    <div className="top-filter-bar">
      <div className="g-main-content">
        <div className="home-filter-wrap">
          <div className="inner">
            <div className="tab-wrap !hidden">
              <div className="tab-list active-1">
                  <div className="item"><p>精选酒店</p></div>
                  <div className="item"><p>本地生活</p></div>
                  <div className="item"><p>体验</p></div>
              </div>
              <div className="tip">
                  <p>GHA代表着45个品牌，850多家酒店，遍布 100多个国家，服务会员3000万</p>
              </div>
            </div>
            <div className="hotel-list-search-bar">
              {slug === "hotels" && <GhaHotelListSearchBar onSubmit={(e) => onCondChange({type: "search", value: e})} sticky/>}
              {slug === "offers" && <GhaDiscountSearchBar onSubmit={(e) => onCondChange({type: "search", value: e})} sticky/>}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}