import { useState, useEffect } from "react"

export default function TagFilterRow({row, selectTags, onSelectTag}) {
  const [showMoreBrands, setShowMoreBrands] = useState(false)
  const [moreBrandLen, setMoreBrandLen] = useState(5)
  useEffect(() => {
    function updateMoreBrandLen() {
      let offset = 0
      let nextLineIdx = 0
      const els = document.querySelectorAll(`.virtual-${row.type}`)
      for (let i = 0; i < els.length; i++) {
        if (els[i].offsetHeight > offset && offset > 0) {
          nextLineIdx = i
          break
        }
        offset = els[i].offsetHeight
      }
      setMoreBrandLen(nextLineIdx)
    }
    // console.error("nextLineIdx=" + nextLineIdx)
    window.onresize = function() {
      updateMoreBrandLen()
    }
    updateMoreBrandLen()
  }, [])
  return (
    <div className="filter-row">
      <div className="filter-row-name">{row.name}</div>
      <div className="filter-row-tags-list relative">
        {row.tags.slice(0, showMoreBrands || (moreBrandLen <= 0) ? 9999 : moreBrandLen).map(tag => {
          return (
            <div key={tag.id} onClick={() => onSelectTag(row, tag)} className={`tag-item ${selectTags.includes(`${row.type}_${tag.id}`) ? "active" : ""}`}>{tag.name}</div>
          )
        })}
        {moreBrandLen > 0 && <div className="tag-item more" onClick={() => setShowMoreBrands(!showMoreBrands)}>{showMoreBrands ? `收起` : `显示全部(${row.tags.length - 1}个)`}</div>}
        <>
          {Array.from({length: row.tags.length}).map((_, index) => {
            return (
              <div key={index} className={`virtual virtual-${row.type} virtual-${index} absolute top-0 left-0 right-0 opacity-0 -translate-x-[9999px]`}>
                {row.tags.slice(0, index + 2).map(tag => {
                  return (
                    <div key={tag.id} className="tag-item">{tag.name}</div>
                  )
                })}
                <div className="tag-item more">显示全部({row.tags.length - 1}个)</div>
              </div>
            )
          })}
        </>
      </div>
    </div>
  )
}