import { Popover } from "antd"
import { useState } from "react"
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";

export const orderOptions = [
  {id: "default", name: "相关度"},
  {id: "brand", name: "品牌"},
  {id: "desc", name: "价格：由低到高"},
  {id: "asc", name: "价格：由高到低"},
]

export default function FilterState({ onCondChange }) {
  const curOrder = useSearchStore(state => state.order)
  const list = useSearchStore(state => state.list)
  const loading = useSearchStore(state => state.loading)
  const showType = useSearchStore(state => state.showType)
  const [orderPopoverOpend, setOrderPopoverOpend] = useState(false)
  const orderPopoverContent = (
    <div className="px-4">
      {orderOptions.map(e => {
        return <div onClick={() => {
          onCondChange({type: "order", value: e.id})
          useSearchStore.setState({order: e.id})
          setOrderPopoverOpend(false)
        }} key={e.id} className={`py-2 border-b last:border-b-0 text-center cursor-pointer ${curOrder === e.id ? "text-primary" : ""}`}>{e.name}</div> 
      })}
      
    </div>
  )
  return (
    <div className={`py-4 ${loading ? 'hidden' : ""}`}>
      <div className="g-main-content">
        <div className="filter-state-wrap">
          <p className="font-12 text-[#696969]">找到{list.length}个结果</p>
          <div className="flex flex-row items-center">
            <Popover placement="bottom" open={orderPopoverOpend} onOpenChange={setOrderPopoverOpend} content={orderPopoverContent} trigger={["click"]}>
              <div className="shadow-md font-12 shadow-[#070102]/10 rounded-full border border-[#eeeeee] flex flex-row items-center pl-4 pr-2.5 h-8 cursor-pointer">
                {orderOptions.find(e => e.id === curOrder)?.name}
                <i className="iconfont icon-xiala font-20 !leading-none"></i>
              </div>
            </Popover>
            <div className="map-select-wrap">
              <div onClick={() => useSearchStore.setState({showType: "list"})} className={showType === "list" ? "active" : ""}><i className="iconfont icon-List"></i>列表</div>
              <div onClick={() => useSearchStore.setState({showType: "map"})} className={showType === "map" ? "active" : ""}><i className="iconfont icon-Map"></i>地图</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}