(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[3654], {
    53547: function(e, t, o) {
        "use strict";
        o.d(t, {
            Z: function() {
                return k
            },
            u: function() {
                return y
            }
        });
        var n = o(85893)
          , r = o(67294)
          , i = o(6158)
          , a = o.n(i)
          , l = o(73935);
        o(81634);
        var c = o(84381)
          , u = o.n(c)
          , s = o(79466)
          , d = o(60461)
          , p = o(81043)
          , f = o(29806)
          , v = o(29090)
          , m = o(67262)
          , h = o(73385)
          , g = o(1959)
          , b = o(71626);
        a().accessToken = "pk.eyJ1IjoibWFrc3ltLW1hbHRzZXYiLCJhIjoiY2tweTVkbG1tMDQ3dTJzcnJxMHV0enJ4MCJ9.2TfFQqNNty9MDAFxZriNaA";
        var x = function(e) {
            return e.map(function(e) {
                var t, o = e.id;
                return {
                    type: "Feature",
                    id: o,
                    properties: {
                        id: o
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [e.longitude, e.latitude]
                    }
                }
            })
        };
        function y(e) {
            var t = e.path;
            switch (t) {
            case "/search/hotels":
                return h.By.mapViewHotelSearch;
            case "/search/offers":
                return h.By.mapViewStayOffersSearch;
            case "/destinations-map":
                return "Destinations Map";
            default:
                return h.By.mapViewHotelPage
            }
        }
        function k(e) {
            var t, o, i, c = e.markers, h = e.isLoading, k = e.onPinClick, _ = e.initialPopupHotelId, C = e.heightScreen, w = e.hotelsSearchPage, L = e.onClickContainerMap, M = (0,
            r.useState)(), N = M[0], S = M[1], B = (0,
            r.useState)(!1), I = B[0], E = B[1], H = (0,
            r.useRef)(null), P = (0,
            r.useState)([]), T = P[0], Z = P[1], j = (0,
            r.useRef)(), A = (0,
            r.useState)(), F = A[0], D = A[1], R = (0,
            r.useRef)(), V = (0,
            v.Z)(), O = (0,
            m.Z)(), W = (0,
            g.Z)(), J = (0,
            d.Z)(), z = (0,
            p.v9)(function(e) {
                return e.global.currency.rates
            }), U = (0,
            p.v9)(function(e) {
                return e.global.currency.selectedCurrency
            }), q = (0,
            f.Z)(U, z), Y = (0,
            p.v9)(function(e) {
                return e.booking.hotel.params
            }), Q = null === (t = c.find(function(e) {
                return e.id === F
            })) || void 0 === t ? void 0 : t.hotel, G = (0,
            b.eg)(null == Q ? void 0 : Q.sabreTaxSetup), X = null == Q ? void 0 : Q.priceLoaded, $ = null == Q ? void 0 : null === (o = Q.priceInfo) || void 0 === o ? void 0 : o[G.dailyPrice], K = null == Q ? void 0 : null === (i = Q.priceInfo) || void 0 === i ? void 0 : i[G.dailyMemberPrice];
            (0,
            r.useEffect)(function() {
                if (j.current && N) {
                    var e, t = document.createElement("div");
                    t.className = u().popup,
                    Q && l.render((0,
                    n.jsx)(s.Z, {
                        hotel: Q,
                        favoriteUse: J,
                        converter: q,
                        bookingParams: Y,
                        trackAddToWishList: V,
                        trackSelectItem: W
                    }), t),
                    null === (e = j.current) || void 0 === e || e.setDOMContent(t)
                }
                Q && X && O.trackViewItemList({
                    listName: y({
                        path: window.location.pathname
                    }),
                    hotels: [Q],
                    hotelSearchData: Y
                })
            }, [F, U, X, $, K, null == Q ? void 0 : Q.priceLoading, F && J.isFavorite(F), ]),
            (0,
            r.useEffect)(function() {
                var e = !1
                  , t = null
                  , o = function(o) {
                    var n = function() {
                        e = !0,
                        t && (clearTimeout(t),
                        t = null)
                    }
                      , r = function() {
                        e = !1,
                        t = setTimeout(function() {
                            if (!e) {
                                var t;
                                null === (t = j.current) || void 0 === t || t.remove(),
                                D(void 0)
                            }
                        }, 100)
                    };
                    if (N) {
                        for (var i = o.features[0].properties.id, l = o.features[0].geometry.coordinates.slice(); Math.abs(o.lngLat.lng - l[0]) > 180; )
                            l[0] += o.lngLat.lng > l[0] ? 360 : -360;
                        j.current || (j.current = new (a()).Popup({
                            closeButton: !1,
                            className: u().popup,
                            closeOnMove: !1,
                            offset: 18,
                            maxWidth: "320px",
                            focusAfterOpen: !0
                        }).on("close", function() {
                            return D(void 0)
                        })),
                        j.current.setLngLat(l).addTo(N);
                        var c = j.current.getElement();
                        c && (c.removeEventListener("mouseenter", n),
                        c.removeEventListener("mouseleave", r),
                        c.addEventListener("mouseenter", n),
                        c.addEventListener("mouseleave", r)),
                        null == k || k(i),
                        D(i)
                    }
                }
                  , n = function() {
                    t = setTimeout(function() {
                        if (!e) {
                            var t;
                            null === (t = j.current) || void 0 === t || t.remove(),
                            D(void 0)
                        }
                    }, 100)
                };
                return null == N || N.on("mouseenter", "unclustered-point", o),
                null == N || N.on("mouseleave", "unclustered-point", n),
                function() {
                    null == N || N.off("mouseenter", "unclustered-point", o),
                    null == N || N.off("mouseleave", "unclustered-point", n)
                }
            }, [c]),
            (0,
            r.useEffect)(function() {
                N ? et() : S(ee())
            }, [N]),
            (0,
            r.useEffect)(function() {
                if (N) {
                    var e = null == N ? void 0 : N.getSource("hotel_markers");
                    e && e.setData({
                        type: "FeatureCollection",
                        features: x(c)
                    })
                }
            }, [N]),
            (0,
            r.useEffect)(function() {
                return function() {
                    null == N || N.remove()
                }
            }, []),
            (0,
            r.useEffect)(function() {
                if (!(null == c ? void 0 : c.length) || T.length !== c.length) {
                    eo();
                    return
                }
                var e = function(e, t) {
                    return e + t.id
                }
                  , t = T.reduce(e, "")
                  , o = c.reduce(e, "");
                t !== o && eo()
            }, [N, c, I]),
            (0,
            r.useEffect)(function() {
                N && (F && N.setFeatureState({
                    source: "hotel_markers",
                    id: F
                }, {
                    isActive: !0
                }),
                R.current && N.setFeatureState({
                    source: "hotel_markers",
                    id: R.current
                }, {
                    isActive: !1
                }),
                R.current = F)
            }, [N, F]);
            var ee = function() {
                if (H.current) {
                    var e = {
                        projection: {
                            name: "mercator"
                        },
                        container: H.current,
                        style: "mapbox://styles/maksym-maltsev/clgkmhjmg007h01qq4u4834ob",
                        zoom: 2,
                        attributionControl: !1
                    };
                    c && c.length > 0 && (e.center = [c[0].longitude, c[0].latitude]);
                    var t = new (a()).Map(e);
                    return t.addControl(new (a()).FullscreenControl, "top-right"),
                    t
                }
            }
              , et = function() {
                N && (N.on("load", function() {
                    N.addSource("hotel_markers", {
                        type: "geojson",
                        data: {
                            type: "FeatureCollection",
                            features: x(c)
                        },
                        cluster: !0,
                        clusterMaxZoom: 14,
                        clusterRadius: 50
                    }),
                    N.addLayer({
                        id: "clusters",
                        type: "circle",
                        source: "hotel_markers",
                        filter: ["has", "point_count"],
                        paint: {
                            "circle-color": "#300B5C",
                            "circle-radius": 18,
                            "circle-opacity": .8
                        }
                    }),
                    N.addLayer({
                        id: "cluster-count",
                        type: "symbol",
                        source: "hotel_markers",
                        filter: ["has", "point_count"],
                        paint: {
                            "text-color": "#fff"
                        },
                        layout: {
                            "text-field": "{point_count}",
                            "text-font": ["Jost Regular"],
                            "text-size": 18
                        }
                    }),
                    N.addLayer({
                        id: "unclustered-point",
                        type: "circle",
                        source: "hotel_markers",
                        filter: ["!", ["has", "point_count"]],
                        paint: {
                            "circle-color": ["case", ["boolean", ["feature-state", "isActive"], !1], "#bbb0dc", "#300B5C"],
                            "circle-radius": 18,
                            "circle-opacity": ["case", ["boolean", ["feature-state", "isActive"], !1], 1, .8]
                        }
                    }),
                    N.addLayer({
                        id: "unclustered-letter",
                        type: "symbol",
                        source: "hotel_markers",
                        filter: ["!", ["has", "point_count"]],
                        paint: {
                            "icon-opacity": 1,
                            "icon-translate": [1.3, -.7]
                        },
                        layout: {
                            "icon-image": "dIcon",
                            "icon-allow-overlap": !0
                        }
                    }),
                    E(!0);
                    var e = ["clusters", "cluster-count", "unclustered-point", "unclustered-letter"];
                    N.on("mouseover", e, function() {
                        return N.getCanvas().style.cursor = "pointer"
                    }),
                    N.on("mouseleave", e, function() {
                        return N.getCanvas().style.cursor = ""
                    })
                }),
                N.on("click", "clusters", function(e) {
                    var t, o = N.queryRenderedFeatures(e.point, {
                        layers: ["clusters"]
                    }), n = null === (t = o[0].properties) || void 0 === t ? void 0 : t.cluster_id;
                    (null == N ? void 0 : N.getSource("hotel_markers")).getClusterExpansionZoom(n, function(e, t) {
                        e || N.easeTo({
                            center: o[0].geometry.coordinates,
                            zoom: t + 1
                        })
                    })
                }))
            }
              , eo = function() {
                if (N && I) {
                    if (!h && c.length) {
                        var e = c.map(function(e) {
                            return [e.longitude, e.latitude]
                        }).reduce(function(e, t) {
                            return e.extend(t)
                        }, new (a()).LngLatBounds);
                        if (null == N || N.fitBounds(e, {
                            padding: 100,
                            maxZoom: 12
                        }),
                        _) {
                            var t = c.find(function(e) {
                                return e.id === _
                            });
                            if (!t)
                                return;
                            var o = [t.longitude, t.latitude];
                            j.current = j.current || new (a()).Popup({
                                closeButton: !1,
                                className: u().popup,
                                closeOnMove: !1,
                                offset: 18,
                                anchor: "top",
                                maxWidth: "320px",
                                focusAfterOpen: !0
                            }).on("close", function() {
                                return D(void 0)
                            }),
                            j.current.setLngLat(o).addTo(N),
                            N.flyTo({
                                center: o,
                                offset: [0, -100],
                                duration: 300,
                                curve: 0
                            }),
                            D(_),
                            null == k || k(_)
                        }
                    }
                    var n = null == N ? void 0 : N.getSource("hotel_markers");
                    null == n || n.setData({
                        type: "FeatureCollection",
                        features: x(c)
                    }),
                    Z(c)
                }
            };
            return w ? (0,
            n.jsx)("div", {
                className: "w-full absolute z-20 left-0 md:relative md:px-6 md:w-full ".concat(u().containerMapBoxHotel, " ").concat(C && u().heightScreen),
                onClick: L,
                children: (0,
                n.jsx)("div", {
                    ref: H,
                    className: "h-full w-full shadow-lg ".concat(u().mapbox)
                })
            }) : (0,
            n.jsx)("div", {
                ref: H,
                className: "h-full w-full " + u().mapbox,
                style: {
                    borderRadius: "14px"
                }
            })
        }
    },
    79466: function(e, t, o) {
        "use strict";
        o.d(t, {
            Z: function() {
                return g
            }
        });
        var n = o(85893)
          , r = o(67294)
          , i = o(77058)
          , a = o(61695)
          , l = o(33779)
          , c = o(60023)
          , u = o(96879)
          , s = o(63419)
          , d = o(43369)
          , p = o(59007)
          , f = o(93942)
          , v = o(52294)
          , m = o(53547)
          , h = o(71626);
        function g(e) {
            var t, o, g, b, x, y = e.hotel, k = e.className, _ = e.favoriteUse, C = e.converter, w = e.bookingParams, L = e.newHotel, M = e.trackAddToWishList, N = e.trackSelectItem, S = (0,
            i.$)().t, B = _.isFavorite, I = _.favoriteClick, E = (0,
            r.useState)(!0), H = E[0], P = E[1];
            if (!y)
                return null;
            var T = (0,
            h.eg)(null == y ? void 0 : y.sabreTaxSetup)
              , Z = null === (t = y.priceInfo) || void 0 === t ? void 0 : t[T.dailyPrice]
              , j = null == y ? void 0 : null === (o = y.priceInfo) || void 0 === o ? void 0 : o[T.dailyMemberPrice]
              , A = y.priceLoaded && (!y.priceInfo || !Z && !j)
              , F = y.priceLoaded && !!w.promoCode && y.priceInfo && !y.priceInfo.promoValid
              , D = [];
            y.priceLoaded && y.priceInfo && (Z && D.push({
                label: S(j ? "price.non_member" : "price.from"),
                price: C.getPriceWithCode(Z, y.priceInfo.currency, void 0, void 0, void 0, !0),
                color: "dark"
            }),
            j && D.push({
                label: S("price.member"),
                price: C.getPriceWithCode(j, y.priceInfo.currency, void 0, void 0, void 0, !0),
                color: "gold"
            }));
            var R = y._location.parentLocation.content
              , V = void 0;
            y.suppressIbe ? V = (0,
            n.jsx)(v.C, {
                text: y.suppressIbeMessage,
                textType: "body2",
                className: "px-3 pt-1 pb-5 tid-hotelSuppressed"
            }) : F ? V = (0,
            n.jsx)(s.Z, {
                validationHint: S("error.promo_unavailable"),
                className: "px-3 tid-promoUnavailable pt-1 pb-5"
            }) : A && R.isCustomNoAvailErrorMessage && (null === (g = R.customNoAvailErrorMessage) || void 0 === g ? void 0 : g.html5) ? V = (0,
            n.jsx)(s.Z, {
                validationHintHtml: R.customNoAvailErrorMessage.html5,
                className: "px-5 tid-promoUnavailable pt-1 pb-4"
            }) : A ? V = (0,
            n.jsx)(s.Z, {
                validationHint: S("error.hotel_unavailable"),
                className: "px-3 pt-1 pb-5 tid-hotelUnavailable"
            }) : y.priceLoading && (V = (0,
            n.jsx)(d.Z, {
                center: !0,
                className: "pb-4 transform scale-75 -mt-3"
            })),
            (0,
            r.useEffect)(function() {
                !H && M && M.trackAddToWishList({
                    hotel: y,
                    listName: (0,
                    m.u)({
                        path: window.location.pathname
                    }),
                    bookingParams: w
                })
            }, [H]);
            var O = function(e) {
                var t = e.ctaName;
                y && N.trackSelectItem({
                    hotel: y,
                    listName: (0,
                    m.u)({
                        path: window.location.pathname
                    }),
                    bookingParams: w,
                    ctaName: t
                })
            };
            return (0,
            n.jsx)("div", {
                className: "mb-5",
                children: (0,
                n.jsx)(a.Z, {
                    cardImageRound: {
                        topLeft: !0,
                        topRight: !0
                    },
                    onClick: function() {
                        return O({
                            ctaName: "Map View Card-title"
                        })
                    },
                    onImageClick: function() {
                        return O({
                            ctaName: "Map View Card"
                        })
                    },
                    className: (k || "") + " h-full",
                    shadowOnHoverEnabled: !0,
                    overlay: {
                        backgroundColor: "subtleBlack",
                        degrees: 180
                    },
                    cardDirection: void 0,
                    favoriteProps: {
                        showFavorite: !0,
                        favorite: B(y._info.id),
                        onFavoriteClick: function() {
                            return P(I(y._info.id, p.r.hotel))
                        },
                        fill: "text-white"
                    },
                    label: void 0,
                    cardClickHref: y._url,
                    images: y.photos ? y.photos.map(function(e) {
                        var t;
                        return (null == e ? void 0 : null === (t = e.image) || void 0 === t ? void 0 : t.uri) ? {
                            image: (0,
                            l.bl)(e.image.uri, u.ie),
                            imageAlt: e.image.alternativeText
                        } : {
                            image: ""
                        }
                    }).filter(function(e) {
                        return !!(null == e ? void 0 : e.image)
                    }).slice(0, 5) : void 0,
                    image: y.photos ? (0,
                    l.bl)(null === (b = y.photos[0]) || void 0 === b ? void 0 : b.image.uri, u.ie) : void 0,
                    imageAlt: null === (x = y.photos[0]) || void 0 === x ? void 0 : x.image.alternativeText,
                    hotelBrandLogo: (0,
                    f.UH)(y),
                    superHeading: void 0,
                    mainHeading: y.name,
                    place: (0,
                    c.AC)(y.city),
                    prices: D.length > 0 ? D : void 0,
                    children: V,
                    pinPopupView: !0,
                    newHotel: void 0 !== L && L
                })
            })
        }
    },
    84381: function(e) {
        e.exports = {
            popup: "DestinationsMapBox_popup__Cs3Hu",
            mapbox: "DestinationsMapBox_mapbox__53L4x",
            containerMapBoxHotel: "DestinationsMapBox_containerMapBoxHotel___tlB0",
            heightScreen: "DestinationsMapBox_heightScreen__Begtj",
            containerMapBoxOffer: "DestinationsMapBox_containerMapBoxOffer__kbgY8"
        }
    }
}]);
