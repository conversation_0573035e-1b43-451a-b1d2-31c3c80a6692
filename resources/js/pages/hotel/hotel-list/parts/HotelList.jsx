import { useEffect, useMemo, useState } from "react"
import HotelItem from "@/_components/HotelItem"
import OfferItem from "@/_components/OfferItem"
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";
import {$http, $helper} from "@/_utils/_index"
import { useAntdMessage } from "@/_components/GhaConfigProvider";

const pageSize = 18
export default function HotelList() {
  const list = useSearchStore(state => state.list)
  const slug = useSearchStore(state => state.slug)
  const loading = useSearchStore(state => state.loading) 
  const [curPage, setCurPage] = useState(1)
  const message = useAntdMessage()
  function loadMore() {
    setCurPage(curPage + 1)
  }
  useEffect(() => {
    if (loading) {
      setCurPage(1)
    }
  }, [loading])

  function onFav(item) {
    if (!$helper.protectLogin()) {
      window.modalLoginCallback = () => {
        onFavProtected(item)
      }
      return
    }
    onFavProtected(item)
  }
  function onFavProtected(item) {
    let type = slug === "hotels" ? "hotel" : "offer"
    $http.collectItem({type, id: item.id}).subscribe(res => {
      if (res.status_code !== 200) {
        message.error(res.msg)
        return
      }
      const newList = [...list]
      const curItem = newList.find(e => e.id === item.id)
      curItem.is_collect = !curItem.is_collect
      useSearchStore.setState({list: newList})
    })
  }
  return (
    <div className="">
      <div className="g-main-content min-h-64">
        {loading ? (
          <div className="py-8">
            <div className="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i className="iconfont icon-loading text-4xl"></i></div>
          </div>
        ) : (
          <>
            {list.length === 0 && (
              <div className="text-center font-18 font-semibold py-8">抱歉，未找到符合您条件的{slug === "hotels" ? '酒店' : '优惠' }。</div>
            )}
            <div className="flex flex-row flex-wrap -mx-3">
              {list.slice(0, curPage * pageSize).map(hotel => {
                return (
                  <div key={hotel.id} className="px-3 w-full md:w-1/2 lg:w-1/3 mb-6">
                    {slug === "hotels" && <HotelItem className="hotel-item-large" onFav={() => onFav(hotel)} hotel={hotel}></HotelItem>}
                    {slug === "offers" && <OfferItem offer={hotel} onFav={() => onFav(hotel)} className="hotel-item-large"></OfferItem>}
                  </div>
                )
              })}
            </div>
          </>
          
        )}
        
        {(curPage * pageSize < list.length && !loading) && (
          <div className="text-center py-4">
            <a onClick={loadMore} href="javascript:;" className="gha-btn !px-8">显示更多</a>
          </div>
        )}
      </div>
    </div>
    
  )
}