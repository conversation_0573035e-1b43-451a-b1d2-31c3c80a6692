import React, { useEffect, useRef, useState } from 'react'
import { Modal, Collapse } from "antd"
import mockjs from 'mockjs'
import {orderOptions} from "./FilterState" 
import { filterTagList } from "./TagFilter"
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";


export default function MobileFilterModal({ modalOpend, setModalOpend, onSubmit }) {
  const [filterSection] = useState(() => {
    return [
      {
        type: "order", 
        name: "排序方式", 
        tags: orderOptions
      },
      ...filterTagList
    ]
  })
  useEffect(() => {
    if (modalOpend) {
      setSelectTags([...useSearchStore.getState().selectTags, `order_${useSearchStore.getState().order}`])
    }
  }, [modalOpend])
  const [selectTags, setSelectTags] = useState(() => {
    return filterSection.map(e => `${e.type}_all`)
  })
  const items = filterSection.map(sec => {
    return {
      key: sec.type,
      label: <div className="font-bold">{sec.name}</div>,
      children: (
        <div className="filter-row-tags-list">
          {
            sec.tags.map(tag => {
              return (
                <div key={tag.id} onClick={() => onSelectTag(sec, tag)} className={`tag-item ${selectTags.includes(`${sec.type}_${tag.id}`) ? "active" : ""}`}>{tag.name}</div>
              )
            })
          }
        </div>
      )
    }
  })
  function onSelectTag(row, tag) {
    let newSelectTags = [...selectTags]
    if (row.type === "order") {
      if (newSelectTags.includes(`${row.type}_${tag.id}`)) return
      newSelectTags = newSelectTags.filter(e => !e.startsWith(`${row.type}_`))
      newSelectTags.push(`${row.type}_${tag.id}`)
    } else {
      if (tag.id === "all") {
        newSelectTags = newSelectTags.filter(e => !e.startsWith(`${row.type}_`))
        newSelectTags.push(`${row.type}_all`)
      } else {
        if (newSelectTags.includes(`${row.type}_${tag.id}`)) {
          newSelectTags = newSelectTags.filter(e => e !== `${row.type}_${tag.id}`)
          if (newSelectTags.filter(e => e.startsWith(`${row.type}_`)).length === 0) {
            newSelectTags.push(`${row.type}_all`)
          }
        } else {
          newSelectTags = newSelectTags.filter(e => e !== `${row.type}_all`)
          newSelectTags.push(`${row.type}_${tag.id}`)
        }
      }
    }
    setSelectTags(newSelectTags)
  }

  function _onClear() {
    const tags = filterTagList.map(e => `${e.type}_all`)
    const order = selectTags.find(e => e.startsWith("order_"))?.split("_")[1]
    onSubmit({selectTags: tags, order})
    setModalOpend(false)
  }
  function _onSubmit() {
    const tags = selectTags.filter(e => !e.startsWith("order_"))
    const order = selectTags.find(e => e.startsWith("order_"))?.split("_")[1]
    onSubmit({selectTags: tags, order})
    setModalOpend(false)
  }
  return (
    <>
      <Modal 
        open={modalOpend}
        footer={null}
        width={500}
        closable={false}
        destroyOnHidden
        transitionName="ant-fade"
        rootClassName='gha-antd-modal'
        centered
        zIndex={2000}
      >
        <div className="gha-antd-modal-wrapper">
          <div className="close-icon" onClick={() => setModalOpend(false)}>
            <i className="iconfont icon-Close"></i>
          </div>
          <div className="gha-antd-modal-content h-[100vh] flex flex-col">
            <div className="px-8 text-center font-14">
              <h1 className='font-20 font-bold'>筛选与排序</h1>
            </div>
            <div className="flex-1 relative w-full">
              <div className="absolute top-6 left-0 right-0 bottom-6 overflow-x-hidden overflow-y-auto">
                <div className="mobile-filter-list px-4">
                  <Collapse items={items}></Collapse>
                </div>
              </div>
            </div>
            <div className="px-4">
              <div className="flex flex-row items-center -mx-2 ">
                <div onClick={_onClear} className='gha-btn flex-1 mx-2'>全部清除</div>
                <div onClick={_onSubmit} className='gha-primary-btn flex-1 mx-2'>确定</div>
              </div>
            </div>
          </div>
        </div>

      </Modal>
    </>
  )
}