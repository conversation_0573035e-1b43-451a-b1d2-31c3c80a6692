"use strict";
(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[1141], {
    44968: function(e, t, n) {
        n.d(t, {
            j: function() {
                return i
            }
        });
        var o = n(67294)
          , r = {
            sm: "640px",
            md: "768px",
            lg: "1024px",
            xl: "1280px",
            "2xl": "1536px"
        }
          , i = function() {
            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "sm"
              , t = (0,
            o.useState)(!1)
              , n = t[0]
              , i = t[1];
            return (0,
            o.useEffect)(function() {
                var t = r[e]
                  , n = window.matchMedia("(min-width: ".concat(t, ")"));
                i(n.matches);
                var o = function(e) {
                    i(e.matches)
                };
                return n.addEventListener("change", o),
                function() {
                    n.removeEventListener("change", o)
                }
            }, [e]),
            n
        }
    },
    71265: function(e, t, n) {
        n.d(t, {
            Z: function() {
                return B
            }
        });
        var o, r, i, l = n(29815), u = n(85893), d = n(67294), c = n(6158), a = n.n(c), s = n(73935);
        n(81634);
        var v = n(84381)
          , f = n.n(v)
          , p = n(79466)
          , h = n(60461)
          , m = n(81043)
          , g = n(29806)
          , y = n(59007)
          , b = n(60023)
          , C = n(78650)
          , x = function() {
            if (o)
                return o.cloneNode(!0);
            (o = document.createElementNS("http://www.w3.org/2000/svg", "svg")).setAttribute("viewBox", "0 0 70 70"),
            o.setAttribute("fill", "none"),
            o.setAttribute("width", "32"),
            o.setAttribute("height", "32");
            var e = document.createElementNS("http://www.w3.org/2000/svg", "path");
            return e.setAttribute("fill-rule", "evenodd"),
            e.setAttribute("clip-rule", "evenodd"),
            e.setAttribute("fill", "white"),
            e.setAttribute("d", "M18.885 20.6993C20.9057 18.6482 23.6148 17.5 26.4924 17.5C29.3717 17.5 32.0798 18.6483 34.0993 20.7002L35.2917 21.9123L36.485 20.6993C38.5055 18.6484 41.213 17.5 44.0925 17.5C46.9703 17.5 49.6799 18.6484 51.7008 20.7017C55.8791 24.9489 55.8795 31.8379 51.7012 36.085L37.1638 50.8639C36.6704 51.3656 35.9961 51.6481 35.2924 51.6481C34.5887 51.6481 33.9144 51.3655 33.421 50.8638L18.8835 36.0833C14.7048 31.837 14.7063 24.9489 18.8833 20.701L18.885 20.6993Z"),
            o.appendChild(e),
            o.cloneNode(!0)
        }
          , w = function() {
            if (r)
                return r.cloneNode(!0);
            (r = document.createElementNS("http://www.w3.org/2000/svg", "svg")).setAttribute("viewBox", "0 0 70 70"),
            r.setAttribute("fill", "none"),
            r.setAttribute("width", "32"),
            r.setAttribute("height", "32");
            var e = document.createElementNS("http://www.w3.org/2000/svg", "path");
            return e.setAttribute("fill-rule", "evenodd"),
            e.setAttribute("clip-rule", "evenodd"),
            e.setAttribute("fill", "white"),
            e.setAttribute("d", "M18.885 20.6993C20.9057 18.6482 23.6148 17.5 26.4924 17.5C29.3717 17.5 32.0798 18.6483 34.0993 20.7002L35.2917 21.9123L36.485 20.6993C38.5055 18.6484 41.213 17.5 44.0925 17.5C46.9703 17.5 49.6799 18.6484 51.7008 20.7017C55.8791 24.9489 55.8795 31.8379 51.7012 36.085L37.1638 50.8639C36.6704 51.3656 35.9961 51.6481 35.2924 51.6481C34.5887 51.6481 33.9144 51.3655 33.421 50.8638L18.8841 36.0839C18.8839 36.0837 18.8837 36.0835 18.8835 36.0833C14.7048 31.837 14.7063 24.9489 18.8833 20.701L18.885 20.6993ZM22.6259 24.3828C20.4573 26.589 20.4588 30.1995 22.6258 32.4012L35.2926 45.2797L47.9586 32.4033C50.1269 30.1992 50.127 26.588 47.9587 24.384C46.9125 23.3212 45.546 22.75 44.0925 22.75C42.6375 22.75 41.272 23.3211 40.2254 24.3833C40.2255 24.3832 40.2253 24.3835 40.2254 24.3833L37.7113 26.9389C36.3938 28.2775 34.1901 28.278 32.8725 26.9394L30.3575 24.3828C30.3576 24.3829 30.3574 24.3827 30.3575 24.3828C29.3129 23.3217 27.9476 22.75 26.4924 22.75C25.039 22.75 23.6723 23.3211 22.6259 24.3828Z"),
            r.appendChild(e),
            r.cloneNode(!0)
        }
          , L = function(e) {
            return e ? x() : w()
        }
          , E = function() {
            if (i)
                return i.cloneNode(!0);
            (i = document.createElementNS("http://www.w3.org/2000/svg", "svg")).setAttribute("viewBox", "0 0 50 50"),
            i.setAttribute("fill", "none"),
            i.setAttribute("width", "30"),
            i.setAttribute("height", "30");
            var e = document.createElementNS("http://www.w3.org/2000/svg", "path");
            return i.appendChild(e),
            e.setAttribute("fillRule", "evenodd"),
            e.setAttribute("clipRule", "evenodd"),
            e.setAttribute("fill", "white"),
            e.setAttribute("d", "M24.6 13.9h-3C28 13.9 33 18.9 33 25s-5 11.1-11.2 11.1h2.9c6.1 0 11.1-5 11.1-11.1s-5-11.1-11.1-11.1ZM15.3 36H18V14h-2.8V36Z"),
            i.cloneNode(!0)
        }
          , k = n(29090)
          , S = n(1959)
          , M = n(67262)
          , A = n(53547)
          , N = n(71626);
        a().accessToken = "pk.eyJ1IjoibWFrc3ltLW1hbHRzZXYiLCJhIjoiY2tweTVkbG1tMDQ3dTJzcnJxMHV0enJ4MCJ9.2TfFQqNNty9MDAFxZriNaA";
        var F = function(e, t) {
            var n, o = e.filter(function(e) {
                var t, n, o, r, i = (0,
                N.eg)(null == e ? void 0 : null === (t = e.hotel) || void 0 === t ? void 0 : t.sabreTaxSetup), l = null === (n = null == e ? void 0 : e.hotel.priceInfo) || void 0 === n ? void 0 : n[i.dailyPrice];
                return (null == e ? void 0 : null === (o = e.hotel) || void 0 === o ? void 0 : null === (r = o.priceInfo) || void 0 === r ? void 0 : r[i.dailyMemberPrice]) || l
            }).map(function(e) {
                var n, o, r, i, l, u = (0,
                N.eg)(null == e ? void 0 : null === (n = e.hotel) || void 0 === n ? void 0 : n.sabreTaxSetup), d = null === (o = null == e ? void 0 : e.hotel.priceInfo) || void 0 === o ? void 0 : o[u.dailyPrice], c = null == e ? void 0 : null === (r = e.hotel) || void 0 === r ? void 0 : null === (i = r.priceInfo) || void 0 === i ? void 0 : i[u.dailyMemberPrice];
                return t.convertCurrency(c || d, (null === (l = e.hotel.priceInfo) || void 0 === l ? void 0 : l.currency) || "USD", "USD")
            });
            return (n = Math).min.apply(n, (0,
            l.Z)(o.filter(function(e) {
                return e
            })))
        }
          , I = function(e, t, n) {
            var o = F(t, n)
              , r = e.filter(function(e) {
                return !e.hotel.priceLoaded
            })
              , i = e.filter(function(e) {
                return e.hotel.priceLoaded
            });
            return (0,
            l.Z)(r).concat((0,
            l.Z)(i)).map(function(e) {
                var t, r, i = e.id, l = e.longitude, u = e.latitude, d = e.hotel, c = (0,
                N.eg)(null == d ? void 0 : d.sabreTaxSetup), a = null === (t = d.priceInfo) || void 0 === t ? void 0 : t[c.dailyPrice], s = null == d ? void 0 : null === (r = d.priceInfo) || void 0 === r ? void 0 : r[c.dailyMemberPrice], v = "", f = "", p = !1;
                if (d.priceLoaded && d.priceInfo) {
                    a && (f = n.getPriceWithCode(a, d.priceInfo.currency, void 0, void 0, void 0, !0)),
                    s && (v = n.getPriceWithCode(s, d.priceInfo.currency, void 0, void 0, void 0, !0));
                    var h = d.priceInfo ? n.convertCurrency(s || a, d.priceInfo.currency, "USD") : void 0;
                    p = !!h && h === o
                }
                return {
                    type: "Feature",
                    id: i,
                    properties: {
                        id: i,
                        price: v || f,
                        newHotelMessage: (0,
                        b.xM)(d) ? "New hotel" : void 0,
                        lowMessage: p ? "Lowest rate" : void 0
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [l, u]
                    }
                }
            })
        };
        function B(e) {
            var t, n, o, r = e.markers, i = e.isLoading, l = e.onPinClick, c = e.hotelPage, v = e.onClickContainerMap, x = e.heightScreen, w = e.hidePrice, F = e.hideFullScreenBtn, B = (0,
            d.useState)(), P = B[0], Z = B[1], T = (0,
            d.useState)(!1), z = T[0], D = T[1], j = (0,
            d.useRef)(null), W = (0,
            d.useState)([]), R = W[0], H = W[1], J = (0,
            d.useRef)(), V = (0,
            d.useState)(), U = V[0], _ = V[1], q = (0,
            d.useState)([]), O = q[0], Q = q[1], Y = (0,
            d.useState)(), G = Y[0], X = Y[1], K = (0,
            m.v9)(function(e) {
                return e.member.favorites.items
            }), $ = (0,
            k.Z)(), ee = (0,
            S.Z)(), et = (0,
            M.Z)(), en = (0,
            h.Z)(), eo = (0,
            m.v9)(function(e) {
                return e.global.currency.rates
            }), er = (0,
            m.v9)(function(e) {
                return e.global.currency.selectedCurrency
            }), ei = (0,
            g.Z)(er, eo), el = (0,
            m.v9)(function(e) {
                return e.booking.hotel.params
            }), eu = null === (t = r.find(function(e) {
                return e.id === U
            })) || void 0 === t ? void 0 : t.hotel, ed = (0,
            N.eg)(null == eu ? void 0 : eu.sabreTaxSetup), ec = null == eu ? void 0 : eu.priceLoaded, ea = null == eu ? void 0 : null === (n = eu.priceInfo) || void 0 === n ? void 0 : n[ed.dailyPrice], es = null == eu ? void 0 : null === (o = eu.priceInfo) || void 0 === o ? void 0 : o[ed.dailyMemberPrice], ev = (0,
            m.v9)(function(e) {
                return e.search.hotel.viewType
            });
            (0,
            d.useEffect)(function() {
                ev !== C.vt.map && _(void 0)
            }, [ev]),
            (0,
            d.useEffect)(function() {
                if (J.current && P) {
                    var e, t = document.createElement("div");
                    t.className = f().popup,
                    eu && s.render((0,
                    u.jsx)(p.Z, {
                        hotel: eu,
                        favoriteUse: en,
                        converter: ei,
                        bookingParams: el,
                        newHotel: (0,
                        b.xM)(eu),
                        trackAddToWishList: $,
                        trackSelectItem: ee
                    }), t),
                    null === (e = J.current) || void 0 === e || e.setDOMContent(t)
                }
                eu && et.trackViewItemList({
                    listName: (0,
                    A.u)({
                        path: window.location.pathname
                    }),
                    hotels: [eu],
                    hotelSearchData: el
                })
            }, [U, er, P, ec, ea, es, null == eu ? void 0 : eu.priceLoading, U && en.isFavorite(U), ]),
            (0,
            d.useEffect)(function() {
                P ? P.on("load", function() {
                    D(!0)
                }) : Z(ef())
            }, [P]),
            (0,
            d.useEffect)(function() {
                P && z && eh(P, I(r, O, ei))
            }, [z]),
            (0,
            d.useEffect)(function() {
                return function() {
                    null == P || P.remove()
                }
            }, []),
            (0,
            d.useEffect)(function() {
                if (!(null == r ? void 0 : r.length) || R.length !== r.length) {
                    eg();
                    return
                }
                var e = function(e, t) {
                    return e + t.id
                }
                  , t = R.reduce(e, "")
                  , n = r.reduce(e, "");
                t !== n && eg()
            }, [P, r, z]),
            (0,
            d.useEffect)(function() {
                if (P && z) {
                    var e, t = null === (e = I(r, O, ei).find(function(e) {
                        var t;
                        return null === (t = e.properties) || void 0 === t ? void 0 : t.lowMessage
                    })) || void 0 === e ? void 0 : e.id;
                    t && t !== G && X(t)
                }
            }, [null == O ? void 0 : O.map(function(e) {
                return e.id
            }).join(",")]),
            (0,
            d.useEffect)(function() {
                P && O.length && eh(P, I(r, O, ei))
            }, [G, U && en.isFavorite(U), K]);
            var ef = function() {
                if (j.current) {
                    var e = {
                        projection: {
                            name: "mercator"
                        },
                        container: j.current,
                        style: "mapbox://styles/maksym-maltsev/clgkmhjmg007h01qq4u4834ob",
                        zoom: 2,
                        attributionControl: !1
                    };
                    r && r.length > 0 && (e.center = [r[0].longitude, r[0].latitude]);
                    var t = new (a()).Map(e);
                    return F || t.addControl(new (a()).FullscreenControl, "top-right"),
                    t
                }
            }
              , ep = (0,
            d.useRef)([])
              , eh = function(e, t) {
                if (z) {
                    var n = []
                      , o = !0
                      , r = !1
                      , i = void 0;
                    try {
                        for (var u, d = t[Symbol.iterator](); !(o = (u = d.next()).done); o = !0)
                            !function() {
                                var t, o, r, i, d, c, s, v, p, h = u.value, m = w ? void 0 : null === (t = h.properties) || void 0 === t ? void 0 : t.price, g = null === (o = h.properties) || void 0 === o ? void 0 : o.newHotelMessage, b = m ? null === (r = h.properties) || void 0 === r ? void 0 : r.lowMessage : void 0, C = g || b, x = en.isFavorite(null === (i = h.properties) || void 0 === i ? void 0 : i.id), k = U === (null === (d = h.properties) || void 0 === d ? void 0 : d.id), S = "" + m + g + b + x + (k ? "+" : "-") + (null === (c = h.properties) || void 0 === c ? void 0 : c.id) + h.geometry.coordinates.join(",");
                                if (n.push(S),
                                ep.current.some(function(e) {
                                    return e.hash === S
                                }))
                                    return "continue";
                                var M = document.createElement("div")
                                  , A = document.createElement("p")
                                  , N = document.createElement("button");
                                N.setAttribute("type", "button"),
                                k ? M.style.zIndex = "3" : b ? M.style.zIndex = "2" : g && (M.style.zIndex = "1");
                                var F = "#300B5C";
                                if (b ? F = "#8BBCD9" : g && (F = "#F69B6F"),
                                M.style.backgroundColor = F,
                                b ? U === (null === (s = h.properties) || void 0 === s ? void 0 : s.id) ? M.style.backgroundColor = "#094F7F" : (M.addEventListener("mouseenter", function() {
                                    M.style.backgroundColor = "#094F7F"
                                }),
                                M.addEventListener("mouseleave", function() {
                                    M.style.backgroundColor = "#8BBCD9"
                                })) : g ? U === (null === (v = h.properties) || void 0 === v ? void 0 : v.id) ? M.style.backgroundColor = "#FF6D26" : (M.addEventListener("mouseenter", function() {
                                    M.style.backgroundColor = "#FF6D26"
                                }),
                                M.addEventListener("mouseleave", function() {
                                    M.style.backgroundColor = "#F69B6F"
                                })) : U === (null === (p = h.properties) || void 0 === p ? void 0 : p.id) ? M.style.backgroundColor = "#bbb0dc" : (M.addEventListener("mouseenter", function() {
                                    M.style.backgroundColor = "#bbb0dc"
                                }),
                                M.addEventListener("mouseleave", function() {
                                    M.style.backgroundColor = "#300B5C"
                                })),
                                m) {
                                    M.style.padding = "4px 56px 4px 20px",
                                    M.style.borderRadius = "5px",
                                    M.style.boxShadow = "0px 4px 4px 0px rgba(0, 0, 0, 0.25)",
                                    M.style.cursor = "pointer";
                                    var I = document.createElement("p");
                                    if (M.appendChild(I),
                                    I.style.color = "#fff",
                                    I.style.fontFamily = "Jost-SemiBold",
                                    I.style.fontSize = "18px",
                                    I.style.lineHeight = C ? "20px" : "26px",
                                    I.style.fontWeight = C ? "bold" : "500",
                                    I.textContent = m,
                                    M.appendChild(N),
                                    N.appendChild(L(x)),
                                    N.style.position = "absolute",
                                    N.style.top = "1px",
                                    N.style.right = "20px",
                                    N.style.width = "32px",
                                    N.style.height = "32px",
                                    N.style.cursor = "pointer",
                                    N.style.zIndex = "1",
                                    N.addEventListener("click", function(e) {
                                        var t;
                                        e.stopPropagation(),
                                        e.preventDefault(),
                                        en.favoriteClick(null === (t = h.properties) || void 0 === t ? void 0 : t.id, y.r.hotel)
                                    }),
                                    g && !b) {
                                        var B = document.createElement("p");
                                        M.appendChild(B),
                                        B.style.color = "#fff",
                                        B.style.fontSize = "12px",
                                        B.style.fontWeight = "bold",
                                        B.style.textTransform = "uppercase",
                                        B.textContent = g
                                    }
                                    b && (M.appendChild(A),
                                    A.style.color = "#fff",
                                    A.style.fontFamily = "Jost-SemiBold",
                                    A.style.fontSize = "12px",
                                    A.style.lineHeight = "15px",
                                    A.style.fontWeight = "bold",
                                    A.style.textTransform = "uppercase",
                                    A.textContent = b)
                                } else if (M.style.width = "".concat(30, "px"),
                                M.style.height = "".concat(30, "px"),
                                M.style.borderRadius = "50%",
                                M.style.cursor = "pointer",
                                x) {
                                    var P = L(x);
                                    P.style.left = "-0.9px",
                                    P.style.position = "absolute",
                                    M.appendChild(P)
                                } else {
                                    var Z = E();
                                    Z.style.left = "1px",
                                    Z.style.position = "absolute",
                                    M.appendChild(Z)
                                }
                                M.addEventListener("click", function(t) {
                                    if (t.preventDefault(),
                                    t.stopPropagation(),
                                    e) {
                                        for (var n, o, r = null === (n = h.properties) || void 0 === n ? void 0 : n.id, i = h.geometry.coordinates.slice(); Math.abs(i[0] - e.getCenter().lng) > 180; )
                                            i[0] = Number(i[0]) + (e.getCenter().lng > i[0] ? 360 : -360);
                                        J.current = J.current || new (a()).Popup({
                                            closeButton: !1,
                                            className: f().popup,
                                            closeOnMove: !1,
                                            offset: 18,
                                            anchor: "top",
                                            maxWidth: "320px",
                                            focusAfterOpen: !0
                                        }).on("close", function() {
                                            return _(void 0)
                                        }),
                                        null === (o = J.current) || void 0 === o || o.setLngLat(i).addTo(e),
                                        e.flyTo({
                                            center: i,
                                            offset: [0, -200],
                                            duration: 300,
                                            curve: 0
                                        }),
                                        null == l || l(r),
                                        _(r)
                                    }
                                });
                                var T = new (a()).Marker(M).setLngLat(h.geometry.coordinates).addTo(e);
                                ep.current.push({
                                    hash: S,
                                    marker: T
                                })
                            }()
                    } catch (c) {
                        r = !0,
                        i = c
                    } finally {
                        try {
                            o || null == d.return || d.return()
                        } finally {
                            if (r)
                                throw i
                        }
                    }
                    if (ep.current.length)
                        for (var s = ep.current.length - 1; s >= 0; s--)
                            n.includes(ep.current[s].hash) || (ep.current[s].marker.getElement().remove(),
                            ep.current[s].marker.remove(),
                            ep.current.splice(s, 1))
                }
            }
              , em = null == P ? void 0 : P.getBounds();
            (0,
            d.useEffect)(function() {
                var e = function() {
                    P && em && Q(r.filter(function(e) {
                        var t = e.latitude
                          , n = e.longitude;
                        return n >= em.getWest() && n <= em.getEast() && t >= em.getSouth() && t <= em.getNorth()
                    }))
                };
                return null == P || P.on("zoomend", e),
                null == P || P.on("moveend", e),
                function() {
                    null == P || P.off("zoomend", e),
                    null == P || P.off("moveend", e)
                }
            }, [em ? Math.round(1e3 * em.getWest()) : null, em ? Math.round(1e3 * em.getSouth()) : null, !P, null == r ? void 0 : r.map(function(e) {
                return e.id
            }).join(",")]);
            var eg = function() {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}
                  , t = e.customPadding
                  , n = e.customMaxZoom;
                if (P && z) {
                    if (!i && r.length) {
                        var o = r.map(function(e) {
                            return [e.longitude, e.latitude]
                        }).reduce(function(e, t) {
                            return e.extend(t)
                        }, new (a()).LngLatBounds);
                        U || P.fitBounds(o, {
                            padding: void 0 === t ? 100 : t,
                            maxZoom: void 0 === n ? 12 : n
                        })
                    }
                    eh(P, I(r, O, ei)),
                    H(r)
                }
            };
            return (0,
            u.jsx)("div", {
                className: c ? "h-full w-full" : "w-full absolute z-20 left-0 md:relative md:px-6 md:w-full ".concat(f().containerMapBoxHotel, " ").concat(x && f().heightScreen),
                onClick: v,
                style: {
                    borderRadius: "14px"
                },
                children: (0,
                u.jsx)("div", {
                    ref: j,
                    style: {
                        borderRadius: "14px"
                    },
                    className: "h-full w-full ".concat(c ? "" : "shadow-lg", " ").concat(f().mapbox)
                })
            })
        }
    }
}]);
