import { createRoot } from "react-dom/client";
import { Checkbox } from "antd"
import OrderStep from "@/pages/booking/select-room/parts/OrderStep";
import OrderDetailWidget from "@/pages/booking/widgets/OrderDetailWidget";
import OrderPriceDetailWidget from "@/pages/booking/widgets/OrderPriceDetailWidget";
import GhaConfigProvider from "@/_components/GhaConfigProvider"

function App() {
  return (
    <>
      <div className="py-12">
        <OrderStep step={3}/>
      </div>
      <div className="">
        <div className="g-main-content-sm">
          <div className="text-[#999] font-14">
            <a href="javascript:;" className="flex flex-row items-center"><i className="iconfont icon-left font-20"></i>返回</a>
          </div>
        </div>
      </div>
      <div className="mt-3">
        <div className="g-main-content-sm">
          <div className="flex flex-col">
            <OrderDetailWidget title={"请核对您的预订信息"} hasUserInfo/>
            <OrderPriceDetailWidget/>
          </div>
        </div>
      </div>
      <div className="mt-4">
        <div className="g-main-content-sm">
          <Checkbox>按立即预订即表示您同意您已阅读<a href="javascript:;" className="underline">条款和条件</a>，您的信用卡将不会被收取费用，并且仅用于保证预订，除非房价说明中另有说明。</Checkbox>
        </div>
      </div>
      <div className="mt-8">
        <div className="g-main-content-sm">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <p className="font-12 text-[#320e5e] mb-4 md:mb-0">通过此次预订，您最多可赚取<span className="text-[#e69d4a]">D$219</span></p>
            <a href="/booking/booking-success" className="gha-primary-btn w-full md:w-auto md:!px-10">下一步,确认订单</a>
          </div>
        </div>
      </div>
    </>
  )
}

createRoot(document.querySelector(".confirm-order-content")).render(<GhaConfigProvider><App /></GhaConfigProvider>);