import { useMemo, useState } from "react"
import { Modal } from "antd"
import { $helper, $http, $storage, $constants } from "@/_utils/_index"

export default function OrderDetailWidget({className, title, hasUserInfo, hasOrderTitle, bookingData, onBackToEdit, editable = true}) {
  console.error("bookingData", bookingData)
  const [policyModalOpend, setPolicyModalOpened] = useState(false)
  const date = useMemo(() => {
    return $helper.parseBookingQuery(bookingData.cond).date
  }, [bookingData])
  const peopleString = useMemo(() => {
    const rooms = $helper.parseBookingQuery(bookingData.cond).rooms
    const adults = rooms.reduce((acc, val) => {
      return acc + val.adult;
    }, 0)
    const children = rooms.reduce((acc, val) => {
      return acc + val.child;
    }, 0)
    return `成人 ${adults}， 儿童 ${children}`
  }, [bookingData])
  const orderState = useMemo(() => {
    const state = bookingData?.orderInfo?.reservationStatus
    return {"CONFIRMED": "已确认", "CANCELED": "已取消"}[state]
  }, [bookingData])
  return (
    <>
      <div className={`order-room-detail-wrap ${className || ""}`}>
        <h2 className="font-20 font-semibold">{title || "您的预订"}</h2>
        {hasOrderTitle ? (
          <>
            <div className="pb-6 pt-2 font-14 border-b border-[#c9c9c9]/50">
              {orderState && <p>订单状态：<span className="text-primary">{orderState}</span></p>}
              <p className="mt-0.5">订单号：{bookingData.orderInfo.itinerary_number}</p>
            </div>
            <div className="flex flex-row items-center border-b border-[#c9c9c9]/50 py-6">
              <div className="flex-1">
                <h4 className="font-16 mt-1">{bookingData.hotel.hotel_name}</h4>
                <div className="flex flex-row items-start font-14 mt-1">
                  <i className="iconfont icon-Location text-[#bbb] text-[22px]"></i>
                  {bookingData.hotel.address}
                </div>
              </div>
              <div className="ml-2 w-24 h-14 md:w-36 md:h-20 rounded-lg border border-[#efefef] shadow-md flex items-center justify-center">
                <img className="max-w-[90%] max-h-[90%]" src={bookingData.hotel.brand_img} alt="" />
              </div>
            </div>
          </>
          
        ) : (
          <div className="border-b border-[#c9c9c9]/50 py-6">
            {/* <h3 className="font-18 mt-2">{bookingData.hotel.brand_name}</h3> */}
            <img className="max-w-20 max-h-20" src={bookingData.hotel.brand_img} alt="" />
            <h4 className="font-16 mt-1">{bookingData.hotel.hotel_name}</h4>
            <div className="flex flex-row items-start font-14 mt-1">
              <i className="iconfont icon-Location text-[#bbb] text-[22px]"></i>
              {bookingData.hotel.address}
            </div>
          </div>
        )}
        <div className="py-6 border-b border-[#c9c9c9]/50">
          <div className="flex flex-row items-center justify-between">
            <h3 className="font-16 font-semibold">日期</h3>
            {editable && <i onClick={onBackToEdit} className="iconfont icon-edit cursor-pointer text-[20px]"></i>}
          </div>
          <div className="font-14 mt-1">
            <p>{date[0].format("YYYY-MM-DD")}至{date[1].format("YYYY-MM-DD")}， 2晚</p>
            <p>{peopleString}</p>
          </div>
        </div>
        <div className="pt-6">
          <div className="flex flex-row items-center justify-between">
            <h3 className="font-16 font-semibold">房间</h3>
            {editable && <i onClick={onBackToEdit} className="iconfont icon-edit cursor-pointer text-[20px]"></i>}
          </div>
          <div className="">
            {bookingData.curRates.map((_, index) => {
              return (
                <div key={index} className="mt-3 first:mt-1">
                  <h4 className="font-15">房间{index + 1}：</h4>
                  <p className="font-14 mt-1">{_.room.roomName},{_.rate.rateName}</p>
                  <a onClick={() => {
                    setPolicyModalOpened(true)
                  }} className="font-14 mt-1 text-primary underline" href="javascript:;" >取消及税收政策</a>
                </div>
              )
            })}
          </div>
        </div>
        {hasUserInfo && (
          <>
            <div className="mt-6 pt-6 border-t border-[#c9c9c9]/50">
              <h3 className="font-16 font-semibold">入住人信息</h3>
              <h4 className="font-16 mt-1 font-IvyMode-Reg">{bookingData.userInfo.firstName} {bookingData.userInfo.lastName}</h4>
              {$helper.isLogin() && <p className="font-14 mt-1">SILVER 会员号：8870636037</p>}
              <p className="font-14 mt-1">邮箱：{bookingData.userInfo.email}</p>
              <p className="font-14 mt-1">手机号：{bookingData.userInfo.phone}</p>
            </div>
          </>
          
        )}
      </div>
      <Modal 
        open={policyModalOpend}
        footer={null}
        width={700}
        closable={false}
        destroyOnHidden
        transitionName="ant-fade"
        rootClassName='gha-antd-modal'
      >
        <div className="gha-antd-modal-wrapper">
          <div className="close-icon" onClick={() => setPolicyModalOpened(false)}>
            <i className="iconfont icon-Close"></i>
          </div>
          <div className="gha-antd-modal-content">
            <div className="px-8">
              <h2 className="font-20 text-center mt-1 font-bold">取消及税收政策</h2>
              <div className="">
                {bookingData.curRates.map((_, idx) => {
                  return (
                    <div key={idx} className="py-6 border-b border-[#919191]/20 font-14 last:border-b-0">
                      <div className="">
                        <p className="font-13 mb-0.5">房间{idx+1}</p>
                        <h4 className="font-18 font-IvyMode-Reg">{_.room.roomName}</h4>
                      </div>
                      <div className="mt-2">
                        <p className="font-13 mb-0.5">房价</p>
                        <p>{_.rate.rateName}</p>
                      </div>
                      {Boolean(_.rate.cancelRuleString) && (
                        <div className="mt-2">
                          <p className="font-13 mb-0.5">取消</p>
                          <p>{_.rate.cancelRuleString}</p>
                        </div>
                      )}
                      <div className="mt-2">
                        <p className="font-13 mb-0.5">押金：</p>
                        <p>信用卡担保</p>
                      </div>
                      <div className="mt-2">
                        <p className="font-bold font-15 mb-0.5">税金</p>
                        <p className="mt-1">服务费：{$helper.formatPrice(_.rate.cny_fee / 100)}</p>
                        <p className="mt-1">税额：{$helper.formatPrice(_.rate.cny_tax / 100)}</p>
                      </div>
                      
                    </div>
                  )
                })}
              </div>
            </div>
            <div className="pt-6 border-t border-[#919191]/20 hidden">
                <div className="px-8">
                  <h4 className="font-bold font-16">税金</h4>
                  <p className="mt-1">10%服务费-953元人民币</p>
                  <p className="mt-1">增值税-153元人民币</p>
                </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}