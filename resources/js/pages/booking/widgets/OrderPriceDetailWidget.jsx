import { useMemo, useState } from "react"
import { Modal } from "antd"
import { $helper, $http, $storage, $constants } from "@/_utils/_index"

export function PriceDetailModal({modalOpend, setModalOpened, bookingData}) {
  const amount = useMemo(() => {
    const curRates = bookingData?.curRates || []
    return {
      cny_price: curRates.reduce((acc, val) => {
        return acc + val.rate.cny_price
      }, 0),
      cny_tax: curRates.reduce((acc, val) => {
        return acc + val.rate.cny_tax + val.rate.cny_fee
      }, 0),
      cny_total_price: curRates.reduce((acc, val) => {
        return acc + val.rate.cny_total_price
      }, 0)
    }
  }, [bookingData])
  return (
    <Modal 
      open={modalOpend}
      footer={null}
      width={700}
      closable={false}
      destroyOnHidden
      transitionName="ant-fade"
      rootClassName='gha-antd-modal'
    >
      <div className="gha-antd-modal-wrapper">
        <div className="close-icon" onClick={() => setModalOpened(false)}>
          <i className="iconfont icon-Close"></i>
        </div>
        <div className="gha-antd-modal-content">
          <div className="px-8">
            <div className="gha-divider w-32 mx-auto"><p className="px-2 text-[#0c0509]/20">价格明细</p></div>
            <h2 className="font-20 text-center mt-1">{bookingData.hotel.hotel_name}</h2>
            <div className="mt-6">
              {bookingData.curRates.map((_, idx) => {
                return (
                  <div key={idx} className="p-6 border border-[#919191]/20 shadow-md rounded-lg mt-3 first:mt-0 font-14">
                    <h4 className="font-15">{_.room.roomName}</h4>
                    <p className="mt-1">{_.rate.rateName}</p>
                    <div className="mt-1">
                      {_.rate.dailyPrices.map(day => {
                        return (
                          <div key={day.date} className="py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between">
                            <p>{day.date}</p>
                            <p className="font-IvyMode-Reg">{$helper.formatPrice(day.cny_price / 100)}</p>
                          </div>
                        )
                      })}
                    </div>
                    <div className="mt-1 py-1.5 flex flex-row items-center justify-between">
                      <p>房间{idx + 1}合计</p>
                      <p className="font-IvyMode-Reg font-16">{$helper.formatPrice(_.rate.cny_price / 100)}</p>
                    </div>
                  </div>
                )
              })}
              
            </div>
          </div>
          <div className="mt-8 pt-2 border-t border-[#919191]/20">
              <div className="px-8">
                <div className="py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between">
                  <p>小计</p>
                  <p className="font-IvyMode-Reg">{$helper.formatPrice(amount.cny_price / 100)}</p>
                </div>
                <div className="py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between">
                  <p>税费</p>
                  <p className="font-IvyMode-Reg">{$helper.formatPrice(amount.cny_tax / 100)}</p>
                </div>
                <div className="py-1.5 flex flex-row items-center justify-between">
                  <p>总计</p>
                  <p className="font-IvyMode-Reg font-18">{$helper.formatPrice(amount.cny_total_price / 100)}</p>
                </div>
              </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default function OrderPriceDetail({className, bookingData}) {
  const [modalOpend, setModalOpened] = useState(false)
  const [cancelModalOpend, setCancelModalOpened] = useState(true)
  const amount = useMemo(() => {
    const curRates = bookingData?.curRates || []
    return {
      cny_price: curRates.reduce((acc, val) => {
        return acc + val.rate.cny_price
      }, 0),
      cny_tax: curRates.reduce((acc, val) => {
        return acc + val.rate.cny_tax + val.rate.cny_fee
      }, 0),
      cny_total_price: curRates.reduce((acc, val) => {
        return acc + val.rate.cny_total_price
      }, 0)
    }
  }, [bookingData])
  return (
    <>
      <div className={`price-detail-wrap ${className || ""}`}>
        <div className="flex flex-row items-center justify-between font-16">
            <h3 className="font-16 font-semibold">价格详情</h3>
            <a className="underline font-14" href="javascript:;" onClick={() => setModalOpened(true)}>查看价格明细</a>
        </div>
        <div className="py-3 border-b border-[#c9c9c9]/20 mt-2">
          <div className="flex flex-row items-center justify-between font-14">
            <p>小计</p>
            <p>{$helper.formatPrice(amount.cny_price / 100)}</p>
          </div>
        </div>
        <div className="py-3 border-b border-[#c9c9c9]/20">
          <div className="flex flex-row items-center justify-between font-14">
            <p>税费</p>
            <p>{$helper.formatPrice(amount.cny_tax / 100)}</p>
          </div>
        </div>
        <div className="pt-4">
          <div className="flex flex-row items-center justify-between font-14">
            <p className="font-medium">总计</p>
            <p className="font-20 font-IvyMode-Reg">{$helper.formatPrice(amount.cny_total_price / 100)}</p>
          </div>
        </div>
      </div>
      <PriceDetailModal modalOpend={modalOpend} setModalOpened={setModalOpened} bookingData={bookingData}/>
    </>
    
  )
}