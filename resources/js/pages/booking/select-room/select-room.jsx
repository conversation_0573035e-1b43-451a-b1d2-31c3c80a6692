import { createRoot } from "react-dom/client";
import { GhaHotelDetailSearchBar } from "@/_components/gha-search-bar/GhaSearchBar"
import OrderStep from "./parts/OrderStep";
import RoomItem from "./parts/RoomItem";
import { useEffect, useMemo, useState, useRef } from "react";
import { $helper, $http, $storage } from "@/_utils/_index"
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore";
import { PriceDetailModal } from "@/pages//booking/widgets/OrderPriceDetailWidget";

function App() {
  const reqSubscription = useRef(null)
  const [pageLoading, setPageLoading] = useState(false);
  const [roomLoading, setRoomLoading] = useState(false);
  const [hotelInfo, setHotelInfo] = useState(null);
  const [roomList, setRoomList] = useState([]);
  const [roomError, setRoomError] = useState("")
  const [showAllRooms, setShowAllRooms] = useState(false)
  const searchBarRooms = useGhaSearchBarStore(state => state.rooms)
  const [selectRates, setSelectRates] = useState([])
  const [modalOpened, setModalOpened] = useState(false)
  const [curRates, setCurRates] = useState([])
  const [curRoomIdx, setCurRoomIdx] = useState(0)
  const urlParams = useMemo(() => {
    return $helper.parseBookingQuery(location.search.replace("?", ""))
  }, [])
  const isEdit = useMemo(() => {
    return urlParams.edit === "1"
  }, [urlParams])
  const bookingData = useMemo(() => {
    return $storage.getLocalBookingData()
  }, [])
  useEffect(() => {
    bootstrap()
  }, [])
  useEffect(() => {
    $helper.getGlobalSubject().on("Booking_Select_Room_Cond_Updated", onBooking_Select_Room_Cond_Updated, "SelectRoom")
    return () => {
      $helper.getGlobalSubject().off("Booking_Select_Room_Cond_Updated", onBooking_Select_Room_Cond_Updated, "SelectRoom")
    }
  }, [])
  useEffect(() => {
    if (history.scrollRestoration) {
      history.scrollRestoration = 'manual';
    }
  }, [])
  function onBooking_Select_Room_Cond_Updated() {
    console.error("onBooking_Select_Room_Cond_Updated", useGhaSearchBarStore.getState())
    onSearch()
  }
  function bootstrap() {
    if (isEdit) {
      setHotelInfo(bookingData.hotel)
      return
    }
    setPageLoading(true)
    const params = $helper.parseBookingQuery(location.search.replace("?", ""))
    $http.getHotelInfo(params.hotelID).subscribe(res => {
      if (res.status_code === 200) {
        setHotelInfo(res.data.hotel)
        setPageLoading(false)
      }
    })
  }

  function buildSearchParams({rooms, date, promoCode, hotelId}) {
    return {
      hotelId: hotelId,
      startDate: date[0].format("YYYY-MM-DD"),
      endDate: date[1].format("YYYY-MM-DD"),
      numRooms: rooms.length,
      adults: rooms.reduce((acc, val) => {
        return acc + val.adult;
      }, 0),
      children: rooms.reduce((acc, val) => {
        return acc + val.child;
      }, 0),
      childrenAge: rooms.reduce((acc, val) => {
        return acc.concat(val.childAges);
      }, []).join(","),
      accessCode: promoCode || "",
      onlyPromo: '0',
      offers: urlParams.offerID || "0"
    }
  }

  useEffect(() => {
    if (hotelInfo) {
      const { rooms, date, promoCode } = urlParams
      const requestParams = buildSearchParams({hotelId: hotelInfo.synxis_hotel_id, rooms, date, promoCode})
      window.$hotel = hotelInfo
      requestRoomList(requestParams)
    }
  }, [hotelInfo])

  function requestRoomList(requestParams) {
    if (reqSubscription.current) {
      reqSubscription.current.unsubscribe()
      reqSubscription.current = null
    }
    setRoomLoading(true)
    setShowAllRooms(false)
    setSelectRates([])
    setRoomError("")
    reqSubscription.current = $http.checkHotelRoomAvailability(requestParams).subscribe(res => {
      setRoomLoading(false)
      if (res.status_code !== 200) {
        setRoomError(res.message || "该日期已售罄，建议您尝试其他日期")
        return;
      }
      const list = res.data.rooms || []
      if (urlParams.roomCode) {
        list.sort((a, b) => {
          if (a.roomCode === urlParams.roomCode) return -1; // a放前面
          if (b.roomCode === urlParams.roomCode) return 1;  // b放前面
          return 0;                     // 其他保持原顺序
        })
      }
      setRoomList(list)
      window.scrollTo({top: 0})
    })
  }
  function onSearch() {
    const { rooms, date, promoCode } = useGhaSearchBarStore.getState()
    if (!location.hash) {
      location.hash = $helper.hash()
    }
    const requestParams = buildSearchParams({hotelId: window.$hotel.synxis_hotel_id, rooms, date, promoCode})
    requestRoomList(requestParams)
  }

  function onRoomSelect(payload) {
    if (payload.rate.isMemberRate) {
      if (!$helper.protectLogin()) {
        window.modalLoginCallback = () => {
          onRoomSelectProtected(payload)
        }
        return
      }
    }
    onRoomSelectProtected(payload)
  }
  function onRoomSelectProtected(payload) {
    // setCurRates()
    const _bookingData = {
      hotel: hotelInfo,
      roomList: roomList,
      curRates: [payload],
      cond: $helper.buildBookingQuery({
        date: useGhaSearchBarStore.getState().date,
        rooms: useGhaSearchBarStore.getState().rooms,
        promoCode: useGhaSearchBarStore.getState().promoCode,
        hotelID: hotelInfo.id
      }),
      ...(isEdit ? {userInfo: bookingData.userInfo, orderInfo: bookingData.orderInfo} : {})
    }
    $storage.setLocalBookingData(JSON.stringify(_bookingData))
    const proParams = []
    if (isEdit) {
      proParams.push("edit=1")
    }
    if (urlParams.itineraryNumber) {
      proParams.push(`itineraryNumber=${urlParams.itineraryNumber}`)
    }
    const proQuery = proParams.join("&")
    location.href = `/booking/input-order-detail${proQuery ? `?${proQuery}` : ""}`
  }
  return pageLoading ? (
    <div className="py-8">
      <div className="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i className="iconfont icon-loading text-4xl"></i></div>
    </div>
  ) : (
    <>
      <div className="hotel-top" style={{backgroundImage: `url(${hotelInfo?.image})`}}>
        <div className="absolute top-0 left-0 right-0 bottom-0" style={{ backgroundImage: `linear-gradient(90deg, ${$helper.hexToRgba(hotelInfo?.gradient_color || '#300B5C')} 50%, rgba(111, 202, 221, 0) 80%)` }}></div>
        <div className="g-main-content-sm relative z-20">
          <div className="flex flex-row items-center">
            <div className="logo">
              <img src={hotelInfo?.brand_img} alt=""/>
            </div>
            <h1>{hotelInfo?.hotel_name}</h1>
          </div>
          {urlParams.itineraryNumber ? (
            <div className="booking-edit-tip">
              <p>请重新选择您的房型</p>
              <p>订单号：{urlParams.itineraryNumber}</p>
            </div>
          ) : (
            <div className="back trigger-back">
              <a><i className="iconfont icon-left"></i>返回</a>
            </div>
          )}
          
        </div>
      </div>
      <div className="-mt-[39px] relative">
        <div className="g-main-content-sm">
            <div className="shadow-md border border-[#ebebeb]/20 rounded-xl overflow-hidden">
                <GhaHotelDetailSearchBar sticky isBooking onSubmit={onSearch}/>
            </div>
        </div>
      </div>
      <>
        {urlParams.itineraryNumber ? null : (
          <div className="py-12">
            <OrderStep/>
          </div>
        )}
        <div className={`${urlParams.itineraryNumber ? 'mt-12' : ''}`}>
          <div className="g-main-content-sm">
            <div className="bg-[#bbb0dd]/80 rounded-xl text-white font-13 flex flex-row items-center px-3 md:px-8 py-3">
                <i className="iconfont icon-a-FrontDesk font-40 mr-2"></i>
                <div>
                    为符合条件的会员和另一位入住同一房间的客人提供免费早餐，提升您的住宿体验。直接预订时，探索之旅Titanium会员专享。<br/>
                    <a href="javascript:;" className="underline">条款和条件</a>适用。
                </div>
            </div>
          </div>
        </div>
        {searchBarRooms.length > 1 && (
          <>
            <div className="py-6">
              <div className="g-main-content-sm">
                <div className="flex flex-row items-center">
                  <p>选择客房</p>
                </div>
              </div>
            </div>
          </>
        )}
        {roomError ? (
          <>
            <div className="py-12">
              <div className="flex flex-row items-center justify-center text-red-600 font-16">
                <i className="iconfont icon-Shapex mr-2"></i>
                {roomError}
              </div>
            </div>
          </>
        ) : (
          <>
            {roomLoading ? (
              <div className="py-12">
                <div className="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i className="iconfont icon-loading text-4xl"></i></div>
              </div>
            ) : (
              <>
                <div className="py-12">
                  <div className={`g-main-content-sm`}>
                    <div className={`room-list-wrap ${showAllRooms ? 'show-all' : ''}`}>
                      {roomList.map((_, index) => {
                        return (
                          <RoomItem 
                            selectRates={selectRates} 
                            searchBarRooms={searchBarRooms}
                            key={_.roomCode} 
                            data={_}
                            curRates={curRates}
                            onRoomSelect={(payload) => onRoomSelect(payload)}
                          />
                        )
                      })}
                    </div>
                    
                  </div>
                </div>
                <div onClick={() => setShowAllRooms(true)} className={`text-center ${(showAllRooms || roomList.length <= 2) ? 'hidden' : ''}`}>
                  <a href="javascript:;" className="gha-btn">显示更多</a>
                </div>
              </>
            )}
          </>
        )}
      </>
    </>
  )
}

createRoot(document.querySelector(".select-room-content")).render(<App />);