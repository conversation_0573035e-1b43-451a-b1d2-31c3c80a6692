import React, { useState, useRef, useMemo } from 'react'
import { Swiper, SwiperSlide } from "swiper/react"
import { Navigation, Pagination } from 'swiper/modules';
import { Modal } from "antd"
import $helper from '@/_utils/helper';

export default function RoomItem({data, selectRates, onSelectUpdate, onRoomSelect, curRates}) {
  const [ showMorePrice, setShowMorePrice ] = useState(false)
  const componentId = useRef(Math.random().toString().slice(2, 10))
  const [ curSlide, setCurSlide ] = useState(1)
  const [ modalCurSlide, setModalCurSlide ] = useState(1)
  const [modalOpend, setModalOpend] = useState(false)
  function onPlus(rate) {
    onSelectUpdate({action: 'plus', roomCode: data.roomCode, rateCode: rate.rateCode})
  }
  function onMinus(rate) {
    onSelectUpdate({action: 'minus', roomCode: data.roomCode, rateCode: rate.rateCode})
  }
  function onNext() {
    location.href = `/booking/input-order-detail`
  }
  return (
    <>
      <div className={`hotel-room-item componentId-${componentId.current}`}>
        <div className="left font-14">
          <div className="swiper-el gha-swiper">
            <div className="absolute top-0 left-0 right-0 bottom-0">
              <div className="gha-swiper-button gha-swiper-button-prev"><i className="iconfont icon-a-Arrow-Left"></i></div>
              <div className="gha-swiper-button gha-swiper-button-next"><i className="iconfont icon-a-Arrow-Right"></i></div>
              <div className="swiper-pagination"></div>
              <div className="nums">{curSlide}/{data.room_images.length}</div>
              <Swiper 
                className='h-full'
                modules={[Pagination, Navigation]}
                onSlideChange={e => setCurSlide(e.activeIndex + 1)}
                pagination={true}
                navigation={{
                  nextEl: `.componentId-${componentId.current} .gha-swiper-button-next`, 
                  prevEl: `.componentId-${componentId.current} .gha-swiper-button-prev`, 
                  clickable: true
                }}
              >
                {data.room_images.map((_, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <img className='h-full' src={_} alt="" />
                    </SwiperSlide>
                  )
                })}
              </Swiper>
            </div>
          </div>
          <div className="mt-3 hidden md:block">
            {data.facility.slice(0, 3).map((e, idx) => {
              return (
                <div key={idx} className="flex flex-row items-center mt-1.5 first:mt-0">
                  <img className='w-4 mr-1' src={e.icon} alt="" />
                  {/* <i className="iconfont icon-Programme_icons_celebration !text-lg mr-1 !leading-none"></i> */}
                  {e.name}
                </div>
              )
            })}
          </div>
          <div className="mt-1.5 hidden md:block">
            <a href="javascript:;" className="underline text-primary" onClick={() => setModalOpend(true)}>查看客房信息</a>
          </div>
        </div>
        <div className="right">
          <div className="room-title">
            <h2>{data.room_name || data.roomName}</h2>
            <p dangerouslySetInnerHTML={{__html: data.room_desc}}></p>
            <div className="text-right mt-2 md:hidden">
              <a href="javascript:;" className='underline text-primary font-14' onClick={() => setModalOpend(true)}>查看客房信息</a>
            </div>
          </div>
          <div className={`price-list`}>
            {data.roomRateList.slice(0, showMorePrice ? 9999 : 2).map((rate, index) => {
              const price = (rate.averagePrice || 0) / 100
              const isSelect = curRates.some(e => e.rate.rateCode === rate.rateCode && data.roomCode === e.room.roomCode)
              return (
                <div key={rate.rateCode} className={`price-item ${rate.is_highlight ? "bg-[#f8f8f8]" : ""}`}>
                  <div className="desc-info">
                    <h4>{rate.rateName}</h4>
                    <h5>{rate.rateDescription}</h5>
                    <div className="flex-1"></div>
                    <div className="mt-2">
                      <p>{rate.cancelRuleString}</p>
                    </div>
                  </div>
                  <div className={`price-info ${price <= 0 ? 'hidden' : ''}`}>
                    <div className="">
                      <div className={`${rate.isMemberRate ? 'text-[#e69d4a]' : ''}`}>
                        {rate.isMemberRate ? <h5>会员价</h5> : null }
                        <h4 className="font-IvyMode-Semi-Bd">{$helper.formatPrice(price)}<span>/晚</span></h4>
                      </div>
                      <p>包含税费及服务费</p>
                    </div>
                    
                    <div className="flex-1"></div>
                    <div className="mt-0 md:mt-8">
                      <div onClick={() => onRoomSelect({rate, room: data})} className="gha-primary-btn !py-1 w-full md:w-auto">
                        {!$helper.isLogin() && rate.isMemberRate ? "会员价预订" : "立即预订"}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
          <div className="toggle-more">
            <div onClick={() => setShowMorePrice(!showMorePrice)} className="cursor-pointer flex flex-row items-center"><span>{!showMorePrice ? "展开" : "收起"}</span><i className={`iconfont ${!showMorePrice ? "icon-zhankai" : "icon-shouqi"}`}></i></div>
          </div>
        </div>
      </div>
      <Modal 
        open={modalOpend}
        footer={null}
        width={700}
        closable={false}
        destroyOnHidden
        zIndex={9999}
        transitionName="ant-fade"
        getContainer={false}
        rootClassName={`gha-antd-modal hotel-room-info-modal`}
      >
        <div className='relative'>
          <div className="absolute flex items-center justify-center -top-6 -right-2 lg:top-0 lg:-right-20 w-12 h-12 bg-white rounded-full cursor-pointer z-20" 
            onClick={() => {
              setModalOpend(false)
              setModalCurSlide(1)
            }}>
            <i className='iconfont icon-Close font-24'></i>
          </div>
          <div className={`swiper-el modal-${componentId.current}`}>
            <div className="absolute top-0 left-0 right-0 bottom-0">
              <div className="gha-swiper-button gha-swiper-button-prev"><i className="iconfont icon-a-Arrow-Left"></i></div>
              <div className="gha-swiper-button gha-swiper-button-next"><i className="iconfont icon-a-Arrow-Right"></i></div>
              <div className="swiper-pagination"></div>
              <div className="nums">{modalCurSlide}/{data.room_images.length}</div>
              <Swiper 
                className='h-full'
                modules={[Pagination, Navigation]}
                onSlideChange={e => setModalCurSlide(e.activeIndex + 1)}
                pagination={true}
                // navigation={true}
                navigation={{
                  nextEl: `.modal-${componentId.current} .gha-swiper-button-next`, 
                  prevEl: `.modal-${componentId.current} .gha-swiper-button-prev`, 
                  clickable: true
                }}
              >
                {data.room_images.map((_, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <img className='h-full w-full' src={_} alt="" />
                    </SwiperSlide>
                  )
                })}
              </Swiper>
            </div>
          </div>
          <div className="px-4 py-6">
            <h2 className="font-20 font-semibold">{data.room_name}</h2>
            {Boolean(data.room_size) && (
              <div className="flex flex-row items-center mt-1">
                <i className="iconfont icon-mianji mr-2"></i> {data.room_size}
              </div>
            )}
            <div className="my-4 w-20 h-0.5 bg-black"></div>
            <p dangerouslySetInnerHTML={{__html: data.room_desc}}></p>
            <div className="gha-divider my-3"><p className="px-4 font-bold">客房设施</p></div>
            <div className="flex flex-row flex-wrap -mx-4">
              {data.facility.map((_, index) => {
                return (
                  <div key={index} className="w-1/2">
                    <div className="px-4 flex flex-row items-center mt-1">
                      <img className='w-4 mr-1' src={_.icon} alt="" />
                      <p className="ml-1">{_.name}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </Modal>
    </>
    
  )
}