import React, { useState } from 'react'
import { Form, Input, Button } from "antd"


// <PERSON><PERSON> 算法验证信用卡号
export const validateCardNumber = (number) => {
  if (!/^\d{13,19}$/.test(number)) return false;
  return true
  let sum = 0;
  let shouldDouble = false;
  for (let i = number.length - 1; i >= 0; i--) {
    let digit = parseInt(number.charAt(i), 10);
    if (shouldDouble) {
      digit *= 2;
      if (digit > 9) digit -= 9;
    }
    sum += digit;
    shouldDouble = !shouldDouble;
  }
  return sum % 10 === 0;
};

// 有效期校验 MM/YY
const validateExpiry = (value) => {
  if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(value)) return false;
  const [month, year] = value.split("/").map(Number);
  const now = new Date();
  const currentYear = now.getFullYear() % 100;
  const currentMonth = now.getMonth() + 1;
  return year > currentYear || (year === currentYear && month >= currentMonth);
};

export const formatCardNumber = (value) => {
  return value
    .replace(/\D/g, "") // 去掉非数字
    .replace(/(\d{4})(?=\d)/g, "$1 "); // 每4位加空格
};

  // 有效期格式化：自动补 /
const formatExpiry = (value) => {
  console.error(value)
  let v = value.replace(/\D/g, ""); // 只保留数字
  if (v.length >= 3) {
    v = v.substring(0, 4);
    return v.replace(/(\d{2})(\d{1,2})/, "$1/$2");
  }
  return v;
};

export default function CertCardForm() {
  // 卡号格式化：每4位加空格
  
  return (
    <>
      <div className="flex flex-row mt-4">
        <div className="flex-1">
          <Form.Item label="持卡人" name="cardHolder" className='flex-1 lg:mr-5' rules={[{required: true, message: "请输入持卡人姓名"}]}>
            <Input variant='underlined' placeholder="请按照信用卡的姓名填写英文/拼音" />
          </Form.Item>
        </div>
      </div>
      <div className="flex flex-col lg:flex-row mt-2">
        <Form.Item label="卡号" name="cardNumber" className='flex-1 lg:mr-5'
          getValueFromEvent={(e) => formatCardNumber(e.target.value)}
          rules={[
            { required: true, message: "请输入信用卡号" },
            {
              validator: (_, value) => {
                const pureValue = value ? value.replace(/\s+/g, "") : "";
                if (!pureValue) return Promise.resolve();
                if (!/^\d{13,19}$/.test(pureValue)) {
                  return Promise.reject(new Error("卡号长度必须为 13~19 位"));
                }
                return validateCardNumber(pureValue)
                  ? Promise.resolve()
                  : Promise.reject(new Error("信用卡号无效"));
              }
                
            },
          ]}
        >
          <Input variant='underlined' placeholder="填写卡号" 
            maxLength={19} // 19 位数字 + 3 个空格
          />
        </Form.Item>
        <Form.Item label="到期日" name="cardExpire" className='flex-1'
          getValueFromEvent={(e) => formatExpiry(e.target.value)}
          rules={[
            { required: true, message: "请输入有效期" },
            {
              validator: (_, value) =>
                !value || validateExpiry(value)
                  ? Promise.resolve()
                  : Promise.reject(new Error("有效期无效或已过期")),
            },
          ]}
        >
          <Input variant='underlined' placeholder="月MM / 年YY (例如: 12/25)" 
            maxLength={5}
          />
        </Form.Item>
      </div>
      
    </>
  )
}