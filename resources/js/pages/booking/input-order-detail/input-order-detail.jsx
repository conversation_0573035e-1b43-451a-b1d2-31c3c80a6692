import { createRoot } from "react-dom/client";
import { Form, Input, Button } from "antd"
import OrderStep from "@/pages/booking/select-room/parts/OrderStep";
import BookingLoginWidget from "@/pages/booking/widgets/BookingLoginWidget"
import OrderDetailWidget from "@/pages/booking/widgets/OrderDetailWidget";
import OrderPriceDetailWidget from "@/pages/booking/widgets/OrderPriceDetailWidget";
import GhaConfigProvider from "@/_components/GhaConfigProvider"
import CertCard01 from "@images/booking/cert-card-01.png"
import CertCard02 from "@images/booking/cert-card-02.png"
import CertCard03 from "@images/booking/cert-card-03.png"
import CertCard04 from "@images/booking/cert-card-04.png"
import CertCard05 from "@images/booking/cert-card-05.png"
import CertCard06 from "@images/booking/cert-card-06.png"
import { useEffect, useMemo, useState } from "react";
import { $helper, $http, $storage, $constants } from "@/_utils/_index"
import CertCardForm, {formatCardNumber} from "./parts/CertCardForm";



function App() {
  const [bookingData] = useState($storage.getLocalBookingData());
  const [form] = Form.useForm();
  const urlParams = useMemo(() => {
    return $helper.parseBookingQuery(location.search.replace("?", ""))
  }, [])
  function onFinish(values) {
    $storage.setLocalBookingData(JSON.stringify({...bookingData, userInfo: {...values, cardNumber: values.cardNumber.replace(/\s+/g, "")}}))
    location.href = `/booking/confirm-order${urlParams.itineraryNumber ? `?itineraryNumber=${urlParams.itineraryNumber}` : ''}`
  }
  useEffect(() => {
    console.error("localBookingData", bookingData)
  }, [])
  function onRegisterContinue() {
    location.href = `/auth/register?redirect=${encodeURIComponent(location.href)}`
  }
  function onLoginContinue() {
    if (!$helper.isLogin()) {
      window.modalLoginCallback = () => {
        
      }
    }
    // location.href = `/auth/login?redirect=${encodeURIComponent(location.href)}`
  }

  const totalPotentialDiscoveryAccrual = useMemo(() => {
    return bookingData?.curRates?.reduce((acc, val) => {
      return acc + (val.rate.potentialDiscoveryAccrual || 0)
    }, 0)
  }, [bookingData])
  function onBackToEdit() {
    location.href = `/booking/select-room?${bookingData.cond}&edit=1${urlParams.itineraryNumber ? `&itineraryNumber=${urlParams.itineraryNumber}` : ''}`
  }

  const initUserInfo = useMemo(() => {
    const isEdit = urlParams.edit === "1"
    const loginUser = $helper.getServerConfig("user")
    return {
      firstName: isEdit ? bookingData.userInfo?.firstName : $helper.isLogin() ? loginUser.first_name : $helper.isLaravelLocal() ? "张" : "",
      lastName: isEdit ? bookingData.userInfo?.lastName : $helper.isLogin() ? loginUser.last_name : $helper.isLaravelLocal() ? "三" : "",
      email: isEdit ? bookingData.userInfo?.email : $helper.isLogin() ? loginUser.email : $helper.isLaravelLocal() ? "<EMAIL>" : "",
      // phone: isEdit ? bookingData.userInfo?.phone : $helper.isLogin() ? (loginUser.phone || "") : $helper.isLaravelLocal() ? "13800138000" : "",
      phone: isEdit ? bookingData.userInfo?.phone : $helper.isLaravelLocal() ? "13800138000" : "",
      cardNumber: formatCardNumber(isEdit ? bookingData.userInfo?.cardNumber : $helper.isLaravelLocal() ? "****************" : ""),
      cardExpire: isEdit ? bookingData.userInfo?.cardExpire : $helper.isLaravelLocal() ? "12/29" : "",
      cardHolder: isEdit ? bookingData.userInfo?.cardHolder : $helper.isLaravelLocal() ? "ZHANG SAN" : ""
    }
  }, [])
  const [showLoginModule, setShowLoginModule] = useState(() => {
    console.error("initUserInfo", initUserInfo)
    return !$helper.isLogin()
  })
  
  return (
    <>
      {urlParams.itineraryNumber ? (
        <>
          <div className="booking-edit-tip fixed-top">
            <p>请再次填写您的个人信息</p>
            <p>订单号：{urlParams.itineraryNumber}</p>
          </div>
          <div className="h-12 mb-6"></div>
        </>
      ) : (
        <div className="py-12">
          <OrderStep step={2}/>
        </div>
      )}
      <div className="">
        <div className="g-main-content-sm">
          <div className="text-[#999] font-14">
            <a className="flex flex-row items-center trigger-back"><i className="iconfont icon-left font-20"></i>返回</a>
          </div>
          <div className={`${!$helper.isLogin() ? '' : 'hidden'}`}>
            <a onClick={onLoginContinue} className="underline text-primary font-14">已经是GHA探索之旅会员？</a>
          </div>
        </div>
      </div>
      <div className="mt-3">
        <div className="g-main-content-sm">
          <div className="flex flex-col-reverse lg:flex-row">
            <div className="flex-1">
              {showLoginModule ? (
                <div className="hidden lg:block">
                  <BookingLoginWidget onRegister={onRegisterContinue} onClose={() => setShowLoginModule(false)}/>
                </div>
              ) : null}
              
              <div className={`book-detail-wrap ${showLoginModule ? "" : "!mt-0"}`}>
                <div className="">
                  <Form
                    layout="vertical"
                    form={form}
                    onFinish={onFinish}
                    className="gha-form"
                    initialValues={initUserInfo}
                  >
                    <h2 className="font-20 font-bold">您的入住信息</h2>
                    <div className="mt-4">
                      <h3 className="font-16 font-medium">入住联系人信息：</h3>
                      <p className="font-14 text-[#999] mt-1">请按实际入住人数填写，姓名与证件保持一致</p>
                    </div>
                    <div className="flex flex-col lg:flex-row mt-2">
                      <div className="flex-1">
                        <Form.Item label="姓" name="firstName" className='flex-1 lg:mr-5' rules={[{required: true, message: "请输入您的姓"}]}>
                          <Input variant='underlined' placeholder="请输入您的姓" />
                        </Form.Item>
                      </div>
                      <div className="flex-1">
                        <Form.Item label="名" name="lastName" className='flex-1' rules={[{required: true, message: "请输入您的名"}]}>
                          <Input variant='underlined' placeholder="请输入您的名" />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="flex flex-col lg:flex-row">
                      <div className="flex-1">
                        <Form.Item label="邮箱" name="email" className='flex-1 lg:mr-5'
                          rules={[
                            {required: true, message: "请输入您的邮箱"},
                            { 
                              pattern: $constants.emailPattern,
                              message: "邮箱格式不正确"
                            }
                          ]}
                        >
                          <Input variant='underlined' placeholder="请输入您的邮箱" />
                        </Form.Item>
                      </div>
                      <div className="flex-1">
                        <Form.Item label="手机号" name="phone" className='flex-1'
                          rules={[
                            {required: true, message: "请输入您的手机号"},
                            { 
                              pattern: $constants.phonePattern,
                              message: "手机号格式不正确"
                            }
                          ]}
                        >
                          <Input variant='underlined' placeholder="请输入您的手机号" />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="mt-4 mb-8 w-full h-px bg-[#c9c9c9]/50"></div>
                    <h3 className="font-16 font-medium">信用卡担保信息：</h3>
                    <p className="font-14 text-[#999] mt-1">仅需要信用卡详细信息来确认预订。 我们使用安全传输。</p>
                    <p className="font-14 mt-4">{bookingData?.hotel?.hotel_name}   支持以下信用卡</p>
                    <div className="flex flex-row flex-wrap -mx-1 mt-2">
                      {new Array(6).fill(0).map((_, index) => {
                        let cartSet = {
                          1: CertCard01,
                          2: CertCard02,
                          3: CertCard03,
                          4: CertCard04,
                          5: CertCard05,
                          6: CertCard06,
                        }
                        return (<img className="mx-1 w-10" key={index} src={cartSet[index + 1]} alt="" />)
                      })}
                    </div>
                    <CertCardForm/>
                    {/* <div className="mt-4 mb-8 w-full h-px bg-[#c9c9c9]/50"></div>
                    <div className="font-13 text-primary">
                      <h3 className="font-16 font-medium text-black">取消及押金政策说明：</h3>
                      <p className="mt-2">取消房价不可取消，押金预订时需缴纳 1049.40 元押金，以保证您的预订。</p>
                      <p>*押金金额基于您选择的金额币种：人民币税费含税 税费 服务费</p>
                    </div> */}
                    <div className="my-8 w-full h-0.5 bg-[#c9c9c9]/30"></div>
                    <div className="flex flex-col lg:flex-row items-center justify-between pb-12">
                      <p className="text-primary font-14">
                        通过此次预订，您最多可赚取
                        <span className="text-[#e69d4a]">D${totalPotentialDiscoveryAccrual}</span>
                      </p>
                      <Button type="primary" className="gha-primary-btn mt-4 lg:mt-0 w-full lg:w-auto !px-10" htmlType="submit">下一步，确认订单</Button>
                    </div>
                  </Form>
                </div>
              </div>
            </div>
            <div className="lg:w-[320px] lg:ml-8">
              {showLoginModule ? (
                <div className="lg:hidden mb-4 lg:mb-0">
                  <BookingLoginWidget onRegister={onRegisterContinue} onClose={() => setShowLoginModule(false)}/>
                </div>
              ) : null}
              <OrderDetailWidget bookingData={bookingData} onBackToEdit={onBackToEdit} className=""/>
              <OrderPriceDetailWidget bookingData={bookingData}/>
            </div>
            
          </div>
        </div>
      </div>
    </>
  )
}

createRoot(document.querySelector(".input-order-detail-content")).render(<GhaConfigProvider><App /></GhaConfigProvider>);