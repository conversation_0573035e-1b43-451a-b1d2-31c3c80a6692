import { createRoot } from "react-dom/client";
import { Checkbox } from "antd"
import OrderStep from "@/pages/booking/select-room/parts/OrderStep";
import OrderDetailWidget from "@/pages/booking/widgets/OrderDetailWidget";
import OrderPriceDetailWidget from "@/pages/booking/widgets/OrderPriceDetailWidget";
import GhaConfigProvider from "@/_components/GhaConfigProvider"

function App() {
  return (
    <>
      <div className="pt-12">
        <div className="g-main-content-sm">
          <div className="text-[#999] font-14">
            <a href="javascript:;" className="flex flex-row items-center"><i className="iconfont icon-left font-20"></i>返回</a>
          </div>
        </div>
      </div>
      <div className="mt-3">
        <div className="g-main-content-sm">
          <div className="flex flex-row justify-stretch">
            <div className="border border-[#ebebeb] rounded-lg shadow-lg shadow-black/10 p-6 flex-1 flex flex-col">
              {/* <h1 className="font-20 font-bold">您的入住信息</h1> */}
              <div className="flex flex-col items-center font-14 font-medium pt-14 flex-1">
                <i className="iconfont icon-a-14Benefits_brand_benefits text-5xl"></i>
                <h2 className="font-18 font-medium">订单预约成功</h2>
                <p className="mt-3">订单确认函已发送至邮箱</p>
                <p><EMAIL></p>
                <p className="mt-3">订单号：GHA25556576454</p>
                <p className="text-[#999] font-normal">请妥善保留单号以便日后查询订单</p>
                <p className="text-primary mt-24">通过此次预订，您可赚取<span className="text-[#e69d4a]">D$216.22</span></p>
                <a href="/booking/order-detail" className="gha-primary-btn mt-4 !px-14 !py-1.5">查看订单</a>
                <a href="/" className="underline text-primary mt-4">返回首页</a>
              </div>
              <div className="py-8 border-t border-[#c9c9c9]/50 text-center">
                <h4 className="font-semibold font-16">取消及押金政策说明</h4>
                <p className="font-13 mt-2">取消房价不可取消，押金预订时需缴纳 1049.40 元押金，以保证您的预订。<br/>*押金金额基于您选择的金额币种：人民币税费含税 税费 服务费</p>
              </div>
            </div>
            <div className="flex flex-col lg:w-[320px] lg:ml-8">
              <OrderDetailWidget/>
              <OrderPriceDetailWidget/>
            </div>
          </div>
          
        </div>
      </div>
    </>
  )
}

createRoot(document.querySelector(".booking-success-content")).render(<GhaConfigProvider><App /></GhaConfigProvider>);