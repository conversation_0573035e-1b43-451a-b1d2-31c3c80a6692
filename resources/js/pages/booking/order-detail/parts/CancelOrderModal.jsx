import { useState, forwardRef, useImperativeHandle } from "react"
import { Mo<PERSON>, But<PERSON> } from "antd"

const CancelOrderModal = forwardRef(({}, ref) => {
  const [modalOpend, setModalOpend] = useState(false);
   useImperativeHandle(ref, () => ({
    open() {
      setModalOpend(true)
    }
  }));
  const content1 = (
    <>
      <h3 className="font-20 font-bold mb-3">取消订单</h3>
      <p>不迟于抵达前24小时取消，否则将收取一晚的取消费用。</p>
      <p className="mt-1">您确定要取消订单吗？</p>
      <div className="flex flex-row items-center justify-center -mx-2 mt-6">
        <Button onClick={() => setModalOpend(false)} className="gha-btn medium w-40 mx-2">再考虑一下</Button>
        <Button type="primary" className="gha-primary-btn medium w-40 mx-2">确定</Button>
      </div>
    </>
  )
  const content2 = (
    <>
      <h3 className="font-20 font-bold mb-2">您的预订已成功取消</h3>
      <p className="font-12">您将很快收到一封电子邮件确认</p>
      <div className="flex flex-row items-center justify-center -mx-2 mt-6">
        <Button onClick={() => setModalOpend(false)} type="primary" className="gha-primary-btn medium w-40 mx-2">确定</Button>
      </div>
    </>
  )
  const content3 = (
    <>
      <h3 className="font-20 font-bold mb-2">预订取消失败</h3>
      <p className="font-12 text-red-500">取消此订单需要授权</p>
      <div className="flex flex-row items-center justify-center -mx-2 mt-6">
        <Button onClick={() => setModalOpend(false)} type="primary" className="gha-primary-btn medium w-40 mx-2">确定</Button>
      </div>
    </>
  )
  return (
    <Modal 
      open={modalOpend}
      footer={null}
      width={500}
      closable={false}
      destroyOnHidden
      transitionName="ant-fade"
      rootClassName='gha-antd-modal'
      centered
    >
      <div className="gha-antd-modal-wrapper">
        <div className="close-icon" onClick={() => setModalOpend(false)}>
          <i className="iconfont icon-Close"></i>
        </div>
        <div className="gha-antd-modal-content">
          <div className="px-8 text-center font-14">
            {content1}
          </div>
        </div>
      </div>

    </Modal>
  )
})

export default CancelOrderModal