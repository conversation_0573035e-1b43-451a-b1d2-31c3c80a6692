import { createRoot } from "react-dom/client";
import { Modal } from "antd"
import OrderDetailWidget from "@/pages/booking/widgets/OrderDetailWidget";
import OrderPriceDetailWidget from "@/pages/booking/widgets/OrderPriceDetailWidget";
import GhaConfigProvider from "@/_components/GhaConfigProvider"
import CancelOrderModal from "./parts/CancelOrderModal";
import { useRef } from "react";

function App() {
  const cancelModalRef = useRef()
  return (
    <>
      <div className="pt-12">
        <div className="g-main-content-sm">
          <div className="text-[#999] font-14">
            <a href="javascript:;" className="flex flex-row items-center"><i className="iconfont icon-left font-20"></i>返回</a>
          </div>
        </div>
      </div>
      <div className="mt-3">
        <div className="g-main-content-sm">
          <div className="flex flex-col">
            <OrderDetailWidget title={"订单信息"} hasUserInfo hasOrderTitle/>
            <OrderPriceDetailWidget/>
          </div>
        </div>
      </div>
      <div className="mt-8">
        <div className="g-main-content-sm">
          <div className="flex flex-col md:flex-row items-center justify-center md:-mx-2">
            <div className="mx-2 w-full md:w-auto">
              <div className="flex flex-row items-center justify-center -mx-2">
                <a onClick={() => cancelModalRef.current.open()} href="javascript:;" className="gha-primary-btn flex-1 md:flex-none !bg-primary/40 !border-transparent !px-8 mx-2">取消订单</a>
                <a href="javascript:;" className="gha-primary-btn flex-1 md:flex-none !bg-primary/60 !border-transparent !px-8 mx-2">修改订单</a>
              </div>
            </div>
            <div className="mx-2 mt-3 md:mt-0 w-full md:w-auto">
              <div className="flex flex-row items-center justify-center -mx-2">
                <a href="javascript:;" className="gha-primary-btn flex-1 md:flex-none !px-8 mx-2">继续订单</a>
                <a href="javascript:;" className="gha-btn flex-1 md:flex-none !px-8 mx-2">打印订单</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CancelOrderModal ref={cancelModalRef}/>
    </>
  )
}

createRoot(document.querySelector(".order-detail-content")).render(<GhaConfigProvider><App /></GhaConfigProvider>);