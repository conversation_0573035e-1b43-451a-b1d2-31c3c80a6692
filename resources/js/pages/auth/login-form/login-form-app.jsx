import { useMemo, useState } from "react";
import { Button, Form, Input } from 'antd';
import GhaConfigProvider, { useAntdMessage } from "@/_components/GhaConfigProvider";
import { $http, $helper, $constants, $storage } from "@/_utils/_index"

function LoginFormApp({ isModal, onLogin }) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false)
  const message = useAntdMessage()
  const navUri = useMemo(() => {
    return {
      activeUri: `${window.__Server_Data__?.activeUri}`,
      forgetPwdUri: `${window.__Server_Data__?.forgetPwdUri}`,
      registerUri: `${window.__Server_Data__?.registerUri}?redirect=${encodeURIComponent(location.href)}`,
    }
  }, [])
  function onFinish(values) {
    setLoading(true)
    $http.authLogin(values).subscribe({
      next: (res) => {
          setLoading(false);
          if (res.status_code !== 200) {
              message.error(res.message);
              return;
          }
          if (res.data.token) {
              $storage.setToken(res.data.token);
          }
          if (isModal) {
            console.error("login res", res.data)
            window.__Server_Data__['user'] = res.data.user
            $(".login-wrap").remove()
            $(".login-wrap-m").remove()
            $(".lang-wrap.tippy-drop-menu").before(`
              <a href="/user/dashboard">
                <div class="bg-[#B7C8D5] mx-5 lg:mx-0 w-9 h-9 rounded-full flex items-center justify-center">
                  <i class="iconfont icon-Union text-white text-base"></i>
                </div>
              </a>  
            `)
            onLogin && onLogin()
            return
          }
          let redirect = new URLSearchParams(location.search.replace("?", "")).get("redirect") || "/";
          if (redirect) {
              location.href = redirect;
          }
          
      },
      error: (err) => {
          console.error('登录请求错误:', err);
          setLoading(false);
          message.error('登录失败，请检查网络连接');
      },
      complete: () => {
          console.log('请求完成');
      }
    });
  }
  return (
    <div className="auth-box login-box">
      <div className="form-wrap">
        <h3>请登录</h3>
        <Form
          layout="vertical"
          form={form}
          requiredMark={false}
          initialValues={$helper.isLaravelLocal() ? {
            email: "<EMAIL>",
            password: "123456Abc$",
          } : {}}
          onFinish={onFinish}
          className="gha-form"
        >
          <Form.Item label="邮箱*" name="email" 
            rules={[
              {required: true, message: "请输入您的邮箱"},
              { 
                pattern: $constants.emailPattern,
                message: "邮箱格式不正确"
              } 
            ]}
          >
            <Input variant='underlined' placeholder="请输入您的邮箱" />
          </Form.Item>
          <Form.Item label="密码*" name="password" rules={[{required: true, message: "请输入密码"}]}>
            <Input.Password variant='underlined' placeholder="请输入密码" />
          </Form.Item>
          <p className='text-right -mt-3.5 mb-5'><a href={navUri.forgetPwdUri} className='underline'>忘记密码?</a></p>
          <Form.Item label={null}>
            <Button className="gha-primary-btn" loading={loading} type="primary" shape="round" block htmlType="submit">登录</Button>
          </Form.Item>
          <p className='text-center'><a href={navUri.activeUri} className='underline'>激活您的在线帐户</a></p>
        </Form>
      </div>
      <div className="extra-wrap">
        <h3>免费加入GHA全球会员计划</h3>
        <p>
          GHA代表着40个品牌,<br/>
          800多家酒店,遍布100个国家,<br/>
          服务会员25万<br/>
        </p>
        <a className="gha-btn" href={navUri.registerUri}>立即加入GHA酒店忠诚计划</a>
      </div>
    </div>
  )
}

export default LoginFormApp