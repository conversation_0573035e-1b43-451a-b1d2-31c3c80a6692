import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider } from 'antd';
import GhaConfigProvider, { useAntdMessage, useForceUpdate } from "@/_components/GhaConfigProvider";
import { $http, $constants, $helper } from "@/_utils/_index"

function ResetPasswordForm() {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false)
  const message = useAntdMessage();
  const [form] = Form.useForm();
  function onFinish(values) {
    setLoading(true)
    const payload = {
      token: window.__ServerVars__.token,
      email: window.__ServerVars__.email,
    }
    $http.authResetPassword({...values, ...payload}).subscribe(res => {
      setLoading(false)
      if (res.status_code !== 200) {
        message.error(res.msg);
        return;
      }
      setStep(2)
    })
  }
  return (
    <div className={`auth-box auth-box-400`}>
      <div className="form-wrap">
        {step === 1 ? (
          <>
            <h3 className="text-center">输入新密码进行重置</h3>
            <div className="mt-5">
              <Form
                layout="vertical"
                form={form}
                requiredMark={false}
                onFinish={onFinish}
                initialValues={$helper.isLaravelLocal() ? {
                  password: '123456Abc$',
                  confirm_password: '123456Abc$'
                } : {}}
              >
                <Form.Item label="密码*" name="password"
                  rules={[
                    {required: true, message: "请输入您的密码"},
                    { 
                      pattern: $constants.passwordPattern,
                      message: "密码至少8位，包含字母、数字和符号，且首尾不能有空格"
                    }
                  ]}
                >
                  <Input.Password variant='underlined' placeholder="请输入密码" />
                </Form.Item>
                <Form.Item label="确认密码*" name="confirm_password"
                  dependencies={['password']} 
                  rules={[
                    {required: true, message: "请输入确认密码"},
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('密码与确认密码不一致!'));
                      },
                    }),
                  ]}
                >
                  <Input.Password variant='underlined' placeholder="请输入确认密码" />
                </Form.Item>
                <Form.Item label={null}>
                  <Button loading={loading} className="gha-primary-btn" type="primary" shape="round" block htmlType="submit">提交</Button>
                </Form.Item>
              </Form>
            </div>
          </>
        ) : (
          <div className={`flex flex-col items-center justify-center`}>
            <i className='iconfont icon-a-14Benefits_brand_benefits text-primary text-4xl'></i>
            <h2 className='font-bold text-lg mt-1'>重置密码成功</h2>
            <p className='mt-2.5 text-xs mb-8'>请使用新密码重新进行登录</p>
            <a href={window.__ServerVars__.loginUri} className='gha-primary-btn w-full mt-5'>登录</a>
          </div>
        )}
        
        
      </div>
    </div>
  )
}

createRoot(document.querySelector(".auth-box-wrap")).render(<GhaConfigProvider><ResetPasswordForm /></GhaConfigProvider>);