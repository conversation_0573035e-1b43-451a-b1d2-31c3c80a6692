import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider } from 'antd';
import GhaConfigProvider, { useAntdMessage, useForceUpdate } from "@/_components/GhaConfigProvider";
import { $http, $constants } from "@/_utils/_index"

function ResetPasswordForm() {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false)
  const message = useAntdMessage()
  const [form] = Form.useForm();
  const [resData, setResData] = useState()
  function onFinish(values) {
    setLoading(true)
    $http.authSendResetPasswordEmail(values).subscribe(res => {
      setLoading(false)
      if (res.status_code !== 200) {
        message.error(res.message)
        return
      }
      setResData(res.data)
      setStep(2)
    })
  }
  return (
    <div className={`auth-box auth-box-500`}>
      <div className="form-wrap">
        <h3 className="text-center">重置密码</h3>
        {step === 1 ? (
          <>
            <h4 className="mt-5 text-sm text-center">
              请输入<span className="text-primary">邮箱地址</span><br/>
              我们将会以邮件形式发送密码重置的链接
            </h4>
            <div className="mt-5">
              <Form
                layout="vertical"
                form={form}
                requiredMark={false}
                onFinish={onFinish}
              >
                <Form.Item label={<>邮箱*</>} name="email"
                  rules={[
                    {required: true, message: "请输入您的邮箱"},
                    { 
                      pattern: $constants.emailPattern,
                      message: "邮箱格式不正确"
                    }
                  ]}
                >
                  <Input variant='underlined' placeholder="请输入您的邮箱" />
                </Form.Item>
                <Form.Item label={null}>
                  <Button loading={loading} className="gha-primary-btn" type="primary" shape="round" block htmlType="submit">下一步</Button>
                </Form.Item>
              </Form>
            </div>
          </>
        ) : (
          <>
            <h4 className="mt-5 text-sm text-center">
              重置密码的电子邮件发送至<span className="text-primary">{resData?.email}</span><br/>
              请按照发送的说明进行操作
            </h4>
            <p className="mt-5 text-center text-xs text-[#919191]">如果电子邮件没有在几分钟内到达，请检查您的垃圾邮件文件夹</p>
            <div className="mt-5 text-center">
              <a href="/" className="gha-primary-btn w-full md:w-[260px]">关闭</a>
            </div>
          </>
        )}
        
        
      </div>
    </div>
  )
}

createRoot(document.querySelector(".auth-box-wrap")).render(<GhaConfigProvider><ResetPasswordForm /></GhaConfigProvider>);