import { Button, Form, Input } from 'antd';
import {$helper, $http, $constants} from "@/_utils/_index"
import { useState } from 'react';
import { useAntdMessage } from "@/_components/GhaConfigProvider";

export default function FormStepOne({onNext}) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false)
  const message = useAntdMessage()
  function onFinish(values) {
    setLoading(true)
    $http.authActiveAccount(values).subscribe(res => {
      setLoading(false)
      if (res.status_code !== 200) {
        message.error(res.message)
        return
      }
      onNext && onNext()
    })
    
  }
  return (
    <Form
      layout="vertical"
      form={form}
      requiredMark={false}
      onFinish={onFinish}
      initialValues={$helper.isLaravelLocal() ? {
        membershipCardNo: '123456',
        email: '<EMAIL>',
        password: '123456Abc$',
        confirm_password: '123456Abc$',
      } : {}}
    >
      <Form.Item label={<>会员号*</>} name="membershipCardNo" rules={[{required: true, message: "请输入您的会员号"}]}>
        <Input variant='underlined' placeholder="请输入您的会员号" />
      </Form.Item>
      <p className='flex flex-row justify-between -mt-3.5 mb-5'>
        <span className="text-[#919191]">会员号可在我们发的电子邮件顶部找到。</span>
        <a href={window.__ServerVars__.forgetAccountUri} className='underline flex-shrink-0'>忘记会员号？</a>
      </p>
      <Form.Item label="邮箱*" name="email" 
        rules={[
          {required: true, message: "请输入您的邮箱"},
          { 
            pattern: $constants.emailPattern,
            message: "邮箱格式不正确"
          }  
        ]}
      >
        <Input variant='underlined' placeholder="请输入您的邮箱" />
      </Form.Item>
      <Form.Item label="密码*" name="password"
        rules={[
          {required: true, message: "请输入您的密码"},
          { 
            pattern: $constants.passwordPattern,
            message: "密码至少8位，包含字母、数字和符号，且首尾不能有空格"
          }
        ]}
      >
        <Input.Password variant='underlined' placeholder="请输入您的密码" />
      </Form.Item>
      <Form.Item label="确认密码*" name="confirm_password"
        dependencies={['password']} 
        rules={[
          {required: true, message: "请输入确认密码"},
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('密码与确认密码不一致!'));
            },
          }),
        ]}
      >
        <Input.Password variant='underlined' placeholder="请输入确认密码" />
      </Form.Item>
      <Form.Item label={null}>
        <Button className="gha-primary-btn" loading={loading} type="primary" shape="round" block htmlType="submit">提交</Button>
      </Form.Item>
    </Form>
  )
}