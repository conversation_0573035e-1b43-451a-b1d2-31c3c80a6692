import { Button, Form, Input } from 'antd';
import { $constants } from "@/_utils/_index"
import { useState } from 'react';

export default function FormStepTwo({onNext}) {
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm();
  function onFinish(values) {
    onNext(values);
  }
  return (
    <Form
      layout="vertical"
      form={form}
      onFinish={onFinish}
    >
      <Form.Item label="密码" name="password"
        rules={[
          {required: true, message: "请输入您的密码"},
          { 
            pattern: $constants.passwordPattern,
            message: "密码至少8位，包含字母、数字和符号，且首尾不能有空格"
          }
        ]}
      >
        <Input.Password variant='underlined' placeholder="请输入您的密码" />
      </Form.Item>
      <Form.Item label="确认密码" name="confirm_password"
        dependencies={['password']} 
        rules={[
          {required: true, message: "请输入确认密码"},
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('密码与确认密码不一致!'));
            },
          }),
        ]}
      >
        <Input.Password variant='underlined' placeholder="请输入确认密码" />
      </Form.Item>
      <Form.Item label={null}>
        <Button className="gha-primary-btn" type="primary" shape="round" block htmlType="submit">提交</Button>
      </Form.Item>
    </Form>
  )
}