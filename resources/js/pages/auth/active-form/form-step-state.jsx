import { Button, Form, Input, ConfigProvider } from 'antd';
import { useEffect, useRef } from 'react';
import { useForceUpdate } from "@/_components/GhaConfigProvider";

export default function FormStepState({state = "success"}) {
  const interval = useRef(null)
  const intervalCount = useRef(5)
  const forceUpdate = useForceUpdate()
  useEffect(() => {
    if (state === "success") {
      startInterval()
    }
    return () => {
      interval.current && clearInterval(interval.current)
    }
  }, [])
  function startInterval() {
    interval.current = setInterval(() => {
      intervalCount.current -= 1
      forceUpdate()
      if (intervalCount.current === 1) {
        clearInterval(interval.current)
        window.location.href = document.referrer || "/"
      }
    }, 1000)
  }
  return (
    <div className={`flex flex-col items-center justify-center`}>
      {state === "success" ? (
        <>
          <i className='iconfont icon-a-14Benefits_brand_benefits text-primary text-4xl'></i>
          <h2 className='font-bold text-lg'>激活成功</h2>
          <h5 className='text-xs mt-2'>感谢您选择 GHA 会员计划</h5>
          <p className='text-xs text-[#737373] mt-16'><span className='text-primary font-bold mr-1'>{intervalCount.current}</span>秒后进入会员首页</p>
        </>
      ) : (
        <>
          <i className='iconfont icon-Information text-primary text-4xl'></i>
          <h2 className='font-bold text-lg'>已有账户</h2>
          <p className='mt-5 text-xs text-[#737373]'>已有一个在线账户关联此信息，请返回并使用您的电子邮箱登录。</p>
          <a href="" className='gha-primary-btn w-full mt-5'>返回首页</a>
        </>
      )}
      
    </div>
  )
}