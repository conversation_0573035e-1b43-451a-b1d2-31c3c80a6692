import { createRoot } from "react-dom/client";
import { useState } from "react";
import FormStepOne from "./form-step-one";
import FormStepTwo from "./form-step-two";
import FormStepState from "./form-step-state";
import GhaConfigProvider, { useAntdMessage, useForceUpdate } from "@/_components/GhaConfigProvider";

function ActiveForm() {
  const [step, setStep] = useState(1);
  const [activeState, setActiveState] = useState("success")
  const [data, setData] = useState({email: "", membershipCardNo: "", password: "", confirm_password: ""})
  return (
    <div className={`auth-box ${step === 1 ? "auth-box-500" : ""}`}>
      <div className="form-wrap">
        {[1, 2].includes(step) && <h3 className="text-center">激活您的在线账户</h3>}
        {step === 1 && <FormStepOne onNext={(payload) => {
          setData({...data, ...payload})
          setStep(2)
        }}/>}
        {step === 2 && <FormStepTwo onNext={(payload, state) => {
          setData({...data, ...payload})
          setActiveState(state)
          setStep(3)
        }}/>}
        {step === 3 && <FormStepState state={activeState}/>}
      </div>
    </div>
  )
}

createRoot(document.querySelector(".auth-box-wrap")).render(<GhaConfigProvider><ActiveForm /></GhaConfigProvider>);