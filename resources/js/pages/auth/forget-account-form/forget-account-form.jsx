import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider } from 'antd';
import GhaConfigProvider, { useAntdMessage, useForceUpdate } from "@/_components/GhaConfigProvider";
import { $http, $constants, $helper } from "@/_utils/_index"

function ForgetAccountForm() {
  const [step, setStep] = useState(1);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false)
  const message = useAntdMessage()
  function onFinish(values) {
    setLoading(true)
    $http.authFindAccount(values).subscribe(res => {
      setLoading(false)
      if (res.status_code !== 200) {
        message.error(res.message)
        return
      }
      setStep(2)
    })
  }
  return (
    <div className={`auth-box auth-box-400`}>
      <div className="form-wrap">
        {step === 1 ? (
          <>
            <h3 className="text-center">找回您的在线账户</h3>
            <h4 className="text-center font-12 -mt-2.5">请输入用户姓氏和电子邮件地址<br/>我们将会发送含有您的GHA DISCOVERY会籍号的邮件到您的邮箱</h4>
            <div className="mt-5">
              <Form
                layout="vertical"
                form={form}
                requiredMark={false}
                onFinish={onFinish}
                initialValues={$helper.isLaravelLocal() ? { 
                  last_name: "wchy",
                  email: "<EMAIL>"
                } : {}}
              >
                <Form.Item label="姓氏*" name="last_name" rules={[{required: true, message: "请输入您的姓"}]}>
                  <Input variant='underlined' placeholder="请输入您的姓氏" />
                </Form.Item>
                <Form.Item label="邮箱*" name="email"
                  rules={[
                    {required: true, message: "请输入您的邮箱"},
                    {
                      pattern: $constants.emailPattern,
                      message: "邮箱格式不正确"
                    }
                  ]}
                >
                  <Input variant='underlined' placeholder="请输入您的邮箱" />
                </Form.Item>
                <Form.Item label={null}>
                  <Button loading={loading} className="gha-primary-btn" type="primary" shape="round" block htmlType="submit">下一步</Button>
                </Form.Item>
              </Form>
            </div>
          </>
        ) : (
          <div className={`flex flex-col items-center justify-center`}>
            <p className='font-12 mb-5 text-center'>我们已向您发送了一封<br/>
              含有GHA DISCOVERY会籍号的电子邮件<br/>
              请继续激活您的在线账户
            </p>
            <a href={window.__ServerVars__.activeUri} className='gha-primary-btn w-full mt-5'>激活在线账户</a>
          </div>
        )}
      </div>
    </div>
  )
}

createRoot(document.querySelector(".auth-box-wrap")).render(<GhaConfigProvider><ForgetAccountForm /></GhaConfigProvider>);
