import { createRoot } from "react-dom/client";
import { $helper } from  "@/_utils/_index";
import LogoutModal from "./parts/logout-modal";
import LoginModal from "./parts/login-modal";
import GhaConfigProvider, { useAntdMessage } from "@/_components/GhaConfigProvider"
import { useEffect } from "react";

const rootEle = $helper.createReactRootElement("__react__root__app__")

function App() {
  useEffect(() => {
    $helper.getGlobalSubject().on("showMessage", handleShowMessage, "APP")
    return () => {
      $helper.getGlobalSubject().off("showMessage", handleShowMessage, "APP")
    }
  }, [])
  const message = useAntdMessage()
  function handleShowMessage(event) {
    const {type, message: msg} = event.data
    message[type](msg)
  }
  return (
    <>
      <LogoutModal/>
      <LoginModal/>
    </>
  )
}

createRoot(rootEle).render(<GhaConfigProvider><App /></GhaConfigProvider>);