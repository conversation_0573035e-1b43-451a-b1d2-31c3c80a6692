import { Modal } from "antd"
import LoginFormApp from "@/pages/auth/login-form/login-form-app"
import { useEffect, useState } from "react"
import { $helper } from "@/_utils/_index"

export default function LoginModal() {
  const [open, setOpen] = useState(false)
  useEffect(() => {
    $helper.getGlobalSubject().on("Trigger_ProtectLogin", () => setOpen(true))
  }, [])
  return (
    <Modal 
      open={open} 
      onCancel={() => setOpen(false)}
      footer={null}
      rootClassName="gha-antd-modal login-modal"
      centered
      transitionName="ant-fade"
      width="auto"
      zIndex={10000}
    >
      <div className="auth-page-wrap">
        <div className="auth-box-wrap is-modal">
          <LoginFormApp isModal onLogin={() => {
            setOpen(false)
            typeof window.modalLoginCallback === "function" && window.modalLoginCallback()
          }}/>
        </div>
      </div>
      
    </Modal>
  )
}