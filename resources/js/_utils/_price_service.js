import $http from "./_request"
const DaEvent = window.DaEvent

class PriceService extends DaEvent {
  store = {}
  cond = {}
  subscriptionList = {}
  keys = {
    ON_HOTEL_PRICE_UPDATED: "ON_HOTEL_PRICE_UPDATED",
    ON_EVERY_HOTEL_PRICE_UPDATED: "ON_EVERY_HOTEL_PRICE_UPDATED",
  }
  setCond(cond) {
    this.cond = cond
  }
  clear() {
    this.store = {}
    Object.values(this.subscriptionList).forEach(sub => sub.unsubscribe())
    this.subscriptionList = {}
  }
  setPrice(hotelID, value) {
    this.store[hotelID] = value
  }
  getPrice(hotelID) {
    let val = this.store[hotelID]
    if (!val) {
      if (!this.subscriptionList[hotelID]) {
        const subscription = this.getPriceFromRemote(hotelID).subscribe(res => {
          let priceData = {
            status: false,
          }
          if (res.status_code === 200) {
            priceData = {...res.data, status: true}
          }
          this.setPrice(hotelID, priceData)
          this.emit(`${this.keys.ON_HOTEL_PRICE_UPDATED}_${hotelID}`, {hotelID, priceData})
          this.emit(this.keys.ON_EVERY_HOTEL_PRICE_UPDATED, {hotelID, priceData})
        })
        this.subscriptionList[hotelID] = subscription
      }
    }
    return val
  }
  getPriceFromRemote(hotelID) {
    const params = this.buildSearchParams({...this.cond, hotelId: hotelID})
    return $http.getHotelMinPrices(params)
  }

  buildSearchParams({rooms, date, promoCode, hotelId}) {
    return {
      hotelId: hotelId,
      startDate: date[0].format("YYYY-MM-DD"),
      endDate: date[1].format("YYYY-MM-DD"),
      numRooms: rooms.length,
      adults: rooms.reduce((acc, val) => {
        return acc + val.adult;
      }, 0),
      children: rooms.reduce((acc, val) => {
        return acc + val.child;
      }, 0),
      childrenAge: rooms.reduce((acc, val) => {
        return acc.concat(val.childAges);
      }, []).join(","),
      accessCode: promoCode || "",
      onlyPromo: '0'
    }
  }
}

const $priceService = new PriceService();

export default $priceService;