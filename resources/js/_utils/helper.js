const { Observable } = window.rxjs
import { defaultRooms } from "@/_components/gha-search-bar/useGhaSearchBarStore"
import dayjs from "dayjs"

function createReactRootElement(id, container) {
  const ele = document.createElement('div')
  ele.id = id || "__react__root__"
  const parentNode = container || document.body
  parentNode.appendChild(ele)
  return ele
}

function getGlobalSubject() {
  return __Global_Subject__
}

function showLoading() {
  $(".page-content").addClass("loading")
}
function hideLoading() {
  $(".page-content").removeClass("loading")
}

function showModal(type, config) {
  $helper.getGlobalSubject().emit("showModal", { type, config })
}
function showMessage(message, type = "error") {
  $helper.getGlobalSubject().emit("showMessage", { type, message })
}

function hash() {
  return Math.random().toString(36).substring(2, 9)
}

function getServerConfig(k) {
  return window.__Server_Data__[k]
}

function formatUsername(user) {
  return user.username
}

function clone(_) {
  return JSON.parse(JSON.stringify(_))
}

function loginProtect() {
  if (!getServerConfig("isLogin")) {
    window.loginRedirectUrl = location.href
    $(".login-btn-trigger:not(.logined)").trigger("click")
    return false
  }
  return true
}

function verifyCaptcha() {
  const appid = '193823154'
  return new Observable(obs => {
    try {
      var captcha = new TencentCaptcha(appid, (ret) => {
        obs.next(ret)
        obs.complete()
      }, {
        userLanguage: window.__currentLang__,
      });
      // 调用方法，显示验证码
      captcha.show();
    } catch (error) {
      console.error(error)
      var ticket = 'trerror_1001_' + appid + '_' + Math.floor(new Date().getTime() / 1000);
      obs.next({
        ret: -1,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error'
      })
      obs.complete()
    }
    
  })
  try {
    // 生成一个验证码对象
    // CaptchaAppId：登录验证码控制台，从【验证管理】页面进行查看。如果未创建过验证，请先新建验证。注意：不可使用客户端类型为小程序的CaptchaAppId，会导致数据统计错误。
    //callback：定义的回调函数
    
  } catch (error) {
    // 加载异常，调用验证码js加载错误处理函数
    loadErrorCallback();
  }
}

function isJsonString(str) {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

function isAntdGhaPopoverRootVisible() {
  const popoverEls = document.querySelectorAll(".gha-popover-root")
    let isPopoverOpen = false
    for (let i = 0; i < popoverEls.length; i++) {
      const el = popoverEls[i]
      const isVisible = !!(el && el.getBoundingClientRect().width > 0 && el.getBoundingClientRect().height > 0)
      if (isVisible) {
        isPopoverOpen = true
        break
      }
    }
    return isPopoverOpen
}

function encodeSearchHash(searchParams) {
  return Object.keys(searchParams).filter(key => {
    return Boolean(searchParams[key])
  }).map(key => {
    return `${key}=${encodeURIComponent(searchParams[key])}`
  }).join("&")
}

function decodeSearchHash(hash) {
  let searchParams = {}
  new URLSearchParams(hash).forEach((value, key) => {
    searchParams[key] = decodeURIComponent(value)
  })
  return searchParams
}

function getUrlHashParams() {
  return $helper.decodeSearchHash((location.hash || "").split("?")[1])
}

function isLaravelLocal() {
  return window.__Server_Data__.appEnv === "local1"
}

function _showMessage(message, type = "error") {
  $helper.getGlobalSubject().emit("showMessage", { type, message })
}

function getUrlDateAndRoomsFromHash() {
  const hash = location.hash.split("?")[1]
  const hashParams = $helper.decodeSearchHash(hash)
  let date = [dayjs().add(30, 'day'), dayjs().add(32, 'day')]
  try {
    date = hashParams.date.split(",").map(e => dayjs(e))
  } catch (e) {}
  let rooms = [...defaultRooms]
  try {
    rooms = JSON.parse(hashParams.rooms)
  } catch (e) {}
  return {date, rooms}
}
function hexToRgba(hex, alpha = 1) {
  // 去掉 #
  hex = hex.replace(/^#/, '');

  // 处理简写 (#F60 -> #FF6600)
  if (hex.length === 3) {
    hex = hex.split('').map(c => c + c).join('');
  }

  // 如果传的是 8 位 hex (#RRGGBBAA)，解析透明度
  if (hex.length === 8) {
    const bigint = parseInt(hex, 16);
    const r = (bigint >> 24) & 255;
    const g = (bigint >> 16) & 255;
    const b = (bigint >> 8) & 255;
    const a = (bigint & 255) / 255;
    return `rgba(${r}, ${g}, ${b}, ${a})`;
  }

  // 普通 6 位 hex
  const bigint = parseInt(hex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

function formatPrice(amount) {
  const formatter = new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    currencyDisplay: 'code', // 显示 CNY 而不是符号
    maximumFractionDigits: 2,
    minimumFractionDigits: 0
  });
  return formatter.format(amount);
}

function encodeRoomPeople(rooms) {
  // let d = []
  return rooms.reduce((acc, room, idx) => {
    let arr = []
    arr.push(`room${idx+1}Adult=${room.adult}`)
    arr.push(`room${idx+1}Child=${room.child}`)
    if ((room.childAges || []).length > 0) {
      arr.push(`room${idx+1}ChildAge=${(room.childAges || []).join(",")}`)
    }
    return acc.concat(arr)
  }, []).join("&")
}
function decodeRoomPeople(query) {
  // 转成对象
  const params = new URLSearchParams(query);
  const rooms = {};

  for (const [key, value] of params) {
    const match = key.match(/^room(\d+)(Adult|Child|ChildAge)$/);
    if (match) {
      const [, roomIndex, field] = match;
      const index = parseInt(roomIndex, 10);

      if (!rooms[index]) {
        rooms[index] = { adult: 0, child: 0, childAges: [] };
      }

      if (field === "Adult") {
        rooms[index].adult = parseInt(value, 10);
      } else if (field === "Child") {
        rooms[index].child = parseInt(value, 10);
      } else if (field === "ChildAge") {
        rooms[index].childAges = value ? value.split(",").map(Number) : [];
      }
    }
  }
  const finalRooms = Object.keys(rooms)
    .sort((a, b) => a - b)
    .map((k) => rooms[k]);
  // 按 room 顺序输出成数组
  return finalRooms.length > 0 ? finalRooms : [...defaultRooms];
}

function parseBookingQuery(query) {
  const params = new URLSearchParams(query);
  let date = [dayjs().add(30, 'day'), dayjs().add(32, 'day')]
  try {
    date = params.get("date").split(",").map(e => dayjs(e))
  } catch (e) {}
  let rooms = [...defaultRooms]
  try {
    rooms = decodeRoomPeople(query)
  } catch (e) {}
  let newParams = {
    keyword: params.get("keyword") || "",
    date,
    rooms,
    offerType: params.get("offerType") || "all",
    promoCode: params.get("promoCode") || ""
  }
  for (const [key, value] of params) {
    if (!["keyword", "date", "offerType", "promoCode"].includes(key) && !/^room(\d+)(Adult|Child|ChildAge)$/.test(key)) {
      newParams[key] = value
    }
  }
  return newParams
}

function buildBookingQuery(params) {
  let queryArray = []
  const {rooms, date, ...rest} = params
  if (date) {
    queryArray.push(`date=${date.map(e => e.format("YYYY-MM-DD")).join(",")}`)
  }
  if (rooms) {
    queryArray.push(encodeRoomPeople(rooms))
  }
  for (const key in rest) {
    queryArray.push(`${key}=${rest[key] === undefined ? '' : rest[key]}`)
  }
  return queryArray.join("&")
}

function isLogin() {
  return Boolean(getServerConfig("user"))
}

function protectLogin(url) {
  if (!isLogin()) {
    $helper.getGlobalSubject().emit("Trigger_ProtectLogin")
    // window.location.href = `/auth/login?redirect=${encodeURIComponent(url || window.location.href)}`
    return false
  }
  return true
}

const $helper = {
  createReactRootElement, getGlobalSubject, showLoading, hideLoading, showModal,
  showMessage, hash, getServerConfig, formatUsername, clone, loginProtect,
  verifyCaptcha, isJsonString, isAntdGhaPopoverRootVisible,
  encodeSearchHash, decodeSearchHash, isLaravelLocal, getUrlDateAndRoomsFromHash,
  hexToRgba, formatPrice, getUrlHashParams, decodeRoomPeople, encodeRoomPeople,
  parseBookingQuery, buildBookingQuery, isLogin, protectLogin,
}

window.$helper = $helper

export default $helper