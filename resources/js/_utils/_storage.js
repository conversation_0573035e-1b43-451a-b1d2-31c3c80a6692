class Storage {
  keys = {
    token: '__TOKEN__',
  }
  get(k) {
    return localStorage.getItem(k)
  }
  set(k, v) {
    return localStorage.setItem(k, v)
  }
  remove(k) {
    localStorage.removeItem(k)
  }

  setToken(v) {
    return this.set(this.keys.token, v)
  }

  getToken() {
    return this.get(this.keys.token)
  }

  setRegisterToken(k, v) {
   return localStorage.set(`__REGISTER__${k}`, v)
  }
  getRegisterToken(k) {
    return localStorage.get(`__REGISTER__${k}`)
  }

  removeToken() {
    return this.remove(this.keys.token)
  }
}

const $storage = new Storage()

export default $storage