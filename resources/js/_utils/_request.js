const { Observable, catchError, defer, delay, map, of, concatMap } = window.rxjs
import axios from "axios"
import $storage from "./_storage"

class ApiService {
  buildUrl(options) {
      return `/api/v1${options.url}`
      // return `${options.url}`
  }
  buildHeaders(options) {
    const tokenConfig = $storage.getToken() ? {
      "Authorization": `Bearer ${$storage.getToken()}`
    } : {}
    return {
      "Content-Type": "application/json",
      "lang": window.__currentLang__ || "zh",
      ...tokenConfig,
      ...options.headers,
    }
  }
  request(options) {
    const {url, method, data, headers, params} = options
    return defer(() => axios({
        url: `${window?.__Server_Data__?.host || window.location.origin}${this.buildUrl(options)}`,
        method: method || "GET",
        data: data || {},
        headers: this.buildHeaders(options),
        params: params || {},
        withCredentials: options.withCredentials || false
      }).then(res => res.data)
    ).pipe(
      delay(500),
      map(this.handleResponse),
      // map(this.handle401),
      catchError(this.handleError),
    )
  }

    // 新增的直接请求方法，不经过加工处理
directRequest(options) {
    return defer(() => axios({
            url: `${window.location.origin}${options.url}`,
            method: options.method || "GET",
            data: options.data || {},
            headers: options.headers || {},
            params: options.params || {},
            withCredentials: options.withCredentials || false
        }).then(res => res.data)
    ).pipe(
        catchError(this.handleError)
    );
}

get(options = {}) {
    return this.request({ ...options, method: 'GET', withCredentials: true });
}

post(options = {}) {
    return this.request({ ...options, method: 'POST', withCredentials: true });
}

  handle401(res) {
    if (res.status_code === 401) {
      $storage.removeToken()
      location.href = window.__Server_Data__.homeUrl
    }
    return res
  }
  handleError(error) {
    console.error("123131313", error)
    return of({success: false, message: "网络错误，请重试", "status_code": 500, data: null})
    // return of({status: false, message: error.message || intel.t("system_networkError") || "网络错误，请重试"})
  }
  handleResponse(res) {
    return res
  }

  register(data) {
    return this.request({url: "/api/auth/register", method: "POST", data})
  }



  hook2000() {
    return of(null).pipe(
      delay(2000)
    )
  }
  searchObscure(keyword) {
    return this.get({url: `/hotel/searchObscure?keyword=${keyword}`})
  }
  searchOffers(params) {
    return this.post({url: `/offers/list`, data: {...params, page_size: 10000}}).pipe(
      map(res => {
        // res.data.data = res.data.data.map(e => {
        //   return {...e, "longitude": Math.random() * 10, "latitude": Math.random() * 10}
        // })
        return res
      })
    )
  }
  searchHotels(params) {
    return this.post({url: `/hotel/searchKey`, data: {...params, page_size: 10000}}).pipe(
      map(res => {
        // res.data.data = res.data.data.slice(0, 5)
        return res
        // return {
        //   ...res,
        // }
      })
    )
  }

  collectItem(data) {
    return this.post({url: `/user/add_collect`, data})
  }

  search(type, opts) {
    return of(null).pipe(
      delay(2000),
      map(() => {
        return {
          status: true,
          data: type === "hotels" ? window.$hotels : window.$offers
        }
      })
    )
  }

  authLogout(data) {
      return this.directRequest({
          url: '/logout',
          method: 'POST',
          data,
          withCredentials: true
      });
  }

  authRegister(data) {
      return this.directRequest({
      url: `/auth/register`,
      method: 'POST',
      data,
      withCredentials: true
    })
  }
  authLogin(data) {
      return this.directRequest({
          url: '/auth/loginUser',
          method: 'POST',
          data,
          withCredentials: true
      });
  }

  authActiveAccount(data) {
    return this.post({
      url: `/auth/active`, data: {...data, ghaMarketingYn: true}
    })
  }

  authFindAccount(data) {
    return this.post({
      url: `/auth/find_member`, data
    })
  }

  authSendResetPasswordEmail(data) {
    return this.post({
      url: `/auth/reset_pwd`, data
    })
  }
  authResetPassword(data) {
    return this.post({
      url: `/auth/set_pwd`, data
    })
  }

  getNewsList(source, params) {
    return this.get({url: `/about/${source}`, params: {...params, page_size: 20}})
  }

  userUpPassword(data) {
    return this.post({url: `/up_pwd`, data})
  }
  userGetCollectItems(tags) {
    return this.get({url: `/user/collect`, params: {type: tags.join(",")}})
  }

  getHotelInfo(id) {
    return this.get({url: `/hotel/detail?id=${id}`})
  }
  checkHotelRoomAvailability(data) {
    return this.post({url: `/room/checkAvailability`, data})
  }
  createReservation(data) {
    return this.post({url: `/room/createReservation`, data})
  }
  getReservation(params) {
    return this.get({url: `/room/getReservation`, params})
  }
  cancelReservation(data) {
    return this.post({url: `/room/cancelReservation`, data})
  }
  modifyReservation(data) {
    return this.post({url: `/room/modifyReservation`, data})
  }
  getMyReservations(params) {
    return this.get({url: `/user/reservations`, params})
  }
  getHotelMinPrices(data) {
    return this.post({url: `/room/hotelMinPrices`, data})
  }
  getRoomMinPrices(data) {
    return this.post({url: `/room/roomMinPrices`, data})
  }
}

const $api = new ApiService()

export default $api
