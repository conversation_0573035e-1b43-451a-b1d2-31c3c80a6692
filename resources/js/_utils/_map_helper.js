import $priceService from "./_price_service";
import $helper from "./helper";

class GhaMapHelper {
  formatClusterPoint (e) {
    return e.map(function(e) {
        var t, o = e.id;
        return {
            type: "Feature",
            id: o,
            properties: {
                id: o
            },
            geometry: {
                type: "Point",
                coordinates: [+e.longitude, +e.latitude]
            }
        }
    })
  };

  buildPriceHtml(options) {
    const {hotelData, slug = "hotels", autoTriggerFav = false} = options
    if (slug !== "hotels") return ""
    let price = $priceService.getPrice(hotelData.synxis_hotel_id)
    if (!price) {
      return `
        <div class="!py-3 mx-auto">
          <div class="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i class="iconfont icon-loading text-4xl"></i></div>
        </div>
      `
    }
    if (!price.status || !price.priceInfo || !price.priceInfo.nonMemberPrice) {
      return `
        <div class="text-red-600 flex flex-row items-center">
          <i class="iconfont icon-Shapex mr-2"></i>暂无可订房间
        </div>
      `
    }
    if (price.priceInfo.memberPrice) {
      return `
        <div class="protrude">
          <p>会员价低至</p>
          <p class="price-num">${$helper.formatPrice(price.priceInfo.memberPrice.cnyTotalPrice / 100)}</p>
        </div>
        <div class="">
          <p>会员价低至</p>
          <p class="price-num">${$helper.formatPrice(price.priceInfo.nonMemberPrice.cnyTotalPrice / 100)}</p>
        </div>
      `
    }
    return `
      <div class="">
        <p>起价</p>
        <p class="price-num">${$helper.formatPrice(price.priceInfo.nonMemberPrice.cnyTotalPrice / 100)}</p>
      </div>
    `
  }

  getPopup(options) {
    const {hotelData, slug = "hotels", autoTriggerFav = false} = options
    // console.error("hotelData", hotelData, slug)
    let hotelPriceHtml = this.buildPriceHtml(options)
    const popupOptions = {
      closeButton: false,
      maxWidth: "320px",
      className: "gha-mapbox-hotel-popup-root",
      offset: [0, 25],
      anchor: 'top'
    }
    const hotelImages = (hotelData.images || [hotelData.image]).map(uri => {
      return `<div class="swiper-slide !h-[213px]"><div class="relative w-full h-full"><img width="658" height="428" src="${uri}"/></div></div>`
    }).join('')
    // const logoSvg = hotelData.brand_img || ""
    let logoSvg
    if (slug === "hotels") {
      logoSvg = hotelData.brand_img || ""
    }
    if (slug === "offers") {
      logoSvg = hotelData.brand_img
    }
    logoSvg = logoSvg || "https://admin.dev.ghaloyalty.com/var/site/storage/images/0/8/3/0/90380-3-eng-GB/98b725aa0183-BR_Logo_140.png"
    let local = ""
    let favType = ""
    if (slug === "hotels") {
      local = [hotelData.country, hotelData.city?.[0]?.name].filter(e => e).join("，")
      favType = "hotel"
    }
    if (slug === "offers") {
      local = [hotelData.country_name, hotelData.city_name].filter(e => e).join("，")
      favType = "offer"
    }
    const popupContentHotelInfo = `
      <h5 class="brand"><a href="/about/brands/${hotelData.brand_id}" class="hover:underline">${hotelData.brand_name || "占位brand_name"}</a></h5>
      <h2 class="title"><a href="/hotel/${hotelData.id}" class="hover:underline">${hotelData.hotel_name}</a></h2>
      ${local ? `<h5 class="local">${local}</h5>` : ''}
      <div class="spt-line"></div>
      <div class="price-wrap">
        ${hotelPriceHtml}
      </div>
      
    `

    const popupContentOfferInfo = `
      <h5 class="brand"><a href="/brand/" class="hover:underline">${hotelData.brand_name || "占位brand_name"}</a></h5>
      <h2 class="title"><a href="/offer/${hotelData.id}" class="hover:underline">${hotelData.title}</a></h2>
      <div class="spt-line"></div>
      ${local ? `<h5 class="local">${local}</h5>` : ''}
    `
    const popupContent = `
        <div class="gha-mapbox-hotel-popup hotel-popup-synxis_hotel_id-${hotelData.synxis_hotel_id || 0} hotel-popup-${hotelData.id}">
          <div class="swiper-el gha-swiper">
            <div class="absolute z-20 top-2.5 left-0 w-full">
              <img class="select-none max-w-[25%] max-h-[40%] filter invert ml-2.5" src="${logoSvg}" alt="" />
            </div>
            <div class="absolute z-20 top-2.5 right-2.5 ${autoTriggerFav ? 'trigger-fav-el' : ''}" data-type="${favType}" data-id="${hotelData.id}">
              <i data-id="${hotelData.id}" class="fav-icon cursor-pointer iconfont ${hotelData.is_collect ? 'icon-Heart-filled' : 'icon-Heart'} text-white p-1 text-4xl"></i>
            </div>
            <div class="swiper-pagination px-4"></div>
            <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-a-Arrow-Left"></i></div>
            <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-a-Arrow-Right"></i></div>
            <div class="swiper-container">
              <div class="swiper-wrapper">
                ${hotelImages}
              </div>
            </div>
          </div>
          <div class="hotel-item hotel-item-large">
            <div class="info-wrap">
              ${slug === "hotels" ? popupContentHotelInfo : popupContentOfferInfo}
            </div>
          </div>
        </div>
      `

    return new mapboxgl.Popup(popupOptions).setHTML(popupContent);
  }

  initPopupSwiper(hotelData) {
    setTimeout(() => {
      new Swiper(`.hotel-popup-${hotelData.id} .swiper-el .swiper-container`, {
        pagination: {
          el: `.hotel-popup-${hotelData.id} .swiper-pagination`,
          clickable: true,
        },
        navigation: {
          nextEl: `.hotel-popup-${hotelData.id} .gha-swiper-button-next`,
          prevEl: `.hotel-popup-${hotelData.id} .gha-swiper-button-prev`,
        },
      });
    }, 200);
  }

  fitBoundsMap(map, points) {
    // const points = dataSourceRef.current.map(mark => [+mark.longitude, +mark.latitude])
    let bounds = new mapboxgl.LngLatBounds();

    // 遍历所有点，并将它们添加到 bounds 中
    points.forEach(function(point) {
        bounds.extend(point);
    });

    // 使用 fitBounds 方法调整视图
    map.fitBounds(bounds, {
        padding: 100,
        maxZoom: 12
    });
  }

  flyMarkerToCenter(map, coordinates) {
    map.flyTo({
      center: coordinates,
      offset: [0, -200],
      duration: 300,
      curve: 0
    })
  }
}
const $ghaMapHelper = new GhaMapHelper()
export default $ghaMapHelper
