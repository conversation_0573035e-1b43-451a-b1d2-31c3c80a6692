import { useEffect, useMemo, useState } from "react"
import { $helper, $priceService, $http } from "@/_utils/_index"
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"
import LoadingInf from "@/_components/LoadingInf"

export default function HotelItem({ hotel, className, onFav }) {
  const [price, setPrice] = useState(() => {
    return $priceService.getPrice(hotel.synxis_hotel_id)
  })
  useEffect(() => {
    $priceService.on(`${$priceService.keys.ON_HOTEL_PRICE_UPDATED}_${hotel.synxis_hotel_id}`, onHotelPriceChange, 'HotelItem')
    return () => {
      $priceService.off(`${$priceService.keys.ON_HOTEL_PRICE_UPDATED}_${hotel.synxis_hotel_id}`, onHotelPriceChange, 'HotelItem')
    }
    
  }, [])
  function onHotelPriceChange(event) {
    if (event.data.hotelID === hotel.synxis_hotel_id) {
      setPrice(event.data.priceData)
    }
  }
  const local = useMemo(() => {
    return [hotel.country, hotel.city?.[0]?.name].filter(e => e).join("，")
  }, [hotel])
  const logoUri = hotel.brand_img || ""
  const state = useGhaSearchBarStore()
  const bookingUrl = useMemo(() => {
    const params = {
      rooms: state.rooms,
      date: state.date,
      hotelID: hotel.id
    }
    return `/booking/select-room?${$helper.buildBookingQuery(params)}`
  }, [state, hotel?.id])
  return (
    <div className={`hotel-item ${className}`}>
      <div className="cover-wrap">
        <img className="bg" src={hotel.image}/>
        <div className="hotel-image-mask"></div>
        <div className="logo-wrap">
          {logoUri && <img src={logoUri} alt="" />}
        </div>
        <div onClick={(e) => {
          e.preventDefault()
          onFav()
        }} className="fav-icon"><i className={`iconfont ${hotel.is_collect ? 'icon-Heart-filled' : 'icon-Heart'} text-white`}></i></div>

      </div>
      <div className="tag-wrap" style={{backgroundColor: hotel.gradient_color}}>
        <p>{hotel.head_line || "Unknown Series"}</p>
      </div>
      <div className="info-wrap">
        <a href={`/about/brands/${hotel.brand_id}`} className="hover:underline">
          <div className="brand">{hotel.brand_name}</div>
        </a>

        <a href={`/hotel/${hotel.id}`} className="hover:underline">
          <h2 className="title">{hotel.hotel_name}</h2>
        </a>

        {local && <h5 className="local">{local}</h5>}
        <div className="spt-line"></div>
        <div className="flex-1"></div>
        <div className="price-wrap">
          {price ? (
            <>
              {(price.status && price.priceInfo) ? (
                <>
                  {price.priceInfo.memberPrice ? (
                    <>
                      <div className="protrude">
                        <p>会员价低至</p>
                        <p className="price-num">{$helper.formatPrice(price.priceInfo.memberPrice.cnyTotalPrice / 100)}</p>
                      </div>
                      <div className="">
                        <p>非会员价</p>
                        <p className="price-num">{$helper.formatPrice(price.priceInfo.nonMemberPrice.cnyTotalPrice / 100)}</p>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="">
                        <p>起价</p>
                        <p className="price-num">{$helper.formatPrice(price.priceInfo.nonMemberPrice.cnyTotalPrice / 100)}</p>
                      </div>
                    </>
                  )}
                  
                </>
              ) : (
                <div className="text-red-600 flex flex-row items-center">
                  <i className="iconfont icon-Shapex mr-2"></i>暂无可订房间
                </div>
              )}
              
            </>
          ) : <LoadingInf className="!py-3 mx-auto"/>}
          
        </div>
        <div className="action-wrap">
          <a href={price?.priceInfo?.nonMemberPrice ? bookingUrl : null} className={`gha-primary-btn ${price?.priceInfo?.nonMemberPrice ? "" : "disabled"}`}>立即预订</a>
          <a href={`/hotel/${hotel.id}`} className="gha-btn">酒店详情</a>
        </div>
      </div>
    </div>

  )
}
