export default function OfferItem({ offer, className, onFav }) {
  return (
    <a href={`/offer/${offer.id}`} className={`hotel-item ${className}`}>
      <div className="cover-wrap">
        <img className="bg" src={offer.image} alt="" />
        <div className="hotel-image-mask"></div>

        <div className="logo-wrap">
          <img src={offer.brand_img || "https://cms.ghadiscovery.com/content/download/281/1261?version=31&inline=1"} alt="" />
        </div>
        <div onClick={(e) => {
          e.preventDefault()
          onFav()
        }} className="fav-icon"><i className={`iconfont ${offer.is_collect ? 'icon-Heart-filled' : 'icon-Heart'} text-white`}></i></div>
      </div>
      <div className="tag-wrap">
        <p>{offer.head_line || "占位"}</p>
      </div>
      <div className="info-wrap">
        <div className="brand">{offer.brand_name || '品牌-占位'}</div>
        <h2 className="title">{offer.title}</h2>
        <div className="spt-line"></div>
        <div className="mt-3">
            <div className="discount-line">
                <i className="iconfont icon-Location"></i>
                {offer.country_name || "国家-占位"}, {offer.city_name || '城市-占位'}
            </div>
            <div className="discount-line">
                <i className="iconfont icon-Calendar"></i>
                预订期限至：{offer.end_date}
            </div>
            <div className="discount-line">
                <i className="iconfont icon-Room"></i>
                住宿期限：{ offer["stay_end_date"] } - { offer["stay_start_date"] }
            </div>
        </div>
      </div>
    </a>
  )
}
