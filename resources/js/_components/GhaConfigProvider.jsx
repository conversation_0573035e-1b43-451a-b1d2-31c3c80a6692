import { ConfigProvider, App } from "antd"
import { useEffect, useState } from "react"

export default function GhaConfigProvider({children}) {
  useEffect(() => {
    
  }, [])
  return (
    <ConfigProvider theme={{
      token: {colorPrimary: "#300B5C"},
      components: {
        Form: {
          itemMarginBottom: 20,
          verticalLabelPadding: "0 0 5px",
        },
      },
    }}>
      <App
        message={{
          top: 120,
        }}
      >{ children }</App>
    </ConfigProvider>
  )
}

export function useAntdMessage() {
  const { message } = App.useApp();
  return message;
}

export function useForceUpdate() {
  const [, updateState] = useState(Math.random());
  return () => updateState(Math.random());
}