import { ConfigProvider, DatePicker } from 'antd'
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { useState } from 'react';
dayjs.locale('zh-cn');
const { RangePicker } = DatePicker;
import { $helper } from "@/_utils/_index"

export default function DateSelect({needConfirm}) {
  const date = useGhaSearchBarStore((state) => state.date);
  function onDateChange(value) {
    useGhaSearchBarStore.setState({ date: value })
    $helper.getGlobalSubject().emit("Booking_Select_Room_Cond_Updated")
  }
  return (
    <div className="date-select search-item flex flex-row items-center relative">
      {/* <i className="iconfont icon-Calendar"></i> */}
      <ConfigProvider 
        locale={locale}
        theme={{
          token: {
            colorPrimary: "#300B5C"
          },
          components: {
            DatePicker: {
              zIndexPopup: 2001
            },
          },
        }}
      >
          <RangePicker 
            variant="borderless" 
            placement="bottomLeft" 
            placeholder={"选择旅行时间"}
            suffixIcon={null}
            allowClear={false}
            prefix={<i className="iconfont icon-Calendar text-3xl"></i>}
            // className="!pl-20"
            inputReadOnly
            onChange={onDateChange}
            classNames={{popup: {root: "gha-popover-root !z-[2000]"}}}
            value={date}
            disabledDate={current => current && current < dayjs().startOf('day')}
            needConfirm={needConfirm}
          />
      </ConfigProvider>
    </div>
  )
}