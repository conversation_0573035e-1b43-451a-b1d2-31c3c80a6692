import { Popover } from "antd"
import React, { useEffect, useState } from "react"
import mockjs from "mockjs"

function getRandomTags(len) {
  return new Array(len).fill(0).map((_, index) => {
    return {
      id: Math.random().toString(36).substr(2, 9),
      name: mockjs.Random.cword(6, 12)
    }
  })
}

export default function DiscountHotelSelect() {
  const [contentWidth, setContentWidth] = useState(0)
  const [open, onOpenChange] = useState(false)
  const types = [
    ...getRandomTags(10)
  ]
  const content = (
    <div className={``} style={{width: `${contentWidth}px`}}>
      {types.map(e => {
        return (
          <div onClick={() => onOpenChange(false)} key={e.id} className="py-2 border-t first:border-t-0 cursor-pointer" >{e.name}</div>
        )
      })}
    </div>
  )
  useEffect(() => {
    const targetElement = document.querySelector('.discount-hotel-select').parentNode;
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const width = entry.contentRect.width;
        setContentWidth(width);
      }
    });
    resizeObserver.observe(targetElement);
  }, [])
  return (
    <Popover
      trigger="click"
      placement="bottom"
      content={content}
      arrow={false}
      open={open}
      onOpenChange={onOpenChange}
      rootClassName="gha-popover-root"
    >
      <div className="discount-hotel-select search-item flex flex-row items-center justify-between">
        <div className="flex flex-row items-center">
          <i className="iconfont icon-Filter"></i>
          <p>大连酒店</p>
        </div>
        <i className="iconfont icon-down !font-20"></i>
      </div>
    </Popover>
    
  )
}