import { Popover } from "antd"
import { useEffect, useState } from "react"
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"


export const GhaOfferTypes = [
  {
    id: "all", name: "所有"},
    ...(window.__ServerVars__?.offerTypeList || [])
  ]
export function getOfferTypeName(id) {
    return GhaOfferTypes.find(e => `${e.id}` === `${id}`)?.name || "所有"
}

export default function DiscountTypeSelect() {
  const offerType = useGhaSearchBarStore(state => state.offerType)
  const [contentWidth, setContentWidth] = useState(0)
  const [open, onOpenChange] = useState(false)
  const [types] = useState([...GhaOfferTypes])
  const content = (
    <div className="">
      <div className={`max-h-[40vh] overflow-x-hidden overflow-y-auto p-3`} style={{width: `${contentWidth}px`}}>
        {types.map(e => {
          return (
            <div onClick={() => {
              useGhaSearchBarStore.setState({offerType: `${e.id}`})
              onOpenChange(false)
            }} key={e.id} className={`py-2 border-t first:border-t-0 cursor-pointer ${offerType === `${e.id}` ? 'text-primary' : ''}`} >{e.name}</div>
          )
        })}
      </div>
    </div>
    
  )
  useEffect(() => {
    const targetElement = document.querySelector('.discount-type-select').parentNode;
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const width = entry.contentRect.width;
        setContentWidth(width);
      }
    });
    resizeObserver.observe(targetElement);
  }, [])
  return (
    <Popover 
      trigger="click"
      placement="bottom"
      content={content}
      arrow={false}
      open={open}
      onOpenChange={onOpenChange}
      rootClassName="gha-popover-root gha-popover-root-no-padding z-[1999]"
      z-index={1999}
      autoAdjustOverflow={false}
    >
      <div className="discount-type-select search-item flex flex-row items-center justify-between pl-1 cursor-pointer">
        <p className="">{getOfferTypeName(offerType)}</p>
        <i className="iconfont icon-down"></i>
      </div>
    </Popover>
  )
}