import { useMemo, useState } from "react";
import { Popover } from "antd";
import PeopleSelectPopover from "./PeopleSelectPopover";
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"

export function formatPeople(rooms) {
    const adults = rooms.reduce((acc, cur) => acc + cur.adult, 0);
    const children = rooms.reduce((acc, cur) => acc + cur.child, 0);
    return `${adults}成人 ${children > 0 ? `${children}儿童` : ""} ${rooms.length}房间`;
}

export default function PeopleSelect({needConfirm}) {
    const rooms = useGhaSearchBarStore(state => state.rooms);
    const [open, setOpen] = useState(false)
    const formattedPeople = useMemo(() => {
        return formatPeople(rooms);
    }, [rooms]);
    function onConfirm(_rooms) {
        if (needConfirm) {
            setOpen(false)
        }
        useGhaSearchBarStore.setState({rooms: _rooms})
        $helper.getGlobalSubject().emit("Booking_Select_Room_Cond_Updated")
    }
    return (
        <Popover
            content={
                <PeopleSelectPopover
                    rooms={rooms}
                    onConfirm={onConfirm}
                    needConfirm={needConfirm}
                />
            }
            trigger="click"
            arrow={false}
            open={open}
            onOpenChange={setOpen}
            placement="bottomLeft"
            rootClassName="people-select-popover-root gha-popover-root"
            zIndex={2000}
            destroyOnHidden
        >
            <div className="people-select search-item flex flex-row items-center">
                <i className="iconfont icon-Family"></i>
                <a href="javascript:;">{formattedPeople}</a>
            </div>
        </Popover>
    );
}
