import { Input, DatePicker, Confi<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, InputNumber, Select } from "antd";
import { useEffect, useMemo, useState } from "react";

function PeopleSelectItem({type, room, onChange}) {
  const value = room[type]
  const subDisabled = useMemo(() => {
    if (type === "child" && value <= 0) return true
    if (type === "adult" && value <= 1) return true
    return false
  }, [type, value]) 
  const addDisabled = useMemo(() => {
    return value >= 4
  }, [type, value]) 
  function onSub() {
    if (subDisabled) return
    onChange(type, value - 1)
  }
  function onAdd() {
    if (addDisabled) return
    onChange(type, value + 1)
  }
  return (
    <div className="people-item">
      <p>{ type === "child" ? "儿童" : "成人" }</p>
      <div className="flex flex-row items-center">
        <div className={`plus ${subDisabled ? "disable" : ""}`} onClick={onSub}>-</div>
        <span className="num">{value}</span>
        <div className={`plus ${addDisabled ? "disable" : ""}`} onClick={onAdd}>+</div>
      </div>
    </div>
  );
}

function AgeSelect({room, idx, onChange}) {
  return (
    <Select variant="underlined" value={room.childAges[idx]} onChange={e => onChange("age", e, idx)}>
      {new Array(16).fill(0).map((_, _idx) => {
        return <Select.Option key={_idx + 1} value={_idx + 1}>{_idx + 1}</Select.Option>
      })}
    </Select>
  )
}

export default function PeopleSelectPopover({rooms: originRooms, onConfirm, needConfirm}) {
  const [rooms, setRooms] = useState(originRooms)
  useEffect(() => {
    console.error("xxxxxx", rooms)
  }, [])
  function updateRoom(_rooms) {
    setRooms(_rooms)
    if (!needConfirm) {
      onConfirm(_rooms)
    }
  }
  function onRoomChange(roomIdx, type, val, ageIdx) {
    const updateRooms = [...$helper.clone(rooms)];
    if (type === "adult") {
      updateRooms[roomIdx].adult = val;
    }
    if (type === "child") {
      if (val > updateRooms[roomIdx].child) {
        updateRooms[roomIdx].childAges.push(1);
      } else {
        updateRooms[roomIdx].childAges.pop();
      }
      updateRooms[roomIdx].child = val;
    }
    if (type === "age") {
      updateRooms[roomIdx].childAges[ageIdx] = val;
    }
    updateRoom(updateRooms)
  }
  function onRoomAdd() {
    const updateRooms = [...$helper.clone(rooms), { adult: 1, child: 0, childAges: [] }];
    // useGhaSearchBarStore.setState({ rooms: updateRooms });
    updateRoom(updateRooms)
  }
  function onRoomRemove(roomIdx) {
    const updateRooms = [...$helper.clone(rooms)];
    updateRooms.splice(roomIdx, 1);
    // useGhaSearchBarStore.setState({ rooms: updateRooms });
    updateRoom(updateRooms)
  }
  return (
    <div className="people-select-popover">
      <div className="p-2">
        <div className="">
          {rooms.map((room, idx) => {
            return (
              <div key={idx} className="select-item">
                <h3>
                  房间{idx + 1}
                  {rooms.length > 1 && <div className="del-btn" onClick={() => onRoomRemove(idx)}><i className="iconfont icon-delete font-normal"></i></div>}
                </h3>
                <div className="item-list">
                    <PeopleSelectItem type="adult" room={room} onChange={(type, val) => onRoomChange(idx, type, val)} />
                    <PeopleSelectItem type="child" room={room} onChange={(type, val) => onRoomChange(idx, type, val)}/>
                    <div className={`people-item age ${room.childAges.length <= 0 ? "!hidden" : ""}`}>
                      <p>儿童年龄</p>
                      <div className="flex flex-col md:flex-row">
                        {room.childAges.map((_e, _idx) => {
                          return <AgeSelect key={_idx} idx={_idx} room={room} onChange={(type, val, ageIdx) => onRoomChange(idx, type, val, ageIdx)}/>
                        })}
                      </div>
                    </div>
                </div>
              </div>
            )
          })}
          
        </div>
        <div className="text-right mt-4">
          {needConfirm && <a className="gha-btn inline-block !px-8 mr-2" onClick={() => onConfirm([...rooms])}>确认修改</a>}
          <a className="gha-btn inline-block !px-8" onClick={onRoomAdd}>添加房间</a>
        </div>
      </div>
    </div>
  );
}
