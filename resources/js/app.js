import './bootstrap';
import tippy from 'tippy.js';
import 'tippy.js/dist/tippy.css'; // optional for styling
import 'tippy.js/themes/light.css';
import { $helper, $http } from "@/_utils/_index"


import.meta.glob([
  '../images/**',
  '../fonts/**',
]);

class AppController {
  constructor() {
    console.log('AppController initialized');
    this.bootstrap()
  }

  bootstrap() {
    this.initTippyMenu()
    this.initHeaderMenu()
    this.initHeaderScroll()
    this.initStickyObserver()
    this.initBackToTop()
    this.bindEvents()
    this.initItemFav()
  }
  bindEvents() {
    $("body").on("click", ".trigger-back", function() {
      window.history.back();
    })
    $("body").on("click", ".booking-btn", function(event) {
      if ($(this).hasClass("disabled")) {
        event.preventDefault();
      }
    })
  }

  initItemFav() {
    $("body").on("click", ".trigger-fav-el", function(event) {
      event.preventDefault();
      const $el = $(this);
      if (!$helper.protectLogin()) {
        window.modalLoginCallback = () => {
          onTriggerFavElProtected($el)
        }
        return
      }
      onTriggerFavElProtected($el)
    })
    function onTriggerFavElProtected($el) {
      if ($el.hasClass("loading")) return
      $el.addClass("loading");
      const id = $el.data('id');
      const type = $el.data('type');
      if (!id || !type) return;
      $http.collectItem({id, type}).subscribe(res => {
        $el.removeClass("loading");
        if (res.status_code !== 200) {
          $helper.showMessage(res.message)
          return;
        }
        const nextIconName = $el.find("i").hasClass('icon-Heart-filled') ? 'icon-Heart' : 'icon-Heart-filled';
        $el.find("i").removeClass('icon-Heart-filled').removeClass('icon-Heart');
        $el.find("i").addClass(nextIconName);
        $helper.getGlobalSubject().emit("updateItemFav", {id, type, nextState: nextIconName === 'icon-Heart-filled'});
      })
    }
  }

  initBackToTop() {
    window.addEventListener("scroll", function() {
      if ($(window).scrollTop() > $(window).height() - 100) {
        $(".back-top-nav-wrap").addClass("active");
      } else {
        $(".back-top-nav-wrap").removeClass("active");
      }
    })
    $(".back-top-nav-wrap").on("click", function () {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
        // behavior: 'auto',
      });
    })
  }

  initStickyObserver() {
    const stickyElement = document.querySelector('.sticky-element-Observer');
    if (stickyElement == null) return;
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.intersectionRatio < 1) {
          // 元素开始粘住
          stickyElement.parentNode.classList.add('sticky-active');
        } else {
          // 元素不再粘住
          stickyElement.parentNode.classList.remove('sticky-active');
        }
      });
    }, {
      threshold: [1],
      rootMargin: '-1px 0px 0px 0px'
    });

    observer.observe(stickyElement);
  }

  initSearchBarScroll() {
    const element = document.querySelector('.filter-bar-wrap');

    if (element == null) return;

    function checkElementPosition() {
      const distanceFromTop = element.getBoundingClientRect().top;
      
      console.error("distanceFromTopdistanceFromTopdistanceFromTop", distanceFromTop, element.offsetHeight)
      // if (distanceFromTop <= 90) {
      //   element.classList.add('.filter-bar-wrap');
      // } else {
      //   element.classList.remove('fixed-bar');
      // }
      // console.error("element.getBoundingClientRect().top", element.getBoundingClientRect().top)
      // console.log('距离顶部:', distanceFromTop);
      
      // // 你可以在这里添加条件判断
      // if (distanceFromTop < 100) {
      //   console.log('元素接近顶部了!');
      // }
    }

    // 添加滚动事件监听
    window.addEventListener('scroll', checkElementPosition);

    // 初始检查
    checkElementPosition();
  }

  initHeaderScroll() {
    $(window).on("scroll", function() {
      if ($(window).scrollTop() > 200) {
        $(".app-layout-wrap").addClass("scrolled-header")
      } else {
        $(".app-layout-wrap").removeClass("scrolled-header")
      }
    })
  }

  initHeaderMenu() {
    $("body").on("click", ".header-action-group-wrap .menu-wrap.open", function() {
      $(".app-layout-wrap").addClass("menu-open")
      $("body").addClass("overflow-hidden")
    })
    $("body").on("click", ".header-action-group-wrap .menu-wrap.close", function() {
      $(".app-layout-wrap").removeClass("menu-open")
      $("body").removeClass("overflow-hidden")
    })
  }

  initTippyMenu() {
    $(".tippy-drop-menu").each(function(index, element) {
      console.error($(element).data("offsety"))
      tippy($(element).get(0), {
        content: $(element).find(".tippy-drop-menu-content").get(0),
        allowHTML: true,
        theme: $(element).data("theme") || "",
        trigger: 'click',
        arrow: false,
        appendTo: $(element).get(0),
        interactive: true,
        offset: [0, $(element).data("offsety") || 18],
        placement: 'bottom',
        triggerTarget: [$(element).find(".tippy-drop-menu-trigger").get(0)]
      });
    });
    
  }
}

$(function() {
  new AppController()
})