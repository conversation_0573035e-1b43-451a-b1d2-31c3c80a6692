.gha-mapbox {
  .gha-mapbox-loading-el {
    @apply absolute left-0 right-0 bottom-0 top-0 bg-[#ebebeb] z-[1] hidden;
    i {
      @apply text-4xl leading-none;
    }
  }
  &.loading {
    .gha-mapbox-loading-el {
      @apply flex items-center justify-center;
      i {
        animation: loading-inf 1200ms linear infinite;
      }
    }
  }
  
}

.gha-mapbox-marker-invalid {
  @apply bg-primary w-9 h-9 rounded-full flex items-center justify-center text-white font-bold text-lg cursor-pointer;
  i {
    @apply text-base;
  }
}



.gha-mapbox-marker-normal {
  @apply bg-primary rounded-md flex flex-row items-center justify-center text-white font-bold text-lg px-4 py-1;
  p {
    @apply font-Jost-SemiBold leading-none !important;
  }
  i {
    @apply ml-2 text-3xl font-medium cursor-pointer;
  }
}
.gha-mapbox-hotel-popup-root {
  .mapboxgl-popup-content {
    @apply m-0 p-0 shadow-sm shadow-black/25 bg-transparent;
  }
  .mapboxgl-popup-tip {
    @apply hidden;
  }
  .gha-mapbox-hotel-popup {
    @apply w-80 rounded-xl overflow-hidden bg-white;
    .swiper-el {
      @apply overflow-hidden relative;
      &:hover {
        .gha-swiper-button {
          @apply flex;
        }
      }
      .swiper-pagination {
        @apply bottom-1 !important;
        .swiper-pagination-bullet {
          @apply bg-white opacity-100;
          &.swiper-pagination-bullet-active {
            @apply bg-primary;
          }
        }
      }
      .gha-swiper-button {
        @apply w-9 h-9 hidden;
        i {
          @apply font-18;
        }
      }
      .gha-swiper-button-next {
        @apply right-2;
      }
      .gha-swiper-button-prev {
        @apply left-2;
      }
      .swiper-slide {
        img {
          @apply select-none h-full w-full object-cover;
        }
      }
    }
  }
}

.loading-ami {
  animation: loading-inf 1200ms linear infinite;
}

@keyframes loading-inf {
  from {transform: rotate(0deg)}
  to {transform: rotate(360deg)}
}
