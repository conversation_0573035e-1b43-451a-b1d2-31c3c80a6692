@use "mixins.scss" as mixins;

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .last-justify {
    text-align-last: justify;
  }
  .w-1\/10 {
    width: 10%;
  }
  .w-1\/7 {
    width: 14.2857143%
  }
  .p-7\.5 {
    padding-left: 1.875rem;
    padding-right: 1.875rem;
    padding-top: 1.875rem;
    padding-bottom: 1.875rem;
  }
  .py-7\.5 {
    padding-top: 1.875rem;
    padding-bottom: 1.875rem;
  }
  .px-7\.5 {
    padding-left: 1.875rem;
    padding-right: 1.875rem;
  }
  .pl-7\.5 {
    padding-left: 1.875rem;
  }
  .pr-7\.5 {
    padding-right: 1.875rem;
  }
  .pt-7\.5 {
    padding-top: 1.875rem;
  }
  .pb-7\.5 {
    padding-bottom: 1.875rem;
  }
  .m-7\.5 {
    margin-left: 1.875rem;
    margin-right: 1.875rem;
    margin-top: 1.875rem;
    margin-bottom: 1.875rem;
  }
  .my-7\.5 {
    margin-top: 1.875rem;
    margin-bottom: 1.875rem;
  }
  .mx-7\.5 {
    margin-left: 1.875rem;
    margin-right: 1.875rem;
  }
  .ml-7\.5 {
    margin-left: 1.875rem;
  }
  .mr-7\.5 {
    margin-right: 1.875rem;
  }
  .mt-7\.5 {
    margin-top: 1.875rem;
  }
  .mb-7\.5 {
    margin-bottom: 1.875rem;
  }
  .text-primary {
    color: #300B5C;
  }
  .bg-primary {
    background-color: #300B5C;
  }
  .border-primary {
    border-color: #300B5C;
  }
  @for $i from 5 through 100 {
    .text-primary\/#{$i} {
      color: rgba(#300B5C, calc($i / 100));
    }
    .bg-primary\/#{$i} {
      background-color: rgba(#300B5C, calc($i / 100));
    }
    .border-primary\/#{$i} {
      border-color: rgba(#300B5C, calc($i / 100));
    }
  }

  @for $i from 12 through 42 {
    .font-#{$i} {
      @include mixins.font-size(#{$i}px);
    }
  }
}