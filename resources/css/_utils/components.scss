@use "./mixins.scss" as *;

.gha-primary-btn {
  @apply cursor-pointer text-white bg-primary border border-primary !rounded-full !py-2 !px-4 inline-block text-center !shadow-[0px_2px_2px_0px_rgba(48,11,92,0.2)];
  @include font-14;
  @apply hover:text-white;
  &.ant-btn {
    @apply h-auto;
  }
  &:disabled, &.disabled {
    @apply bg-[#300B5C]/20 text-white border-transparent cursor-not-allowed;
  }
  &.medium {
    @apply py-1.5 !important;
  }
}

.gha-btn {
  @apply cursor-pointer text-primary border border-primary !rounded-full !py-2 !px-4 inline-block text-center !shadow-[0px_2px_2px_0px_rgba(48,11,92,0.2)];
  @include font-14;
  &.ant-btn {
    @apply h-auto;
  }
  &.medium {
    @apply py-1.5 !important;
  }
}



.gha-primary-btn-medium {
  @apply text-white bg-primary border border-primary !rounded-full !py-1 !px-4 font-12 inline-block text-center !shadow-[0px_2px_2px_0px_rgba(48,11,92,0.2)];
  @include font-12;
  &.ant-btn {
    @apply h-auto;
  }
}

.gha-btn-medium {
  @apply cursor-pointer text-primary bg-white border border-primary !rounded-full !py-1 !px-4 font-12 inline-block text-center !shadow-[0px_2px_2px_0px_rgba(48,11,92,0.2)];
  @include font-12;
  &.ant-btn {
    @apply h-auto;
  }
}