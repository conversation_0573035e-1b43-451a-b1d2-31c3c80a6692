@use "../_utils/mixins.scss" as mixins;

.register-page-wrap {
  .top-sec {
    @apply h-[230px];
    background: linear-gradient(180deg, #0A1639 0%, #4A5193 35%, #D7AEC6 82.21%, #FFD2BA 100%);
  }
  .page-main-wrap {
    @apply -mt-40;
    h1 {
      @apply text-2xl lg:text-4xl font-bold text-white;
    }
    .page-content-wrap {
      @apply flex flex-col lg:flex-row lg:items-stretch mt-6 lg:mt-10;
      .form-wrap {
        @apply flex-1 rounded-[14px] border border-[#919191]/20 bg-white p-5 py-7 lg:p-10;
        .ant-input, .ant-input-password, .ant-select-selector {
          @apply px-0;
        }
        .ant-input:focus {
          @apply border-b-[#300B5C];
        }
        .tip-wrap {
          @apply mt-1;
          a {
            @apply underline text-[#300B5C];
          }
        }
        .submit-wrap {
          @apply w-full lg:w-44 text-center mt-6;
          .ant-btn {
            @apply shadow-none;
          }
          p {
            @apply mt-4;
          }
        }
      }
      .extra-wrap {
        .extra-list {
          @apply w-full rounded-[14px] bg-white p-7 mt-10 border border-[#919191]/20;
          @apply lg:w-[260px] lg:ml-5 lg:border-0 lg:mt-0;
          box-shadow: 0px 6px 6px 0px #0000000D;
          > div {
            @apply py-5 border-b text-center;
            &:last-child {
              @apply border-b-0 pb-0;
            }
            &:nth-child(2) h2 {
              @apply text-[#300B5C];
            }
            &:nth-child(3) h2 {
              @apply text-[#DA9F59];
            }
            &:nth-child(4) h2 {
              @apply text-[#8BBCD9];
            }
            &:first-child {
              @apply pt-0;
            }
            h2 {
              @apply font-bold;
              @include mixins.font-18;
            }
            h4 {
              @include mixins.font-14;
            }
            p {
              @apply mt-1;
              @include mixins.font-12;
            }
          }
        }
        
        
      }
    }
  }
}