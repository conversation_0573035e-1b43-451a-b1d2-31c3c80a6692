.discount-page-wrap {
  .index-banner {
    background-image: url(../images/discount/index-top-banner.png);
    @apply w-full bg-cover bg-center bg-no-repeat;
    @apply py-32 lg:py-[200px];
  }
  .discount-search-bar {
    @apply shadow-md border-[#efefef] rounded-xl overflow-hidden;
  }

  .fixed-bar, .fixed-bar-m {
    .fixed-holder {
      @apply hidden
    }
    &.gha-fixed {
      .fixed-holder {
        @apply block;
      }
      .bar-el {
        @apply fixed top-[90px] left-0 right-0 z-[10] bg-white shadow-md;
        .inner {
          @apply rounded-none shadow-none;
        }
      }
    }
  }
}

.shadow-bg {
  background-image: url(../images/index/border-b.png);
  background-position: left bottom;
  background-size: 100% auto;
  background-repeat: no-repeat;
}

.anchor-item.active {
  @apply border-b-[3px] border-primary text-primary;
}