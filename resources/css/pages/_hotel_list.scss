.hotel-list-page-wrap {
  &::before {
    @apply content-[''] w-full h-[300px] block absolute top-0 left-0 right-0;
    @apply bg-gradient-to-b from-black/10 to-transparent;
  }
  .top-filter-bar {
    @apply py-4 lg:py-12 border-b border-[#999]/10;
    .home-filter-wrap {
      @apply mt-0;
      .filter-item {
        &.reward {
          @apply hidden;
        }
        &.people {
          @apply w-full border-r-0 lg:flex-1;
        }
        &.date {
          @apply w-full lg:flex-1;
        }
        &.keyword {
          @apply w-full lg:flex-1;
        }
      }
    }
  }
  .tag-filter-wrap {
    @apply border-2 border-[#eeeeee] rounded-xl overflow-hidden shadow-md shadow-[#919191]/10;
    .filter-row {
      @apply flex flex-row items-start justify-stretch border-b last:border-0 bg-[#f0f0f0]/50;
      .filter-row-name {
        @apply text-[16px] w-36 text-center flex-shrink-0 leading-[60px] font-medium;
      }
      .filter-row-tags-list, .virtual {
        @apply flex-1 flex flex-row items-center flex-wrap py-2 -mx-1 px-4 min-h-[60px] bg-white;
        .tag-item {
          @apply bg-[#f6f6f6] text-[13px] mx-1 my-1 py-1 px-2.5 cursor-pointer rounded-md border border-[#f6f6f6];
          &.active {
            @apply border-primary text-primary bg-white;
          }
          &.more {
            @apply text-[#242424]/50;
          }
        }
      }
    }
  }

  .filter-state-wrap {
    @apply flex flex-row items-center justify-between;
    .map-select-wrap {
      @apply ml-2 rounded-full h-8 shadow-md font-12 shadow-[#070102]/10 border border-[#eeeeee] flex flex-row items-center overflow-hidden;
      > div {
        @apply px-3 h-full flex flex-row items-center cursor-pointer;
        i {
          @apply mr-1 font-20;
        }
        &.active {
          @apply bg-primary text-white;
        }
      }
    }
  }

  .mobile-filter-bar {
    .gha-fixed {
      @apply fixed top-[146px] left-0 right-0 z-[100] px-4 bg-white shadow-md border-b;
    }
  }
  
}
.mobile-filter-list {
  .filter-row-tags-list {
    @apply flex-1 flex flex-row items-center flex-wrap py-2 -mx-1 px-4 min-h-[60px] bg-white;
    .tag-item {
      @apply bg-[#f6f6f6] text-[13px] mx-1 my-1 py-1 px-2.5 cursor-pointer rounded-md border border-[#f6f6f6];
      &.active {
        @apply border-primary text-primary bg-white;
      }
      &.more {
        @apply text-[#242424]/50;
      }
    }
  }
}
