@use "../_utils/mixins.scss" as mixins;


.tippy-drop-menu >.tippy-drop-menu-content {
  display: none;
}

@mixin header-action() {
  .header-action-group-wrap {
    @include mixins.font-14;
    @apply flex flex-row items-center leading-none;
    .login-and-lang {
      @apply flex flex-row items-center;
    }
    .login-wrap {
      @apply hidden lg:flex flex-row items-center;
      a {
        @apply px-2.5 relative block;
        &:nth-child(2) {
          @apply pr-0;
          &::before{
            @apply content-[''] absolute top-1/2 -translate-y-1/2 left-0 w-[2px] h-[12px] bg-[#cbcbcb]/80;
          }
        }
      }
    }
    .login-wrap-m {
      @apply lg:hidden mx-5 px-6 py-2 border border-[#300b5c] text-[#300b5c] text-sm rounded-full;
    }
    .lang-wrap {
      @apply mx-5 hidden lg:block;
      .lang-item {
        @apply flex flex-row items-center;
        span {
          @apply ml-1.5;
        }
        i {
          @apply ml-1.5 text-xs;
        }
      }
      .tippy-drop-menu-content {
        @apply px-2 py-3;
        .lang-item {
          @apply mt-3.5;
          &:nth-child(1) {
            @apply mt-0;
          }
        }
      }
    }

    .menu-wrap {
      &.open {
        @apply block;
      }
      &.close {
        @apply hidden;
      }
      i {
        // @apply text-3xl;
      }
    }
  }
}

.app-layout-wrap:not(.transparent-header) {
  @apply pt-[90px];
}

.transparent-header {
  &:not(.menu-open):not(.scrolled-header) {
    &::before {
      @apply content-[''] fixed left-0 right-0 top-0 h-[90px] block z-[98];
      @apply bg-gradient-to-b from-black/30 to-transparent;
    }
    .g-header-wrap {
      @apply bg-transparent shadow-none;
      .logo {
        @apply hidden;
      }
      .logo-white {
        @apply block;
      }
      .header-action-group-wrap {
        @apply text-white;
      }
      .login-wrap-m {
        @apply border-white text-white;
      }
      
    }
  }
  
}

.menu-open {
  @apply overflow-hidden;
  .g-header-wrap {
    .logo, .logo-white {
      @apply hidden lg:block;
    }
    .header-action-group-wrap .login-and-lang {
      @apply flex-row-reverse lg:flex-row;
      .lang-wrap {
        @apply block mr-0 lg:mr-5;
      }
    }
  }
  
  .g-menu-drawer-wrap {
    @apply translate-y-0 lg:translate-x-0;
  }
  .g-header-wrap .header-action-group-wrap .menu-wrap.open {
    @apply hidden;
  }
  .g-header-wrap .header-action-group-wrap .menu-wrap.close {
    @apply block;
  }
}
.g-header-wrap {
  @apply fixed top-0 left-0 right-0 h-[90px] px-4 md:px-16 flex items-center z-[1999] bg-white;
  @apply shadow-[0px_4px_4px_0px_rgba(0,0,0,0.05)] transition-all duration-300 ease-in-out;
  // box-shadow: 0px 4px 4px 0px #0000001A;
  .logo, .logo-white {
    img {
      @apply w-32 lg:w-40 filter;
    }
  }
  .logo-white {
    @apply hidden;
  }
  @include header-action;
}

.g-menu-drawer-wrap {
  @apply w-full lg:w-[490px] lg:right-0 bottom-0 fixed top-[90px] bg-[#fefefe] transition-all duration-300 z-[1998] overflow-x-hidden overflow-y-auto;
  @apply -translate-y-[100vh] lg:translate-x-[490px] lg:translate-y-0;
  box-shadow: 0px 3px 0px 0px rgba(5, 2, 2, 0.06);
  @include header-action;
  .header-action-group-wrap {
    @apply justify-end pt-11;
  }

  .drawer-content {
    @apply px-16 flex flex-col h-full;
  }

  .nav-wrap {
    @apply py-20 lg:mt-20 lg:py-0 font-bold text-xl tracking-wider;
    .nav-item {
      @apply mt-4 text-center lg:text-left;
      &:nth-child(1) {
        @apply mt-0;
      }
    }
  }

  .join-btn {
    @apply text-center lg:text-left lg:mt-12;
    a {
      @apply bg-[#da9f59] text-sm text-white h-11 px-8 inline-flex items-center justify-center rounded-full;
      box-shadow: 0px 0px 9px 4px rgba(#da9f59, 0.36);
    }
  }

  .link-wrap {
    @apply pb-14 text-[#999] lg:text-[#333];
    @include mixins.font-14;
    > div {
      @apply flex flex-row items-center mt-2.5;
      a {
        @apply relative px-3 leading-none block;
        &::after {
          @apply content-[''] absolute top-1/2 right-0 h-[80%] w-0.5 bg-[#8c8c8c]/30 -translate-y-1/2;
        }
        &:nth-last-child(1)::after {
          @apply hidden;
        }
      }
    }
  }
}