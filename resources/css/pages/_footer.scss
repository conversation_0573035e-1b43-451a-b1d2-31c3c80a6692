@use "../_utils/mixins.scss" as mixins;


.g-footer-wrap {
  @apply py-8 md:py-20 border-t border-t-[#0c0509]/10;
  .footer-content {
    @apply md:flex md:flex-row md:justify-between;
    .footer-navs {
      @apply flex flex-col md:flex-row md:-mx-7;
      .nav-wrap {
        @apply mt-5 md:mt-0 md:mx-7;
        &:nth-child(1) {
          @apply mt-0;
        }
        h5 {
          @apply text-base  font-medium;
        }
        ul {
          @apply mt-2 md:mt-4;
          li {
            @apply mt-1;
            &:nth-child(1) {
              @apply mt-0;
            }
          }
        }
        a {
          @apply text-sm text-black/70;
        }
      }
    }
    .footer-medias {
      @apply mt-5 md:mt-0;
      h5 {
        @apply text-base  font-medium;
      }
      ul {
        @apply flex flex-row -mx-2 mt-4 md:mt-10;
        li {
          @apply mx-2;
          a {
            @apply w-14 h-14 flex items-center justify-center rounded-full text-[#525252];
            box-shadow: 0px 1px 8px rgba(84,58,168,.248);
            i {
              @apply text-3xl;
            }
          }
          
        }
      }
      
    }
  }
}