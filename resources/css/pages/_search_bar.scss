.gha-search-bar-wrap {
  // @apply flex;
  @apply font-14;
  @apply lg:font-16;
  .ant-input, .ant-picker {
    @apply px-0;
    @apply lg:font-16;
    input {
      @apply lg:font-16;
    }
  }
  i {
    @apply text-3xl mr-1;
  }
  .search-item {
    @apply h-full;
  }
  .right-split {
    @apply relative;
    &::after {
      @apply content-[''] block absolute top-1/2 -translate-y-1/2 h-4 w-px bg-gray-200 right-0;
    }
  }

  &.gha-fixed {
    .fixed-content {
      @apply lg:fixed lg:left-0 lg:right-0 lg:top-[90px] z-[1998] lg:border-b lg:shadow-md;
    }
    .fixed-holder {
      @apply lg:block;
    }
    .fixed-mobile {
      @apply block lg:hidden;
    }
  }

  
  .fixed-mobile {
    @apply hidden;
  }
  .fixed-holder {
    @apply hidden;
  }
  .fixed-sentinel {
    @apply h-0;
  }

  .discount-type-select-popover {
    @apply w-full;
  }
}