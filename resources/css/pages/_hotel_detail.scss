.hotel-detail-page-wrap {
  
  .top-banner {
    @apply pt-40 pb-12 bg-cover bg-center bg-no-repeat relative;
    &::before {
      @apply content-[''] w-full h-[300px] block absolute top-0 left-0 right-0;
      @apply bg-gradient-to-b from-black/10 to-transparent;
    }
    .info {
      @apply relative;
      .logo {
        @apply w-20 h-20 rounded-full bg-white overflow-hidden flex items-center justify-center;
        img {
          @apply max-w-[80%] max-h-[80%];
        }
      }
      h1 {
        @apply font-30 font-medium text-white py-2;
      }
      .tags {
        @apply flex flex-row -mx-1;
        > div {
          @apply mx-1 rounded-full text-white border border-white px-4 py-1 font-12;
        }
      }
    }
    .back {
      @apply absolute left-0 right-0 bottom-14 z-20 text-white font-14;
      a {
        @apply flex flex-row items-center;
      }
    }
  }

  .section-title {
    @apply font-24 font-medium text-primary;
  }
  .facility-block {
    &:not(.show-more) {
      .facility-list {
        > div {
          @apply max-lg:[&:nth-child(n+5)]:hidden;
          @apply lg:[&:nth-child(n+8)]:hidden;
        }
      }
    }
  }

  .hotel-pic-block {
    .thumbs {
      @apply flex flex-row justify-stretch -mx-1;
      .thumb-big {
        @apply mx-1 md:w-1/2 lg:w-2/5;
          > div {
            @apply w-full pb-[63%] bg-center bg-cover bg-no-repeat rounded-lg overflow-hidden relative;
            img {
              @apply absolute left-0 top-0 w-full h-full object-cover;
            }
          }
      }
      .thumb-oth {
        @apply md:w-1/2 lg:w-3/5 mx-1 overflow-hidden relative;
        .inner {
          @apply flex flex-row flex-wrap h-full -m-1;
          > div {
            @apply md:w-1/2 lg:w-1/3 px-1 my-1;
            height: calc(100% / 2 - 4px);
            > div {
              @apply h-full w-full bg-slate-300 bg-center bg-cover bg-no-repeat rounded-lg overflow-hidden relative;
              img {
                @apply absolute left-0 top-0 w-full h-full object-cover;
              }
            }
            
          }
        }
      }
    }
    .thumb-swiper {
      @apply relative;
      .gha-swiper-button {
        @apply top-1/2 -translate-y-1/2;
      }
      .gha-swiper-button-prev {
        @apply left-4;
      }
      .gha-swiper-button-next {
        @apply right-4;
      }
      .swiper-pagination {
        @apply -bottom-8;
      }
    }
  }

  .recommend-swiper {
    .swiper-container {
      @apply overflow-hidden;
    }
  }

  .fixed-nav-bar {
    .fixed-holder {
      @apply hidden;
    }
    &.gha-fixed {
      .fixed-holder {
        @apply block;
      }
      .bar-el {
        // @apply fixed top-[146px] lg:top-[168px] left-0 right-0 z-20 bg-white shadow-md border-t;
        @apply fixed top-[90px] left-0 right-0 z-20 bg-white shadow-md border-t;
      }
    }
  }

  .anchor-panel-flag {
    // @apply absolute -top-[195px] lg:-top-[217px];
    @apply absolute -top-[140px];
  }
}

.gha-modal-wrap {
  @apply fixed top-0 left-0 right-0 bottom-0 bg-black/50 bg-white z-[9999];
  .close {
    @apply shadow-md shadow-black/20 bg-white rounded-full w-10 h-10 flex items-center justify-center cursor-pointer;
    i {
      @apply text-3xl text-primary;
    }
  }
}

.glary-item.active {
  @apply border-primary text-primary;
}

.thumbs-swiper-bottom .swiper-slide {
  @apply opacity-60 border-2 border-transparent cursor-pointer;
  &.active {
    @apply opacity-100 border-primary cursor-auto;
  }
}
.thumbs-swiper-m {
  &.gha-swiper .gha-swiper-button {
    @apply static translate-y-0;
  }
}
