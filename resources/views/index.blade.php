@extends('layouts.main-layout', ["layoutRootClassName" => "transparent-header"])
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
@endsection

@section('content')
    <div class="home-banner-wrap">
        <div class="swiper">
            <div class="swiper-wrapper">
                @foreach ($banners as $item)
                <div class="swiper-slide">
                    <div class="swiper-item" style="background-image: url({{ $item['img_url'] }})">
                        <div class="item-content">
                            <h2>{{ $item["title"] }}</h2>
{{--                            <h4>{{ $item["description"] }}</h4>--}}
                            <a href="{{$item['url']}}">{{ $item["button_text"] }}</a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    <div class="home-filter-wrap">
        <div class="g-main-content-sm">
            <div class="inner">
                <div class="tab-wrap">
                    <div class="tab-list active-1">
                        <div class="item"><p>精选酒店</p></div>
                        <div class="item"><p>本地生活</p></div>
                        <div class="item"><p>体验</p></div>
                    </div>
                    <div class="tip">
                        <p>GHA代表着45个品牌，850多家酒店，遍布 100多个国家，服务会员3000万</p>
                    </div>
                </div>
                <div class="index-search-bar-wrap"></div>
                <div class="filter-bar-wrap-holder !hidden">
                    <div class="filter-bar-wrap">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="home-section-wrap">
        <div class="g-main-content">
            <div class="title-wrap">
                <h1>GHA探索之旅</h1>
                <h2>助您随心所欲享受旅行</h2>
                <h5>全球800多家酒店住宿及超值体验任您选择，回馈丰厚的奢华酒店忠诚计划，一切为您而生！</h5>
            </div>
            <div class="desc-list-wrap">
                @foreach ($tansuo as $item)
                <div class="desc-item">
                    <div class="cover" style="background-image: url({{ $item['img_url'] }})"></div>
                    <div class="info">
                        <h3>{{$item['name']}}</h3>
                        <p>{{$item['description']}}</p>
                        <div class="text-center">
                            <a href="{{$item['url']}}">{{$item['button_text']}}</a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    <div class="home-section-wrap account-section-wrap compact">
        <div class="g-main-content">
            <div class="title-wrap">
                <h1>会员尊享</h1>
                <h2>多重优惠活动</h2>
                <h5>总有一款符合您需求的套餐或乐享超值优惠，利用“多住多省”优惠，节省更多！ 查看优惠，尽情旅行！</h5>
            </div>
        </div>
        <div class="swiper swiper-lg hidden lg:block">
            <div class="swiper-wrapper">
                @foreach ($huiyuan as $item)
                <div class="swiper-slide">
{{--                     <div class="swiper-item" style="background-image: url({{ $item['image_url'] }})">--}}
                    <div class="swiper-item gha-bg-test" style="">
                        <p>{{$item['name']}}</p>
                    </div>
                </div>
                @endforeach
            </div>
            <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-left"></i></div>
            <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-right"></i></div>
            @foreach ($huiyuan as $i => $item)
            <div class="active-info-wrap idx-{{ $i }} {{ $i == 0 ? '' : 'hidden' }}">
                <h3>{{ $item['name'] }}</h3>
                <h5>{{ $item['sub_name'] }}</h5>
                <p>{{ $item['description'] }}</p>
                <a href="{{ $item['url'] }}">{{ $item['button_text'] }}<i class="iconfont icon-a-Arrow-Right"></i></a>
            </div>
            @endforeach
        </div>

        <div class="relative">
            <div class="gha-swiper">
                <div class="swiper-pagination account-swiper-m"></div>
            </div>

            <div class="swiper swiper-m block lg:hidden">
                <div class="swiper-wrapper">
                    @foreach ($huiyuan as $item)
                    <div class="swiper-slide">
                        {{-- <div class="swiper-item" style="background-image: url({{ $item['image_url'] }})"> --}}
                        <div class="swiper-item" style="">
                            <p>{{$item['name']}}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

        </div>


    </div>
    <div class="home-section-wrap compact">
        <div class="g-main-content">
            <div class="title-wrap">
                <h1>全新探索</h1>
                <h2>只为满足您的一切旅行需求</h2>
                <h5>总有一款符合您需求的套餐或乐享超值优惠，利用“多住多省”优惠，节省更多！ 查看优惠，尽情旅行！</h5>
            </div>
            <div class="desc-list-wrap explore">
                @foreach ($quanxin as $item)
                <div class="desc-item">
                    <div class="cover" style="background-image: url({{ $item['img_url'] }})"></div>
                    <div class="extra-info">
                        <h3>{{$item['hotal_name']}}</h3>
                        <h5><i class="iconfont icon-Location"></i>{{$item['address']}}</h5>
                        <p>{{$item['description']}}<a href="{{$item['url']}}">详情</a></p>
                    </div>
                    <div class="info">
                        <h3>{{$item['name']}}</h3>
                        <div class="text-center">
                            <a href="{{$item['url']}}">{{$item['button_text']}}</a>
                        </div>
                    </div>
                </div>
                @endforeach

            </div>
        </div>
    </div>
    <div class="home-link-wrap">
        <div class="g-main-content relative">
            <div class="content">
                <div class="text-wrap">
                    <h2>诚邀您探索全球精选目的地，留下难忘回忆</h2>
                    <p>畅享全球系列酒店品牌，规划并预订您的度假之旅</p>
                </div>
                <div class="code-wrap">
                    <div class="item">
                        <div class="code"  style="background-image: url({{$configs['mini_qrcode']}})"></div>
                        <p>微信扫码收藏小程序</p>
                        <p>获取更高效便捷的预订体验</p>
                    </div>
                    <div class="item">
                        <div class="code"  style="background-image: url({{$configs['public_qrcode']}})"></div>
                        <p>微信扫码关注我们</p>
                        <p>了解更多酒店资讯</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection


@section('scripts')
    <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
    @vite("resources/js/pages/index/index.js")
    @viteReactRefresh
    @vite("resources/js/pages/index/jsx/index-filter/main.jsx")
@endsection
