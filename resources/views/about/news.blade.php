@extends('layouts.main-layout', ["layoutRootClassName" => "transparent-header", "hideFooterBrands" => true])
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
@endsection

@section('content')
  @php
    $module_name = "新闻动态";
    $module_name_short = "新闻";
    if ($source == "wechat_list") {
      $module_name = "微信精选";
      $module_name_short = "微信精选";
    }
  @endphp
  <div class="about-gradient-top relative">
    <div class="absolute bottom-20 md:bottom-14 left-0 right-0">
      <div class="g-main-content-sm text-center md:text-left">
        <h1 class="text-white font-36 font-semibold">{{ $module_name }}</h1>
      </div> 
    </div>
  </div>
  @include('partials.about.about-nav')
  <div class="relative pt-12 pb-10 md:pb-20">
    <div class="g-main-content-sm">
      <div class="flex flex-row items-center justify-between">
        <h5 class="font-22 font-bold font-Jost-SemiBold">{{ $year }}年{{ $module_name_short }}</h5>
        <div class="tippy-drop-menu" data-theme="light" data-offsety="8">
          <div class="tippy-drop-menu-trigger bg-white cursor-pointer rounded-full flex flex-row items-center border border-[#eeeeee] shadow-md px-5 py-2">
            <p class="font-14 mr-5 text-[#696969] !leading-none">选择年份</p>
            <i class="iconfont icon-xialakuang-xiala font-12 !leading-none"></i>
          </div>
          <div class="tippy-drop-menu-content px-4 py-3">
            @foreach($yearsList as $item)
            <a href="{{ route('about.news') }}?year={{$item}}" class="mt-3.5 first:mt-0 block font-14 cursor-pointer year-select-item">
              <span>{{ $item }}</span>
            </a>
            @endforeach
        </div>
        </div>
        
      </div>
      @if(count($news) > 0)
      <div class="news-list mt-4">
        @foreach($news as $article)
          <a href="{{ $article['url'] }}" target="{{ $source == 'wechat_list' ? '_blank' : '' }}" class="news-item">
            <div class="info">
              <h1>{{ $article['title'] }}</h1>
              <p>{{ $article['msg'] }}</p>
              <div class="extra">
                <span>{{ $article['author'] }}</span>
                <span>{{ $article['created_at'] }}</span>
              </div>
            </div>
            @if($article['image'])
            <div class="thumb" style="background-image: url({{ $article['image'] }})"></div>
            @endif
          </a>
        @endforeach
        
      </div>
      @else
      <div class="text-center mt-12">暂无数据</div>
      @endif
      @if(count($news) < $total)
      <div class="text-center mt-12 news-load-more">
        <a href="javascript:;" class="gha-btn  inline-block w-40">显示更多</a>
      </div>
      @endif
      <div class="py-8 loading-el hidden ">
        <div class="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i class="iconfont icon-loading text-4xl"></i></div>
      </div>
    </div>
    
  </div>

@endsection


@section('scripts')
  <script>var source = '{{ $source }}'; var newsTotal = {{ $total }}; var page = 1; var curCount = {{ count($news) }}; var curYear = '{{ $year }}';</script>
  <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
  @vite("resources/js/pages/about/about-index.js")
@endsection