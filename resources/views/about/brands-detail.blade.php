@extends('layouts.main-layout', ["layoutRootClassName" => "transparent-header", "hideFooterBrands" => true])
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
@endsection

@section('content')
  <div class="about-gradient-top relative">
    <div class="absolute bottom-20 md:bottom-14 left-0 right-0">
      <div class="g-main-content-sm text-center md:text-left">
        <h1 class="text-white font-36 font-semibold">精选品牌</h1>
      </div>
    </div>
  </div>
  @include('partials.about.about-nav')
  <div class="py-8 md:py-20 relative">
    <div class="g-main-content-sm">
        <div class="flex flex-col-reverse md:flex-row justify-stretch bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="md:flex-1">
                <div class="p-8 py-10 text-center md:text-left">
                    <p class="">
                        <img class="max-w-32 max-h-32 inline-block" src="{{$brand['logo_svg']}}" alt="">
                    </p>
                    <h1 class="font-28 font-medium mt-8">{{$brand['name']}}</h1>
                    <h2 class="font-IvyMode-Reg font-20">{{$brand['name_en']}}</h2>
                    <div class="mt-8">
                        <a href="{{ route('search.index', ['slug' => 'hotels']) }}?keyword={{$brand['name']}}&brandID={{$brand['id']}}" class="gha-primary-btn inline-block !px-9">查找酒店</a>
                    </div>
                </div>

            </div>
            <div class="md:flex-1 h-56 md:h-auto bg-no-repeat bg-cover bg-center gha-bg-test" style="background-image: url({{ $brand['image'] ?? Vite::asset('resources/images/default-img.jpg') }})"></div>
        </div>
    </div>
  </div>
  <div class="border-b-2 border-black/10 overflow-hidden">
    <div class="g-main-content-sm -mb-0.5">
        <div class="flex flex-row -mx-8">
            <div class="brand-desc-tab-item mx-8"><a href="javascript:;" class="block py-3  active">品牌详情</a></div>
            <div class="brand-desc-tab-item mx-8"><a href="javascript:;" class="block py-3 ">酒店分布</a></div>
            <div class="brand-desc-tab-item mx-8"><a href="javascript:;" class="block py-3 ">特别亮点</a></div>
        </div>
    </div>
  </div>
  <div class="border-b-2 py-12 border-black/10">
    <div class="g-main-content-sm">
        <div class="brand-desc-tab-panel">
            <div class="gha-rich-content">
                {{ $brand['description'] }}
            </div>
        </div>
        <div class="brand-desc-tab-panel hidden">
            <div class="gha-rich-content">
                {{ $brand['distribution'] }}
            </div>
        </div>
        <div class="brand-desc-tab-panel hidden">
            <div class="gha-rich-content">
                {{ $brand['highlights'] }}
            </div>
        </div>
    </div>
  </div>

  @if(count($hotels) > 0)
  <div class="py-12 bg-[#efefef]/30 brands-hotel-block overflow-hidden">
    <div class="g-main-content-sm">
        <div class="flex flex-row items-center justify-between">
            <h2 class="font-24 font-medium">品牌酒店</h2>
            <p class="font-20 font-bold font-IvyMode-Semi-Bd text-[#999999] tracking-widest"><span class="swiper-cur-idx text-[#333333] font-bold font-IvyMode-Semi-Bd">1</span>/{{ count($hotels) }}</p>
        </div>
    </div>
    <div class="mt-6">
        <div class="g-main-content-sm">
            <div class="gha-swiper relative">
                <div class="gha-swiper-button gha-swiper-button-prev hidden xl:flex"><i class="iconfont icon-a-Arrow-Left"></i></div>
                <div class="gha-swiper-button gha-swiper-button-next hidden xl:flex"><i class="iconfont icon-a-Arrow-Right"></i></div>
                <div class="swiper-pagination block xl:hidden"></div>
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        @foreach($hotels as $hotel)
                            <div class="swiper-slide">
                                @include('partials.hotel-item', ['hotel_item' => $hotel])
                            </div>
                        @endforeach
                        <div class="swiper-slide hidden md:block"></div>
                        <div class="swiper-slide hidden lg:block"></div>
                    </div>
                </div>
            </div>
            <div class="text-center lg:text-right mt-16 xl:mt-12">
                <a href="{{ route('search.index', ['slug' => 'hotels']) }}?keyword={{$brand['name']}}&brand_id={{$brand['id']}}" class="gha-primary-btn !py-1.5 !px-8 inline-block">查看全部<i class="iconfont icon-a-Arrow-Right ml-2"></i></a>
            </div>
        </div>
    </div>
  </div>
  @endif

  @if(count($offer) > 0)
  <div class="py-12 brands-discount-block overflow-hidden">
    <div class="g-main-content-sm">
        <div class="flex flex-row items-center justify-between">
            <h2 class="font-24 font-medium">住宿优惠</h2>
            <p class="font-20 font-bold font-IvyMode-Semi-Bd text-[#999999] tracking-widest"><span class="swiper-cur-idx text-[#333333] font-bold font-IvyMode-Semi-Bd">1</span>/{{ count($offer) }}</p>
        </div>
    </div>
    <div class="mt-6">
        <div class="g-main-content-sm">
            <div class="gha-swiper relative">
                <div class="gha-swiper-button gha-swiper-button-prev hidden xl:flex"><i class="iconfont icon-a-Arrow-Left"></i></div>
                <div class="gha-swiper-button gha-swiper-button-next hidden xl:flex"><i class="iconfont icon-a-Arrow-Right"></i></div>
                <div class="swiper-pagination block xl:hidden"></div>
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        @foreach($offer as $offer_item)
                            <div class="swiper-slide">
                                @include('partials.hotel-offer-item', ['offer_item' => $offer_item])
                            </div>
                        @endforeach
                        <div class="swiper-slide hidden md:block"></div>
                        <div class="swiper-slide hidden lg:block"></div>
                    </div>
                </div>
            </div>
            <div class="text-center lg:text-right mt-16 xl:mt-12">
                <a href="{{ route('search.index', ['slug' => 'offers']) }}?keyword={{$brand['name']}}&brand_id={{$brand['id']}}" class="gha-primary-btn !py-1.5 !px-8 inline-block">查看全部<i class="iconfont icon-a-Arrow-Right ml-2"></i></a>
            </div>
        </div>
    </div>
  </div>
  @endif
@endsection


@section('scripts')
  <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
  @vite("resources/js/pages/about/about-brand-detail.js")
@endsection
