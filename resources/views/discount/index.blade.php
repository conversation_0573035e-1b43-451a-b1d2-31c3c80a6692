@extends('layouts.main-layout', ["layoutRootClassName" => "transparent-header"])
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
@endsection

@section('content')
  <div class="discount-page-wrap">
    <div class="index-banner">
      <div class="g-main-content-sm text-center lg:text-left">
        <div class="font-bold font-36 text-white inline-block mx-auto">
          <h1 class="last-justify">会员</h1>
          <h1>尊享优惠</h1>
        </div>
      </div>
    </div>
    <div class="-mt-8">
      <div class="g-main-content-sm">
        <div class="discount-search-bar"></div>
      </div>
    </div>
    <div class="py-12">
      <div class="g-main-content">
        <div class="flex flex-col md:flex-row items-center justify-between font-14">
          <p class="text-center">浏览下列选项或从我们的系列品牌中搜索您喜爱的酒店，看看您可获享哪些优惠</p>
          <a href="{{ route('search.index', ['slug' => 'offers']) }}" class="gha-primary-btn mt-2 md:mt-0">查看全部优惠<i class="ml-1 iconfont icon-a-Arrow-Right"></i></a>
        </div>
      </div>
    </div>
    <div class="">
      <div class="g-main-content">
        <div class="flex flex-row flex-wrap -mx-3 -mt-6">
          @foreach($type as $item)
          <a href="{{ route('search.index', ['slug' => 'offers']) }}?offerType={{$item['id']}}" class="w-full md:w-1/2 lg:w-1/3 mt-6">
            <div class="px-3">
              <div class="">
                <div class="w-full pb-[64%] relative rounded-t-xl bg-cover bg-center bg-no-repeat">
                  <img src="{{ $item['image'] }}" class="absolute inset-0 w-full h-full object-cover" alt="">
                </div>
                <div class="px-4 py-6 text-center relative rounded-b-xl border border-[#E0E0E0]/20 border-t-0">
                  <div class="h-12 shadow-bg absolute -bottom-4 left-8 right-8"></div>
                  <div class="relative">
                    <h3 class="font-18 font-semibold">{{ $item['name'] }}</h3>
                    <p class="font-14 text-[#999] mt-1.5">{{ $item['description'] }}</p>
                  </div>
                </div>
              </div>
            </div>
          </a>
          @endforeach
        </div>
      </div>
    </div>
  </div>
@endsection


@section('scripts')
  <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
  <script>
    window.__ServerVars__ = {
      offerTypeList: @json($type)
    }
  </script>
  @vite("resources/js/pages/discount/index/search-bar.jsx")
@endsection
