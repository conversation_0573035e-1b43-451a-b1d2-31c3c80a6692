@extends('layouts.main-layout', ["layoutRootClassName" => "transparent-header"])
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
@endsection

@section('content')
  <div class="about-banner relative flex items-center justify-center">
    <div class="g-main-content-sm text-center md:text-left">
      <img class="w-64  md:w-auto inline-block" src="{{ Vite::asset('resources/images/about/banner-text.png') }}" alt="">
    </div>
  </div>
  @include('partials.about.about-nav')

  <div class="py-8 md:py-32 about-mask">
    <div class="g-main-content-sm text-center font-14 leading-normal">
      <p>GHA 是一系列独立酒店品牌的集合，并为其提供独特的多品牌忠诚计划  GHA 探索之旅。 </p>
      <p class="mt-1">GHA  旗下系列 45 个品牌， 850 家酒店，遍布 100 个国家，为超过 3000 万名会员。 </p>
      <p class="mt-1">这意味着从适合蜜月、情侣出行的标志性的欧洲宫殿、马尔代夫水上别墅、野奢度假和豪华水疗中心酒店，到位于各大全球枢纽中心的商务友好型酒店，这里可以满足您的各种旅行需求。</p>
    </div>
  </div>
  <div class="gha-divider hidden md:flex">
    <span class="font-16 font-bold md:font-24">我们的品牌</span>
  </div>
  <div class="g-main-content-sm md:hidden">
    <div class="gha-divider">
      <span class="font-16 font-bold md:font-24">我们的品牌</span>
    </div>
  </div>

  <div class="brands-swiper gha-swiper py-6 md:py-24">
    <div class="g-main-content-sm">
      @php
        $allItems = range(1, 50); // 模拟50个数据
        $chunks = array_chunk($allItems, 21); // 每21个一组
        $slides = count($chunks);
      @endphp
      <div class="brand-group brand-list-toggle brand-group-m">
        @foreach(range(1, 50) as $idx)
          <div class="">
            <a href="" class="opacity-80 hover:opacity-100">
              <img src="https://cms.ghadiscovery.com/content/download/455/2072?version=13&inline=1" alt="">
            </a>
          </div>
        @endforeach
      </div>
      <div class="text-center md:hidden">
        <a href="javascript:;" class="show-more-brand-btn gha-btn">展开查看更多</a>
      </div>

      <div class="relative hidden md:block lg-swiper-el">
        <div class="gha-swiper-button gha-swiper-button-prev hidden xl:flex"><i class="iconfont icon-a-Arrow-Left"></i></div>
        <div class="gha-swiper-button gha-swiper-button-next hidden xl:flex"><i class="iconfont icon-a-Arrow-Right"></i></div>
        <div class="swiper-pagination block xl:hidden"></div>
        <div class="swiper-container overflow-hidden">
          <div class="swiper-wrapper">
            @foreach(range(0, $slides - 1) as $idx)
            <div class="swiper-slide">
              <div class="brand-group">
                @foreach($chunks[$idx] as $item)
                  <div class="">
                    <a href="" class="opacity-80 hover:opacity-100">
                      <img src="https://cms.ghadiscovery.com/content/download/455/2072?version=13&inline=1" alt="">
                    </a>
                  </div>
                @endforeach
              </div>
            </div>
            @endforeach
          </div>
        </div>
      </div>
    </div>

  </div>
  <div class="py-8 md:py-20">
    <div class="g-main-content-sm">
      <div class="oth-block">
        <div class="cover"></div>
        <div class="desc">
          <h2>会员奖励计划</h2>
          <p>
            GHA 探索之旅于 2010 年推出，是一个连接 GHA全球酒店联盟独立品牌和特色酒店的会员奖励计划。 会员无论在本地或他乡，都能享受 VIP 认可、周到的福利和丰厚的奖励。 <br/>
            近期该奖励计划进一步升级，为会员提供一个额外的等级和灵活的升级途径，使得会员可以通过房晚/住宿、合格购买或入住品牌数量，更容易、更快地达到精英身份。
          </p>
          <div class="text-right mt-4 lg:mt-8">
            <a href="{{ route('membership.rights') }}" class="gha-primary-btn w-full md:w-auto">了解更多</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="py-8 md:py-20 bg-[#f5f5f5]">
    <div class="g-main-content-sm">
      <div class="oth-block reverse">
        <div class="cover"></div>
        <div class="desc">
          <h2>赚取DISCOVERY奖励金（D$）</h2>
          <p>
            与其他传统会员计划不同，GHA探索之旅不要求会员通过累积积分升级。 相反，会员可赚取及消费DISCOVERY奖励金（一种独家回馈货币，简称D$）， 享受由各酒店策划的仅向会员提供的特色体验，并通过会员专属的本地生活优惠，无需入住即可造访家附近的酒店。透过其“地道本地生活”概念，该计划创建了会员社群，并提供使用当地酒店设施及参与其活动的机会。
          </p>
          <div class="text-right mt-4 lg:mt-8">
            <a href="{{ route('membership.d-plan') }}" class="gha-primary-btn w-full md:w-auto">了解更多</a>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection


@section('scripts')
  <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
  @vite("resources/js/pages/about/about-index.js")
@endsection
