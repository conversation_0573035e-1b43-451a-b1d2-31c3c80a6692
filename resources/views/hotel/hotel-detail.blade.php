@extends('layouts.main-layout', ["layoutRootClassName" => "transparent-header"])
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
    <link rel="stylesheet" href="https://thenextravelmarket.com/plugins/mapbox-gl-js/v2.14.1/mapbox-gl.css">
@endsection


@section('content')
  <div class="hotel-detail-page-wrap overflow-x-hidden">
    <div class="top-banner" style="background-image: url({{ $hotel['image'] }})">
        <div class="absolute top-0 left-0 right-0 bottom-0" style="background-image: linear-gradient(90deg, {{ hexToRgba($hotel['gradient_color'] ?? '#300B5C', 1) }} 50%, {{ hexToRgba('#000000', 0) }} 80%);"></div>
        <div class="info relative">
            <div class="g-main-content-sm">
                <div class="logo">
                    <img src="{{ $hotel['brand_img'] }}" alt="">
                </div>
                <h1>{{ $hotel['hotel_name'] }}</h1>
                @if(count($interest) > 0)
                <div class="tags">
                    @foreach($interest as $tag)
                    <div class="">{{ $tag['name'] }}</div>
                    @endforeach
                </div>
                @endif
            </div>
        </div>
        <div class="mt-16 relative text-white font-14">
            <div class="g-main-content-sm">
                <a href="javascript:;" class="flex flex-row items-center trigger-back"><i class="iconfont icon-left font-18"></i>返回</a>
            </div>
        </div>
    </div>
    <div class="-mt-[39px] relative">
        <div class="g-main-content-sm">
            <div class="shadow-md border border-[#ebebeb]/20 rounded-xl overflow-hidden">
                <div class="search-bar"></div>
            </div>
        </div>
    </div>

    <div class="border-b-2 border-black/10 mt-10">
        <div class="fixed-nav-bar">
            <div class="fixed-sentinel-top"></div>
            <div class="fixed-holder"></div>
            <div class="bar-el">
                <div class="g-main-content-sm">
                    @php
                        // $anchors = ["酒店概况", "酒店图片", "房型介绍", "住宿优惠", "本地生活优惠", "特色体验", "餐饮与康体"];
                        // $anchors = ["酒店概况", "酒店图片", "房型介绍", "住宿优惠", "餐饮与康体"];
                        $anchors = ["酒店概况", "酒店图片"];

                        if (!empty($room) && count($room) > 0) {
                            $anchors[] = "房型介绍";
                        }

                        if (!empty($offer) && count($offer) > 0) {
                            $anchors[] = "住宿优惠";
                        }

                        if (!empty($dining) && count($dining) > 0) {
                            $anchors[] = "餐饮与康体";
                        }
                    @endphp
                    <div class="flex flex-row whitespace-nowrap overflow-x-auto -mx-2 lg:-mx-8">
                        @foreach($anchors as $i => $anchor)
                        <div class="brand-desc-tab-item mx-2 lg:mx-8"><a href="#anchor{{$i}}" class="block py-3">{{ $anchor }}</a></div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="pt-12">
        <div class="g-main-content-sm">
            <div class="bg-[#bbb0dd]/80 rounded-xl text-white font-12 flex flex-row items-center px-8 py-3">
                <i class="iconfont icon-a-FrontDesk font-40 mr-2"></i>
                <div>
                    为符合条件的会员和另一位入住同一房间的客人提供免费早餐，提升您的住宿体验。直接预订时，探索之旅Titanium会员专享。<br/><a href="" class="underline">条款和条件</a>适用。
                </div>
            </div>
        </div>
    </div>
    <div class="py-12 relative">
        <div class="anchor-panel-flag" id="anchor0"></div>
        <div class="g-main-content-sm">
            <div class="flex flex-row items-center justify-between">
                <h2 class="section-title">酒店概况</h2>
                <div class="flex flex-row items-center">
                    <div class="flex flex-row items-center font-13 text-primary">
                        <i class="iconfont icon-maisui-you [transform:rotateY(180deg)]"></i>
                        荣获“2019 年世界水疗大奖”
                        <i class="iconfont icon-maisui-you"></i>
                    </div>
                    <a href="javascript:;" class="px-2 trigger-fav-el" data-type="hotel" data-id="{{ $hotel['id'] }}"><i class="iconfont {{ $hotel['is_collect'] ? 'icon-Heart-filled' : 'icon-Heart' }} font-28 font-bold"></i></a>
                </div>
            </div>
            <div class="font-15 mt-2">
                {!! $hotel['hotel_desc'] !!}
            </div>
            <div class="mt-4 flex flex-row items-center">
                <img class="rounded-xl w-20 h-20" src="{{ Vite::asset("resources/images/hotel/map-icon.png") }}" alt="">
                <div class="flex-1 m-4">
                    <p class="font-13">{{ $hotel["address"] }}</p>
                    <p class="font-12 text-black/80">{{ $hotel["postal_code"] }}</p>
                    <div class="mt-2">
                        <a id="toggleMapBtn" href="javascript:;" class="underline text-primary font-13 ">查看地图</a>
                    </div>
                </div>
            </div>
            <div class="mt-4 facility-block @if(count($roms) <= 10)show-more @endif">
                <div class="gha-divider"><p class="px-8">酒店设施</p></div>
                <div class="flex flex-row flex-wrap mx-0 lg:-mx-12 overflow-hidden py-6 facility-list">
                    @foreach($roms as $fact)
                    <div class="w-1/4 lg:w-1/7 flex flex-col items-center my-2">
                        <div class="w-12 h-12 shrink-0 flex items-center justify-center rounded-full shadow-[0px_2px_4px_2px_rgba(0,0,0,0.05)]">
                            <img src="{{ $fact["image"] }}" alt="">
                            {{-- <i class="iconfont icon-Experiences font-28 text-primary"></i> --}}
                        </div>
                        <p class="mt-2 font-13">{{ $fact["name"] }}</p>
                    </div>
                    @endforeach
                </div>
                @if(count($roms) > 10)
                <div class="text-center">
                    <a href="javascript:;" class="gha-btn facility-toggle-btn">查看所有设施</a>
                </div>
                @endif
            </div>
        </div>
    </div>
    <div class="py-12 relative">
        <div class="anchor-panel-flag" id="anchor1"></div>
        <div class="g-main-content-sm">
            <h2 class="section-title">酒店图片</h2>
        </div>

        <div class="hotel-pic-block py-6">
            <div class="thumb-swiper block md:hidden">
                <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-a-Arrow-Left"></i></div>
                <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-a-Arrow-Right"></i></div>
                <div class="swiper-pagination block xl:hidden"></div>
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        @foreach(array_slice($hotel['images'], 0, 7) as $item)
                        <div class="swiper-slide">
                            <img class="object-cover w-full h-full" src="{{ $item }}" alt="">
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="thumbs hidden md:block">
                <div class="thumb-big">
                    <div >
                        <img  src="{{ $hotel['images'][0] }}" alt="" loading="lazy">
                    </div>
                </div>
                <div class="thumb-oth">
                    <div class="inner">
                        @foreach(array_slice($hotel['images'], 1, 6) as $item)
                            <div><div><img src="{{ $item }}" alt="" loading="lazy"></div></div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        <div class="text-center mt-6 md:mt-0">
            <a id="toggleThumbsBtn" href="javascript:;" class="gha-btn">查看所有图片</a>
        </div>
    </div>
    @if(count($room) > 0)
    <div class="pt-12 pb-20 bg-[#efefef]/30 brands-hotel-block relative">
        <div class="anchor-panel-flag" id="anchor2"></div>
        <div class="g-main-content-sm">
            <div class="flex flex-row items-center justify-between">
                <h2 class="section-title">房型介绍</h2>
                <p class="font-20 font-bold font-IvyMode-Semi-Bd text-[#999999] tracking-widest"><span class="swiper-cur-idx text-[#333333] font-bold font-IvyMode-Semi-Bd">1</span>/{{ count($room) }}</p>
            </div>
        </div>
        <div class="mt-6">
            <div class="g-main-content-sm">
                <div class="gha-swiper relative">
                    <div class="gha-swiper-button gha-swiper-button-prev hidden xl:flex"><i class="iconfont icon-a-Arrow-Left"></i></div>
                    <div class="gha-swiper-button gha-swiper-button-next hidden xl:flex"><i class="iconfont icon-a-Arrow-Right"></i></div>
                    <div class="swiper-pagination block xl:hidden"></div>
                    <div class="swiper-container">
                        <div class="swiper-wrapper">
                            @foreach($room as $item)
                                <div class="swiper-slide">
                                    @include('partials.hotel-room-item', ['room_item' => $item])
                                </div>
                            @endforeach
                            <div class="swiper-slide hidden md:block"></div>
                            <div class="swiper-slide hidden lg:block"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
    @if(count($offer) > 0)
    <div class="pt-12 pb-20 brands-discount-block relative">
        <div class="anchor-panel-flag" id="anchor3"></div>
        <div class="g-main-content-sm">
            <div class="flex flex-row items-center justify-between">
                <h2 class="section-title">住宿优惠</h2>
                <p class="font-20 font-bold font-IvyMode-Semi-Bd text-[#999999] tracking-widest"><span class="swiper-cur-idx text-[#333333] font-bold font-IvyMode-Semi-Bd">1</span>/{{ count($offer) }}</p>
            </div>
        </div>
        <div class="mt-6">
            <div class="g-main-content-sm">
                <div class="gha-swiper relative">
                    <div class="gha-swiper-button gha-swiper-button-prev hidden xl:flex"><i class="iconfont icon-a-Arrow-Left"></i></div>
                    <div class="gha-swiper-button gha-swiper-button-next hidden xl:flex"><i class="iconfont icon-a-Arrow-Right"></i></div>
                    <div class="swiper-pagination block xl:hidden"></div>
                    <div class="swiper-container">
                        <div class="swiper-wrapper">
                            @foreach($offer as $item)
                                <div class="swiper-slide">
                                    @include('partials.hotel-offer-item', ['offer_item' => $item])
                                </div>
                            @endforeach
                            <div class="swiper-slide hidden md:block"></div>
                            <div class="swiper-slide hidden lg:block"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
    @if(count($dining) > 0)
    <div class="pt-12 pb-20 bg-[#efefef]/30 hotel-activity-block relative">
        <div class="anchor-panel-flag" id="anchor4"></div>
        <div class="g-main-content-sm">
            <div class="flex flex-row items-center justify-between">
                <h2 class="section-title">餐饮与康体</h2>
                <p class="font-20 font-bold font-IvyMode-Semi-Bd text-[#999999] tracking-widest"><span class="swiper-cur-idx text-[#333333] font-bold font-IvyMode-Semi-Bd">1</span>/{{ count($dining) }}</p>
            </div>
        </div>
        <div class="mt-6">
            <div class="g-main-content-sm">
                <div class="gha-swiper relative">
                    <div class="gha-swiper-button gha-swiper-button-prev hidden xl:flex"><i class="iconfont icon-a-Arrow-Left"></i></div>
                    <div class="gha-swiper-button gha-swiper-button-next hidden xl:flex"><i class="iconfont icon-a-Arrow-Right"></i></div>
                    <div class="swiper-pagination block xl:hidden"></div>
                    <div class="swiper-container">
                        <div class="swiper-wrapper">

                            @foreach($dining as $item)
                                <div class="swiper-slide">
                                    @include('partials.hotel-activity-item', ['activity_item' => $item])
                                </div>
                            @endforeach
                            <div class="swiper-slide hidden md:block"></div>
                            <div class="swiper-slide hidden lg:block"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
    @if(count($other) > 0)
    <div class="pt-12 pb-20">
        <div class="g-main-content-sm">
            <div class="gha-divider"><p class="px-4 text-primary font-medium font-18">其他酒店</p></div>
            <div class="mt-12">
                <div class="gha-swiper recommend-swiper relative">
                    <div class="gha-swiper-button gha-swiper-button-prev hidden xl:flex"><i class="iconfont icon-a-Arrow-Left"></i></div>
                    <div class="gha-swiper-button gha-swiper-button-next hidden xl:flex"><i class="iconfont icon-a-Arrow-Right"></i></div>
                    <div class="swiper-pagination block xl:hidden"></div>
                    <div class="swiper-container">
                        <div class="swiper-wrapper">
                            @foreach($other as $item)
                                <div class="swiper-slide">
                                    @include('partials.recommend-hotel-item', ['hotel_item' => $item, "hotelClassName" => "recommend-hotel-item"])
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
  </div>
  <div class="gha-modal-wrap map hidden">
    <div class="flex flex-col h-full">
        <div class="border-b">
            <div class="px-4 lg:px-8">
                <div class="text-center relative">
                    <h2 class="font-24 font-bold py-8">查看地图</h2>
                    <div class="absolute top-1/2 right-0 -translate-y-1/2">
                        <div class="close">
                            <i class="iconfont icon-Close"></i>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="flex-1 relative">
            <div id="modalMap" class="absolute top-0 left-0 w-full h-full gha-mapbox loading">
                <div class="gha-mapbox-loading-el">
                    <i class="iconfont icon-loading"></i>
                </div>
            </div>
        </div>
    </div>
  </div>
  <div class="gha-modal-wrap thumbs hidden">
    <div class="flex flex-col h-full overflow-hidden">
        <div class="border-b">
            <div class="px-4 lg:px-8">
                <div class="text-center relative">
                    <h2 class="font-24 font-bold py-8">{{ $hotel['hotel_name'] }}</h2>
                    <div class="absolute top-1/2 right-0 -translate-y-1/2">
                        <div class="flex flex-row items-center">
                            <a href="javascript:;" class="gha-primary-btn mr-4 hidden lg:block">立即预订</a>
                            <div class="close">
                                <i class="iconfont icon-Close"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex-1 flex flex-col justify-center">
            <div class="flex-1 relative mt-8 hidden md:block">
                <div class="absolute top-0 left-0 lg:left-8 right-0 lg:right-8 bottom-0 overflow-hidden">
                    @foreach($images as $idx => $glary)
                        <div class="thumbs-swiper-top relative w-full h-full gha-swiper thumbs-swiper-{{$idx}} {{ $idx == 0 ? '' : 'hidden' }}">
                            <div class="gha-swiper-button gha-swiper-button-prev !left-0"><i class="iconfont icon-a-Arrow-Left"></i></div>
                            <div class="gha-swiper-button gha-swiper-button-next !right-0"><i class="iconfont icon-a-Arrow-Right"></i></div>
                            <div class="page-count absolute z-10 bottom-0 right-0 bg-primary/80 text-white px-4 py-0.5 rounded-full"><span>1</span>/{{ count($glary['images']) }}</div>
                            <div class="swiper-container h-full">
                                <div class="swiper-wrapper">
                                    @foreach($glary['images'] as $image)
                                        <div class="swiper-slide">
                                            <div class="h-full max-w-max mx-auto rounded-xl overflow-hidden">
                                                <img data-src="{{ $image }}" class="object-center w-full h-full block object-contain" alt="">
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="text-center relative py-4 -mt-24 md:mt-0">
                <div class="inline-flex flex-row items-center -mx-4">
                    @foreach($images as $idx => $glary)
                        <a href="javascript:;" class="glary-item mx-4 py-0.5 border-b-2 border-transparent {{ $idx == 0 ? 'active' : '' }}">{{ $glary['label'] }}</a>
                    @endforeach
                </div>
            </div>
            <div class="mt-4 md:hidden">
                @foreach($images as $idx => $glary)
                    <div class="thumbs-swiper-m relative w-full gha-swiper thumbs-swiper-{{$idx}} {{ $idx == 0 ? '' : 'hidden' }}">
                        <div class="swiper-container">
                            <div class="swiper-wrapper">
                                @foreach($glary['images'] as $image)
                                    <div class="swiper-slide !w-[92.5vw] !aspect-[5/3.38] rounded-xl overflow-hidden">
                                        <div class="h-full max-w-max mx-auto rounded-xl overflow-hidden">
                                            <img data-src="{{ $image }}" class="object-center w-full h-full block object-cover" alt="">
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="flex flex-row items-center justify-center mt-4">
                            <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-a-Arrow-Left"></i></div>
                            <div class="page-count text-[#999] mx-4 font-IvyMode-Semi-Bd font-24"><span class="text-black font-IvyMode-Semi-Bd">1</span>/{{ count($glary['images']) }}</div>
                            <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-a-Arrow-Right"></i></div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="hidden md:block">
                @foreach($images as $idx => $glary)
                    <div class="thumbs-swiper-bottom gha-swiper thumbs-swiper-{{$idx}} {{ $idx == 0 ? '' : 'hidden' }}">
                        <div class="swiper-container">
                            <div class="swiper-wrapper">
                                @foreach($glary['images'] as $image_idx => $image)
                                    <div class="swiper-slide !h-[14vh] !aspect-[13.5/9] !w-auto rounded-xl overflow-hidden {{ $image_idx == 0 ? 'active' : '' }}">
                                        <div class="h-full w-full">
                                            <img data-src="{{ $image }}" class="object-center w-full h-full block object-cover" alt="">
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
  </div>
@endsection


@section('scripts')
  <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
  <script src="https://thenextravelmarket.com/plugins/mapbox-gl-js/v2.14.1/mapbox-gl.js"></script>
  <script src="https://mapbox.dragontrail.com.cn/mapbox-gl-js/plugins/mapbox-gl-language/v1.0.0/mapbox-gl-language.js"></script>
  <script>
    var $hotel = {!! json_encode($hotel) !!};
  </script>
  @vite("resources/js/pages/hotel/hotel-detail/hotel-detail.js")
  @viteReactRefresh
  @vite("resources/js/pages/hotel/hotel-detail/search-bar.jsx")
@endsection
