<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        @yield("meta")
        <title>@yield('title')</title>
        <link rel="shortcut icon" href="{{ asset('favicon.ico') }}"/>
        <script src="{{ asset('vendors/js/da-event.min.js') }}"></script>
        @php
            if (isset($user) && isset($ghaMockMembershipRights)) {
                $userLevel = array_filter($ghaMockMembershipRights, function ($item) use ($user) {
                    return $item['GHA_key'] == $user['member_balance']['membership_level'];
                });
                $userCard = $ghaMockMembershipRights[0];
                if (count($userLevel)) {
                    $userLevel=array_merge($userLevel);
                    $userCard = $userLevel[0];
                }
            }

        @endphp
        @if (isset($userCard))
        <script>
            window.__$userCardLevel__ = "{{ $userCard['key'] }}";
        </script>
        @endif
        <script>
            window.__Global_Subject__ = new DaEvent();
        </script>
        <link rel="stylesheet" href="{{ config('app.iconfont_url') }}">
        @vite("resources/css/app.scss")
        @yield('styles')
        @include('partials.layout-assign', ['layoutName' => "user"])
    </head>
    <body>
        <div class="app-layout-wrap">
            @include('partials.header.header')
            <div class="user-page-wrap">
              <div class="top-linear-gradient h-56 hidden lg:block"></div>
              @include('partials.user.user-nav', ["navMode" => "mobile"])
              <div class="pt-[50px] lg:-mt-40">
                <div class="g-main-content">
                  <h1 class="text-2xl hidden lg:block lg:text-4xl font-bold text-white">GHA全球会员计划</h1>
                  <div class="flex flex-row items-stretch mt-6">
                    @include('partials.user.user-nav', ["navMode" => "pc"])
                    <div class="user-content-wrap">
                        @yield('content')
                    </div>
                  </div>
                </div>
            </div>
            @include('partials.brands')
            @include('partials.footer')
        </div>
        <script src="{{ asset('vendors/js/jquery-2.2.4.min.js') }}"></script>
        <script src="{{ asset('vendors/js/rxjs.umd.min.js') }}"></script>
        @vite("resources/js/app.js")
        @viteReactRefresh
        @vite('resources/js/global/react-modal.jsx')
        @yield("scripts")
    </body>
</html>
