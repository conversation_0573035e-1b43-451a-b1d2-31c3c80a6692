<div data-roomcode="{{ $room_item['roomcode'] }}" class="hotel-item hotel-item-large hotel-room" data-id="{{ $room_item['id'] }}">
    <div class="cover-wrap">
        <div class="cover-swiper ignore-opacity">
            <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-a-Arrow-Left"></i></div>
            <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-a-Arrow-Right"></i></div>
            <div class="swiper-pagination"></div>
            <div class="swiper-pagination-nums"><span class="cur">1</span>/{{ count($room_item['images']) }}</div>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    @foreach($room_item['images'] as $image)
                    <div class="swiper-slide relative">
                        <img class="w-full h-full" src="{{ $image }}" alt="" loading="lazy">
                        <div class="hotel-image-mask"></div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    <div class="tag-wrap">
        <p>{{ $room_item['head_line'] ?? '标签标签标签标签' }}</p>
    </div>
    <div class="info-wrap">
    <h5 class="brand">{{ !empty($room_item["brand_name"]) ? $room_item["brand_name"] : "品牌名" }}</h5>
    <h2 class="title">{{ !empty($room_item["room_name"]) ? $room_item["room_name"] : '房型标题' }}</h2>
    @if($room_item['size'])
    <h5 class="room-area">
        <i class="iconfont icon-mianji"></i>
        {{ $room_item['size'] }}
    </h5>
    @endif
    <div class="spt-line"></div>
    <div class="price-wrap">
        <div class="!py-3 mx-auto">
          <div class="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i class="iconfont icon-loading text-4xl"></i></div>
        </div>
    </div>
    <div class="action-wrap">
        <a href="/booking/select-room?hotelID={{ $hotel['id'] }}&roomCode={{$room_item['roomcode']}}" class="gha-primary-btn booking-btn disabled">立即预订</a>
    </div>
    </div>
</div>