<div data-synxisid="{{$hotel_item['synxis_hotel_id']}}" class="hotel-item hotel-item-large {{ $hotelClassName ?? '' }}">
    <div class="cover-wrap">
        <img class="bg" src="{{ $hotel_item['image'] ?? ''}}" alt="">
        <div class="hotel-image-mask"></div>
        <div class="logo-wrap">
            <img src="{{ $hotel_item['brand_img'] ?? '' }}" alt="" />
        </div>
        <div class="fav-icon trigger-fav-el" data-type="hotel" data-id="{{$hotel_item['id']}}"><i class="iconfont {{$hotel_item['is_collect'] ? 'icon-Heart-filled' : 'icon-Heart'}} text-white"></i></div>
    </div>
    <div class="tag-wrap" style="background-color: '{{ $hotel_item['gradient_color'] ?? ''}}'">
        <p>{{$hotel_item['head_line']??'暂无'}}</p>
    </div>
    <div class="info-wrap">
    <a href="{{ route('about.brands-detail', ['id' => $hotel_item['brand_code']]) }}" class="hover:underline">
        <h5 class="brand">{{ $hotel_item["brand_name"] ?? '占位' }}</h5>
    </a>
    <a href="{{ route('hotel.hotel-detail', ['id' => $hotel_item['id']]) }}" class="hover:underline">
        <h2 class="title">{{ $hotel_item["hotel_name"] ?? '占位' }}</h2>
    </a>

    <h5 class="local">{{ $hotel_item["country"] ?? '占位' }}, {{ $hotel_item["city"] ?? '占位' }}</h5>
    <div class="spt-line"></div>
    <div class="price-wrap">
        <div class="!py-3 mx-auto">
            <div class="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i class="iconfont icon-loading text-4xl"></i></div>
        </div>
    </div>
    <div class="action-wrap">
        <a data-smhotelid="{{ $hotel_item['id'] }}" href="/booking/select-room?hotelID={{ $hotel_item['id'] }}" class="gha-primary-btn booking-btn disabled">立即预订</a>
        <a href="/hotel/{{ $hotel_item['id'] }}" class="gha-btn">酒店详情</a>
    </div>
    </div>
</div>
