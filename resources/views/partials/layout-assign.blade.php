<script>
    window.__Server_Data__ = {
        @if (isset($user))
        @php
            $userLevel = array_filter($ghaMockMembershipRights, function ($item) use ($user) {
                return $item['GHA_key'] == $user['member_balance']['membership_level'];
            });
            $userCard = $ghaMockMembershipRights[0];
            if (count($userLevel)) {
                $userLevel =array_merge($userLevel);
                $userCard = $userLevel[0];
            }
        @endphp
        gha_user: @json($user),
        user_card: @json($userCard),
        @endif
        user: @json($user_info),
        appEnv: "{{ config('app.env') }}",
        defaultImage: "{{ Vite::asset('resources/images/default-img.jpg') }}",
        activeUri: "{{ route('auth.active') }}",
        forgetPwdUri: "{{ route('auth.resetPassword') }}",
        registerUri: "{{ route('auth.register') }}",
    }
    console.error('__Server_Data__', __Server_Data__)
</script>
