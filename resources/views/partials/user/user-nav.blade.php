@php
  $navs = [
    ["url" => "/user/dashboard", "label" => "会籍信息",],
    ["url" => "/user/orders", "label" => "我的预订",],
    ["url" => "/user/favorite", "label" => "我的收藏",],
    ["url" => "/user/profile", "label" => "基本信息",],
    ["url" => "/user/settings", "label" => "喜好设置",],
    ["url" => "/user/reset-password", "label" => "修改密码",],
    // ["url" => "/user/logout", "label" => "退出账号",],
  ];
@endphp
@if($navMode == 'pc')
<div class="user-nav-wrap hidden lg:block lg:w-64 lg:mr-5">
  <div class="shadow-md rounded-xl bg-white pt-7 ">
    <div class="px-7 border-b border-[#919191]/20 pb-2.5">
      <p class="font-14">欢迎你，</p>
      <h4 class="font-18 font-bold mt-2.5">{{ $user['name'] }}</h4>
      <p class="font-14 mt-2.5">会员卡号：<span>{{ $user['member_balance']['membership_card_no'] }}</span></p>
      <p class="font-14 mt-2.5">会员等级：<span>{{ $userCard['title'] }}会员</span></p>
    </div>
    <div class="pt-2.5 px-7 border-b border-[#919191]/20 pb-2.5">
      <p class="font-14">您的奖励金</p>
      <h2 class="font-36 font-bold font-Jost-SemiBold">D${{ $user['member_balance']['gravty_discovery_balance'] }}</h2>
    </div>
    
    <ul class="pt-2.5">
      @foreach($navs as $item)
        <li class="block font-14 font-semibold px-7 last:border-none py-2.5 border-b border-[#919191]/20 @if(request()->is(trim($item['url'], '/'))) bg-primary text-white @endif">
          <a href="{{ $item['url'] }}" class="flex justify-between">{{ $item["label"] }} 
            @if(request()->is(trim($item['url'], '/')))<i class="iconfont icon-a-Arrow-Right"></i>@endif
          </a>
        </li>
      @endforeach
        <li class="block font-14 font-semibold px-7 last:border-none py-2.5 border-b border-[#919191]/20 logout-btn-trigger">
          <a href="javascript:;" class="flex justify-between">退出账号</a>
        </li>
    </ul>
  </div>
</div>
@endif
@if($navMode == 'mobile')
<div class="user-nav-m-wrap lg:hidden">
  <div class="g-main-content">
    <ul>
      @foreach($navs as $item)
        <li class="@if(request()->is(trim($item['url'], '/'))) active @endif">
          <a href="{{ $item['url'] }}" class="flex justify-between">{{ $item["label"] }}</a>
        </li>
      @endforeach
    </ul>
  </div>
</div>
@endif