<div class="about-nav-wrap sticky-element">
  <div class="sticky-element-Observer"></div>
  <div class="g-main-content-sm">
    <ul class="nav-list">
      @php
        $navs = [
          ["url" => "/about/index", "title" => "关于GHA"],
          ["url" => "/about/brands", "title" => "精选品牌"],
          ["url" => "/about/partners", "title" => "合作伙伴"],
          ["url" => "/about/news", "title" => "新闻动态"],
          ["url" => "/about/wechat-news", "title" => "微信精选"],
        ]   
      @endphp
      @foreach($navs as $item)
        <li class="nav-item @if(request()->is(trim($item['url'], '/').'*')) active @endif">
          <div class="">
            <a href="{{ $item['url'] }}" class="">{{ $item['title'] }}</a>
          </div>
        </li>
      @endforeach
    </ul>
  </div>
</div>