@extends('layouts.main-layout')
@section("title", "网站地图")

@section('content')
  <div class="sitemap-page-wrap">
    <div class="py-8 md:py-14">
      <div class="g-main-content">
        <h1 class="text-primary font-24 font-bold">网站地图</h1>
      </div>
    </div>
    <div class="">
      <div class="g-main-content">
        @php
          $sitemapLinks = [
            [
              "title" => "目的地/酒店", 
              "children" => [
                ["name" => "目的地", "url" => route("destination")],
                ["name" => "查询酒店", "url" => route("search.index", ["slug" => "hotels"])],
              ]
            ],
            [
              "title" => "会员计划", 
              "children" => [
                ["name" => "忠诚计划", "url" => route("membership.index")],
                ["name" => "会员礼遇", "url" => route("membership.rights")],
                ["name" => "DISCOVERY奖励金(D$)", "url" => route("membership.d-plan")],
              ]
            ],
            [
              "title" => "优惠活动", 
              "children" => [
                ["name" => "酒店优惠", "url" => route("offer.index")],
              ]
            ],
            [
              "title" => "关于我们", 
              "children" => [
                ["name" => "关于GHA", "url" => route("about.index")],
                ["name" => "精选品牌", "url" => route("about.brands")],
                ["name" => "合作伙伴", "url" => route("about.partners")],
                ["name" => "新闻动态", "url" => route("about.news")],
                ["name" => "微信精选", "url" => route("about.wechat-news")],
              ]
            ],
            [
              "title" => "其他", 
              "children" => [
                ["name" => "条件条款", "url" => route("activity.detail", ['id' => 1])],
                ["name" => "隐私政策", "url" => route("activity.detail", ['id' => 12])],
                ["name" => "Cookie政策", "url" => route("activity.detail", ['id' => 13])],
                ["name" => "网站地图", "url" => route("sitemap")],
              ]
            ],
          ];
        @endphp
        <div class="">
          @foreach($sitemapLinks as $site_link_group)
            <div class="mt-4 first:mt-0">
              <h3 class="font-16 text-primary">{{ $site_link_group['title'] }}</h3>
              <div class="-mx-4 flex flex-row flex-wrap">
                @foreach($site_link_group['children'] as $site_link)
                <div class="w-1/2 md:w-1/3 lg:w-1/4 font-14 mt-1">
                  <div class="px-4"><a href="{{ $site_link['url'] }}" class="hover:underline hover:text-primary">{{ $site_link['name'] }}</a></div>
                </div>
                @endforeach
              </div>
            </div>
          @endforeach
        </div>
      </div>
    </div>
    <div class="mt-8 md:mt-14">
      <div class="g-main-content">

          @foreach($brands as $brand)
              <div class="mt-4 first:mt-0">
                  <a href="{{ route('about.brands-detail', ['id' => $brand['id']]) }}" class="text-primary hover:underline">
                      <h4 class="font-18">{{$brand['name']}}</h4>
                      <h5 class="font-16 font-IvyMode-Reg">{{$brand['name_en']}}</h5>
                  </a>
                  <div class="-mx-4 flex flex-row flex-wrap mt-2">
                      @foreach($brand['hotels'] as $hotel)
                          <div class="w-1/2 md:w-1/3 lg:w-1/4 font-14 mt-2 font-IvyMode-Reg">
                              <div class="px-4">
                                  <a href="{{ route('hotel.hotel-detail', ['id' => $hotel['id']]) }}" class="hover:underline">
                                      <p>{{ $hotel['hotel_name'] }}</p>
                                      <p class="text-[#919191] font-13">{{ $hotel['hotel_name_en'] }}</p>
                                  </a>
                              </div>
                          </div>
                      @endforeach
                  </div>
              </div>
          @endforeach
      </div>
    </div>

  </div>
@endsection
