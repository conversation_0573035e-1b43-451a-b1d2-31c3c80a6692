# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a GHA (Global Hotel Alliance) hotel booking system built with Laravel 11.x, providing comprehensive hotel reservation services with Sabre Channel Connect C1 integration. The system supports hotel search, booking management, loyalty programs, and promotional offers.

## Technology Stack

### Backend
- **PHP 8.2+** - Modern PHP with strict typing
- **Laravel 11.x** - Primary framework
- **MySQL** - Primary database
- **Redis** - Caching and session storage
- **RabbitMQ** - Message queue system
- **Elasticsearch** - Search functionality
- **JWT Auth** - API authentication

### Frontend
- **React 19.x** - Frontend framework
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Ant Design** - UI component library
- **Zustand** - State management

### Key Integrations
- **Sabre Channel Connect C1** - Hotel booking API
- **GXP API** - Additional hotel services
- **Dcat Admin** - Administrative panel

## Development Commands

### Laravel Application
```bash
# Install dependencies
composer install
npm install

# Environment setup
cp .env.example .env
php artisan key:generate

# Database operations
php artisan migrate
php artisan db:seed

# Start development server with all services
composer run dev  # Runs server, queue, logs, and vite concurrently

# Individual services
php artisan serve
php artisan queue:listen --tries=1
php artisan pail --timeout=0
npm run dev

# Frontend build
npm run build

# Testing
php artisan test
php artisan test tests/Feature/SabreReservationSimplifiedTest.php  # Run specific test

# Code formatting
./vendor/bin/pint
```

## Architecture Overview

### API Structure
- **Versioned APIs** - All API routes under `/api/v1/` prefix
- **Service Layer Pattern** - Business logic in dedicated service classes
- **DTO Pattern** - Data Transfer Objects for API request/response handling
- **Middleware Authentication** - Custom Sanctum auth middleware

### Core Services
- **SabreService** - Main Sabre API integration service
- **SabreAvailabilityService** - Hotel availability checking
- **SabreReservationService** - Booking management
- **SabreReservationTransformer** - Data transformation for reservations
- **ElasticsearchService** - Search functionality
- **GxpService** - GXP API integration
- **RabbitMQService** - Message queue handling

### Key Controllers (V1 API)
- **SabreController** - Hotel booking operations
- **HotelController** - Hotel search and information
- **GxpController** - GXP API endpoints
- **Auth/User** - Authentication and user management

### Data Transfer Objects (DTOs)
Located in `app/DTOs/` - Handle structured data transformation:
- **SimplifiedReservationRequestDTO** - Reservation requests
- **SimplifiedGuestDTO** - Guest information
- **SimplifiedPaymentDTO** - Payment data
- **RoomAvailabilityResponseDTO** - Room availability responses

### Configuration Files
- **config/sabre.php** - Comprehensive Sabre API configuration with mappings
- **config/gha.php** - GHA-specific settings
- **config/elasticsearch.php** - Search configuration

### Authentication & Authorization
- **Laravel Sanctum** - API token authentication
- **JWT Auth** - Alternative token system
- **CustomSanctumAuth** - Custom middleware for protected routes
- **WeChat Integration** - WeChat login/registration support

### Hotel Booking Workflow
1. Hotel search via SabreAvailabilityService
2. Room availability checking
3. Price calculation with loyalty discounts
4. Reservation creation through SabreReservationService
5. Data transformation via DTOs
6. Response formatting

### Testing Structure
- **Unit Tests** - `tests/Unit/` for service logic
- **Feature Tests** - `tests/Feature/` for API endpoints
- **Sabre Integration Tests** - Specific tests for hotel booking workflows

### Logging & Monitoring
- **SabreLoggingTrait** - Standardized API logging
- **Laravel Pail** - Real-time log viewing
- **Performance tracking** - Slow query and API call monitoring

## Development Notes

### Sabre API Integration
- Authentication tokens are cached (55-minute TTL)
- Comprehensive logging with performance metrics
- Retry logic for failed requests
- Mapping configurations for loyalty levels, currencies, countries

### Data Flow
1. API requests come through versioned routes (`routes/v1.php`)
2. Controllers validate and delegate to service layer
3. Services handle external API calls (Sabre/GXP)
4. DTOs transform data between internal and external formats
5. Responses formatted and cached where appropriate

### Queue System
- RabbitMQ for background processing
- Queue workers for async operations
- Concurrent development setup via composer script