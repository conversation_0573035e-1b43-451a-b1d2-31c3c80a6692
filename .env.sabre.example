# Sabre Channel Connect C1 API Configuration
# 复制此文件内容到你的 .env 文件中

# Sabre API 认证配置
SABRE_AUTH_URL=https://oscp.stage.ghaloyalty.com/api/v3/auth/token
SABRE_USERNAME=your_username_here
SABRE_PASSWORD=your_password_here
SABRE_API_KEY=your_api_key_here

# Sabre API 基础URL
SABRE_API_URL=https://services-c1.synxis.com

# 默认配置
SABRE_PRIMARY_CHANNEL=SYDC
SABRE_SECONDARY_CHANNEL=DSCVRYLYLTY
SABRE_CHAIN_ID=32446
SABRE_CONTEXT=WBSVC
SABRE_LANGUAGE=zh-CN
SABRE_ENTRY_CHANNEL_CODE=GHA
SABRE_SUB_SOURCE_CODE=GHA
SABRE_MARKET_SOURCE_CODE=GHA
SABRE_LOYALTY_PROGRAM=GHA

# 超时设置（秒）
SABRE_CONNECT_TIMEOUT=10
SABRE_REQUEST_TIMEOUT=30

# 重试配置
SABRE_MAX_RETRY_ATTEMPTS=3
SABRE_RETRY_DELAY=1000

# 缓存配置
SABRE_CACHE_ENABLED=false  # 本地开发建议设为false，生产环境设为true
SABRE_TOKEN_CACHE_TTL=3300  # 令牌缓存时间（秒）
SABRE_AVAILABILITY_CACHE_TTL=300  # 可用性查询缓存时间（秒）
SABRE_HOTEL_DETAILS_CACHE_TTL=3600  # 酒店详情缓存时间（秒）

# 日志配置
SABRE_LOGGING_ENABLED=true
SABRE_LOG_LEVEL=info
SABRE_LOG_CHANNEL=daily
SABRE_LOG_CACHE_HITS=false  # 是否记录缓存命中日志
SABRE_LOG_CACHE_MISSES=false  # 是否记录缓存未命中日志
SABRE_LOG_PERFORMANCE=true  # 是否记录性能指标
SABRE_SLOW_THRESHOLD_MS=5000  # 慢请求阈值（毫秒）
SABRE_SANITIZE_SENSITIVE_DATA=true  # 是否清理敏感数据

# 汇率配置
EXCHANGE_RATE_USD=7.2
EXCHANGE_RATE_EUR=7.8
EXCHANGE_RATE_GBP=9.1
EXCHANGE_RATE_JPY=0.048

# 测试模式
SABRE_TEST_MODE=false
SABRE_MOCK_RESPONSES=false
