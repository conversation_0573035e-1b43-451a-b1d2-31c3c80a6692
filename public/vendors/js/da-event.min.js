!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).DaEvent=t()}(this,function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function t(e,t){return e(t={exports:{}},t.exports),t.exports}var o=e(t(function(e){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.default=e.exports,e.exports.__esModule=!0})),s=e(t(function(e){function s(e,t){for(var o=0;o<t.length;o++){var s=t[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}e.exports=function(e,t,o){return t&&s(e.prototype,t),o&&s(e,o),e},e.exports.default=e.exports,e.exports.__esModule=!0}));return function(){function e(){o(this,e)}return s(e,[{key:"on",value:function(e,t,o){"function"==typeof t?(this._stores=this._stores||{},(this._stores[e]=this._stores[e]||[]).splice(0,0,{cb:t,ctx:o})):console.error("listener must be a function")}},{key:"emit",value:function(e){this._stores=this._stores||{};var t=this._stores[e],o=null;if(t){t=t.slice(0),(o=[].slice.call(arguments,1))[0]={eventCode:e,data:o[0]};for(var s=0,n=t.length;s<n;s++)t[s].cb.apply(t[s].ctx,o)}}},{key:"off",value:function(e,t){if(this._stores=this._stores||{},arguments.length){var o=this._stores[e];if(o)if(1!==arguments.length){for(var s=0,n=o.length;s<n;s++)if(o[s].cb===t){o.splice(s,1);break}}else delete this._stores[e]}else this._stores={}}}]),e}()});
