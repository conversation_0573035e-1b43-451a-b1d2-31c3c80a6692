<?php

use App\Http\Controllers\V1\SabreController;
use App\Http\Middleware\CustomSanctumAuth;
use Illuminate\Support\Facades\Route;

Route::post('brand', 'BaseDataController@brand');
Route::post('hotel', 'BaseDataController@hotel');
Route::post('room', 'BaseDataController@room');
// 酒店配置
Route::post('updateHotelConfig', 'BaseDataController@hotelConfig');
// 酒店品牌同步
Route::post('updateBrandConfig', 'BaseDataController@brandConfig');
// 酒店url生成
Route::post('updateHotelUrl', 'BaseDataController@hotelUrl');

Route::prefix('auth')->namespace('Auth')->group(function () {
    Route::post('/register', 'User@register'); // 注册
    Route::post('/login', 'User@login'); // 登录
    Route::post('/reset_pwd', 'User@resetPwd'); // 发送找回密码邮件
    Route::post('/set_pwd', 'User@setPwd'); // 重置密码
    Route::post('/active', 'User@active'); // 账号激活
    Route::post('/find_member', 'User@findMember'); // 找回会员号
});

// 需要登录的操作
Route::middleware([CustomSanctumAuth::class])->group(function () {
    Route::post('/logout', 'Auth\User@logout'); // 退出登录
    // 个人信息
    Route::get('/user_info', 'Auth\User@userInfo');
    // 修改密码
    Route::post('/up_pwd', 'Auth\User@upPwd');
    // 用户收藏
    Route::get('user/collect', 'Auth\User@collect');
    // 我的订阅
    Route::get('user/reservations', 'RoomController@reservations');
    // 我的预订列表 (GXP服务)
    Route::get('user/bookings', 'Auth\User@getBookings');
    // 喜好设置
    Route::get('user/preferences', 'Auth\User@preferences');
});

// 微信返回
Route::prefix('wechat')->namespace('Auth')->group(function () {
    // 微信登录
    Route::post('/login', 'User@wechatLogin');
    // 微信注册
    Route::post('/register', 'User@wechatRegister');
    // 微信校验绑定
    Route::post('/validate', 'User@wechatValidate');
});

Route::prefix('home')->group(function () {
    Route::get('/index', 'HotelController@index');
});

Route::prefix('user')->namespace('Auth')->group(function () {
    Route::post('/add_collect', 'User@addCollect')->name('user.addCollect');
    Route::post('/upload/image', 'User@uploadImage')->name('user.upload.image');
})->middleware(CustomSanctumAuth::class);

Route::prefix('about')->group(function () {
    // 新闻动态
    Route::get('/news_list', 'AboutController@newsList')->name('about.newsList');
    Route::get('/news_detail', 'AboutController@newsDetail')->name('about.newsDetail');
    // 微信精选
    Route::get('/wechat_list', 'AboutController@wechatList')->name('about.wechatList');
    Route::get('/wechat_detail', 'AboutController@wechatDetail')->name('about.wechatDetail');

});

Route::prefix('offers')->group(function () {
    // 优惠活动
    Route::get('/type_list', 'HotelController@offersTypeList')->name('offers.offersTypeList');
    Route::post('/list', 'HotelController@List')->name('offers.List');
    Route::get('/detail', 'HotelController@offersDetail')->name('offers.detail');
});

Route::group(['prefix' => 'hotel'], function () {
    // 酒店首页
    Route::get('/home', 'HotelController@home');
    Route::get('/detail', 'HotelController@detail');
    // 关键词搜索
    Route::post('/searchKey', 'HotelController@searchKey');
    // 关键词搜索获取国家城市酒店
    Route::get('/searchObscure', 'HotelController@searchObscure');
    // 热门搜索
    Route::get('/searchHot', 'HotelController@searchHot');
    // 搜索活动
    Route::get('/searchActivity', 'HotelController@searchActivity');
    // 预定酒店搜索
    Route::get('/searchHotels', 'HotelController@searchHotels');
});

Route::group(['prefix' => 'room'], function () {
    Route::post('/checkAvailability', 'RoomController@checkAvailability');
    Route::post('/hotelMinPrices', 'RoomController@hotelMinPrices');
    Route::post('/createReservation', 'RoomController@createReservation');
    Route::post('/createBatchReservations', 'RoomController@createBatchReservations');
    Route::get('/getReservation', 'RoomController@getReservation');
    Route::post('/cancelReservation', 'RoomController@cancelReservation');
    Route::post('/modifyReservation', 'RoomController@modifyReservation');
    Route::post('/roomMinPrices', 'RoomController@roomTypesPricing');
});

Route::group(['prefix' => 'brand'], function () {
    // 品牌列表
    Route::get('/brand_list', 'BrandController@lists');
    // 品牌详情
    Route::get('/brand_info', 'BrandController@detail');
});

Route::group(['prefix' => 'banner'], function () {
    // banner列表
    Route::get('/banner_list', 'BannerController@lists');
});

Route::group(['prefix' => 'activity'], function () {
    // 活动
    Route::get('/activity_many_list', 'ActivityController@listsManyType');
    Route::get('/activity_list', 'ActivityController@lists');
    Route::get('/activity_info', 'ActivityController@detail');
});

// 基础配置
Route::group(['prefix' => 'config'], function () {
    // 国家
    Route::get('/country_list', 'ConfigController@countryList');
    // 城市
    Route::get('/city_list', 'ConfigController@cityList');
    // 酒店配置
    Route::get('/hotel_config', 'ConfigController@hotelConfig');
    // 喜好设置
    Route::get('/interests_config', 'ConfigController@interestsConfig');
});

// 酒店配置
Route::post('updateHotelConfig', 'BaseDataController@hotelConfig');
// 酒店品牌同步
Route::post('updateBrandConfig', 'BaseDataController@brandConfig');

// GXP API 路由
Route::group(['prefix' => 'gxp'], function () {
    // 访客令牌（无需认证）
    Route::post('/auth/token', 'GxpController@getGuestToken');

    Route::get('/ddollars/exchange-rate', 'GxpController@getCurrencyConversionRate');

    // 预订管理
    Route::post('/reservations-repo', 'GxpController@reserveRepoPosting');
    Route::get('/reservations-repo', 'GxpController@getMyBookings');

    // 查询酒店可用性
    Route::post('/checkAvailability', 'SabreController@checkAvailability');
});
Route::prefix('sabre')->group(function () {
    Route::post('/availability', [SabreController::class, 'checkAvailability']);
    Route::post('/multipleHotelsBestPrices', [SabreController::class, 'checkMultipleHotelsBestPrices']);
    Route::post('/hotelLowestMemberAndNonMemberPrices', [SabreController::class, 'checkHotelLowestMemberAndNonMemberPrices']);
    Route::post('/roomTypesPricing', [SabreController::class, 'getRoomTypesPricing']);
    Route::post('/createReservation', [SabreController::class, 'createReservation']);
    Route::post('/createBatchReservations', [SabreController::class, 'createBatchReservations']);
    Route::post('/processBatchReservations', [SabreController::class, 'processBatchReservations']);
    Route::get('/getReservation', [SabreController::class, 'getReservation']);
    Route::post('/modifyReservation', [SabreController::class, 'modifyReservation']);
    Route::post('/modifyReservationWithRoomChanges', [SabreController::class, 'modifyReservationWithRoomChanges']);
    Route::post('/cancelReservation', [SabreController::class, 'cancelReservation']);

    // 统一的预订处理接口（支持create/modify/cancel）
    Route::post('/reservation', [SabreController::class, 'processReservation']);
});
